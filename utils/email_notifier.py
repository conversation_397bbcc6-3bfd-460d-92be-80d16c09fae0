import smtplib
import json
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from typing import List, Dict
from .config import Config

class EmailNotifier:
    """Email notification system for security alerts"""
    
    def __init__(self):
        self.smtp_server = Config.SMTP_SERVER
        self.smtp_port = Config.SMTP_PORT
        self.username = Config.EMAIL_USERNAME
        self.password = Config.EMAIL_PASSWORD
        self.admin_email = Config.ADMIN_EMAIL
    
    def send_alert(self, alert_type: str, alert_data: Dict, user_email: str = None):
        """Send security alert email"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['Subject'] = f"🚨 Azure Security Alert: {alert_type}"
            
            # Recipients: admin + affected user (if available)
            recipients = [self.admin_email]
            if user_email and user_email != self.admin_email:
                recipients.append(user_email)
            
            msg['To'] = ', '.join(recipients)
            
            # Create email body
            body = self._create_alert_body(alert_type, alert_data)
            msg.attach(MIMEText(body, 'html'))
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            print(f"✅ Alert email sent for {alert_type}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to send email alert: {e}")
            return False
    
    def _create_alert_body(self, alert_type: str, alert_data: Dict) -> str:
        """Create HTML email body for alert"""
        timestamp = alert_data.get('timestamp', datetime.utcnow().isoformat())
        
        html_body = f"""
        <html>
        <body>
            <h2 style="color: #d73027;">🚨 Azure Security Alert</h2>
            <h3>Alert Type: {alert_type}</h3>
            <p><strong>Timestamp:</strong> {timestamp}</p>
            
            <h4>Alert Details:</h4>
            <table border="1" style="border-collapse: collapse; width: 100%;">
        """
        
        # Add alert-specific details
        for key, value in alert_data.items():
            if key != 'timestamp':
                html_body += f"<tr><td><strong>{key.replace('_', ' ').title()}</strong></td><td>{value}</td></tr>"
        
        html_body += """
            </table>
            
            <h4>Recommended Actions:</h4>
            <ul>
        """
        
        # Add recommendations based on alert type
        recommendations = self._get_recommendations(alert_type)
        for rec in recommendations:
            html_body += f"<li>{rec}</li>"
        
        html_body += """
            </ul>
            
            <p style="color: #666; font-size: 12px;">
                This alert was generated by Azure Sentinel Lite.<br>
                Please investigate this activity immediately.
            </p>
        </body>
        </html>
        """
        
        return html_body
    
    def _get_recommendations(self, alert_type: str) -> List[str]:
        """Get recommendations based on alert type"""
        recommendations = {
            'privilege_escalation': [
                'Verify if the role change was authorized',
                'Check with the user if they requested this change',
                'Review recent activity for this user account',
                'Consider temporarily disabling the account if suspicious'
            ],
            'port_scan': [
                'Check network security group rules',
                'Verify if the source IP is legitimate',
                'Consider blocking the source IP if malicious',
                'Review firewall logs for additional activity'
            ],
            'exposed_vm': [
                'Review if public IP exposure is necessary',
                'Implement network security group restrictions',
                'Consider using Azure Bastion for secure access',
                'Audit VM security configuration'
            ],
            'unusual_signin': [
                'Verify with the user if they traveled to this location',
                'Check for concurrent sessions from different locations',
                'Consider requiring MFA for this user',
                'Monitor for additional suspicious activity'
            ],
            'elevated_activity': [
                'Verify if the elevated actions were authorized',
                'Review the business justification for these actions',
                'Check if proper approval process was followed',
                'Monitor for additional privileged operations'
            ]
        }
        
        return recommendations.get(alert_type, ['Investigate this activity immediately'])
