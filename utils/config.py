import os
from dotenv import load_dotenv
from pathlib import Path

# Load environment variables
load_dotenv('config.env')

class Config:
    """Configuration class for Azure Sentinel Lite"""
    
    # Azure Configuration
    AZURE_SUBSCRIPTION_ID = os.getenv('AZURE_SUBSCRIPTION_ID')
    AZURE_TENANT_ID = os.getenv('AZURE_TENANT_ID')
    AZURE_CLIENT_ID = os.getenv('AZURE_CLIENT_ID')
    AZURE_CLIENT_SECRET = os.getenv('AZURE_CLIENT_SECRET')
    
    # Email Configuration
    SMTP_SERVER = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    SMTP_PORT = int(os.getenv('SMTP_PORT', '587'))
    EMAIL_USERNAME = os.getenv('EMAIL_USERNAME')
    EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
    ADMIN_EMAIL = os.getenv('ADMIN_EMAIL')
    
    # Detection Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    ALERT_THRESHOLD_MINUTES = int(os.getenv('ALERT_THRESHOLD_MINUTES', '60'))
    MAX_FAILED_LOGINS = int(os.getenv('MAX_FAILED_LOGINS', '5'))
    UNUSUAL_LOCATION_THRESHOLD_KM = int(os.getenv('UNUSUAL_LOCATION_THRESHOLD_KM', '500'))
    
    # Paths
    LOG_DIR = Path('logs')
    ALERTS_LOG = LOG_DIR / 'alerts.jsonl'
    EVENTS_LOG = LOG_DIR / 'events_log.jsonl'
    
    # GeoIP
    GEOIP_DB_PATH = os.getenv('GEOIP_DB_PATH', './data/GeoLite2-City.mmdb')
    
    @classmethod
    def validate(cls):
        """Validate required configuration"""
        required_fields = [
            'AZURE_SUBSCRIPTION_ID', 'AZURE_TENANT_ID', 
            'EMAIL_USERNAME', 'EMAIL_PASSWORD', 'ADMIN_EMAIL'
        ]
        
        missing = []
        for field in required_fields:
            if not getattr(cls, field):
                missing.append(field)
        
        if missing:
            raise ValueError(f"Missing required configuration: {', '.join(missing)}")
        
        return True
