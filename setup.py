#!/usr/bin/env python3
"""
Setup script for Azure Sentinel Lite
"""

import os
import sys
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        sys.exit(1)
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False
    
    return True

def create_config_file():
    """Create configuration file from template"""
    config_file = Path("config.env")
    
    if config_file.exists():
        print("✅ Configuration file already exists")
        return True
    
    print("📝 Creating configuration file...")
    
    config_template = """# Azure Configuration
AZURE_SUBSCRIPTION_ID=your_subscription_id_here
AZURE_TENANT_ID=your_tenant_id_here
AZURE_CLIENT_ID=your_client_id_here
AZURE_CLIENT_SECRET=your_client_secret_here

# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
ADMIN_EMAIL=<EMAIL>

# Detection Configuration
LOG_LEVEL=INFO
ALERT_THRESHOLD_MINUTES=60
MAX_FAILED_LOGINS=5
UNUSUAL_LOCATION_THRESHOLD_KM=500

# GeoIP Database (optional - for location detection)
GEOIP_DB_PATH=./data/GeoLite2-City.mmdb
"""
    
    try:
        with open(config_file, 'w') as f:
            f.write(config_template)
        print("✅ Configuration file created: config.env")
        print("⚠️  Please edit config.env with your actual values before running")
        return True
    except Exception as e:
        print(f"❌ Failed to create configuration file: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = [
        "logs",
        "data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directories created")

def validate_azure_cli():
    """Check if Azure CLI is available (optional)"""
    try:
        import subprocess
        result = subprocess.run(["az", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Azure CLI detected")
            return True
    except:
        pass
    
    print("⚠️  Azure CLI not found (optional - for easier service principal setup)")
    return False

def show_next_steps():
    """Show next steps to user"""
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("\n📋 Next Steps:")
    print("1. Edit config.env with your Azure and email settings")
    print("2. Create an Azure Service Principal with appropriate permissions:")
    print("   az ad sp create-for-rbac --name 'azure-sentinel-lite' --role 'Security Reader'")
    print("3. Test the configuration:")
    print("   python azure_sentinel_lite.py --simulate")
    print("4. Start monitoring:")
    print("   python azure_sentinel_lite.py --monitor")
    
    print("\n📚 Documentation:")
    print("   README.md - Complete setup and usage guide")
    
    print("\n🔧 Test Commands:")
    print("   python azure_sentinel_lite.py --help")
    print("   python azure_sentinel_lite.py --simulate")
    print("   python azure_sentinel_lite.py --recent 5")
    
    print("\n⚠️  Important:")
    print("   - Update config.env with real credentials")
    print("   - Ensure service principal has proper permissions")
    print("   - Use app passwords for Gmail (not regular password)")
    print("="*60)

def main():
    """Main setup function"""
    print("🔒 Azure Sentinel Lite Setup")
    print("="*40)
    
    # Check Python version
    check_python_version()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Create configuration file
    if not create_config_file():
        print("❌ Setup failed during configuration file creation")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Check for Azure CLI (optional)
    validate_azure_cli()
    
    # Show next steps
    show_next_steps()

if __name__ == "__main__":
    main()
