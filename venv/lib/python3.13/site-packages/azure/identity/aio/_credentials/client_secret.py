# ------------------------------------
# Copyright (c) Microsoft Corporation.
# Licensed under the MIT License.
# ------------------------------------
from typing import Optional, TypeVar, Any

from azure.core.credentials import AccessToken
from .._internal import AadClient, AsyncContextManager
from .._internal.get_token_mixin import GetTokenMixin
from ..._internal import validate_tenant_id

T = TypeVar("T", bound="ClientSecretCredential")


class ClientSecretCredential(AsyncContextManager, GetTokenMixin):
    """Authenticates as a service principal using a client secret.

    :param str tenant_id: ID of the service principal's tenant. Also called its 'directory' ID.
    :param str client_id: The service principal's client ID
    :param str client_secret: One of the service principal's client secrets

    :keyword str authority: Authority of a Microsoft Entra endpoint, for example 'login.microsoftonline.com',
          the authority for Azure Public Cloud (which is the default). :class:`~azure.identity.AzureAuthorityHosts`
          defines authorities for other clouds.
    :keyword cache_persistence_options: Configuration for persistent token caching. If unspecified, the credential
          will cache tokens in memory.
    :paramtype cache_persistence_options: ~azure.identity.TokenCachePersistenceOptions
    :keyword List[str] additionally_allowed_tenants: Specifies tenants in addition to the specified "tenant_id"
        for which the credential may acquire tokens. Add the wildcard value "*" to allow the credential to
        acquire tokens for any tenant the application can access.

    .. admonition:: Example:

        .. literalinclude:: ../samples/credential_creation_code_snippets.py
            :start-after: [START create_client_secret_credential_async]
            :end-before: [END create_client_secret_credential_async]
            :language: python
            :dedent: 4
            :caption: Create a ClientSecretCredential.
    """

    def __init__(self, tenant_id: str, client_id: str, client_secret: str, **kwargs: Any) -> None:
        if not client_id:
            raise ValueError("client_id should be the id of a Microsoft Entra application")
        if not client_secret:
            raise ValueError("secret should be a Microsoft Entra application's client secret")
        if not tenant_id:
            raise ValueError("tenant_id should be a Microsoft Entra tenant's id (also called its 'directory id')")
        validate_tenant_id(tenant_id)

        self._client = AadClient(tenant_id, client_id, **kwargs)
        self._client_id = client_id
        self._secret = client_secret
        super().__init__()

    async def __aenter__(self: T) -> T:
        await self._client.__aenter__()
        return self

    async def close(self) -> None:
        """Close the credential's transport session."""

        await self._client.__aexit__()

    async def _acquire_token_silently(self, *scopes: str, **kwargs: Any) -> Optional[AccessToken]:
        return self._client.get_cached_access_token(scopes, **kwargs)

    async def _request_token(self, *scopes: str, **kwargs: Any) -> AccessToken:
        return await self._client.obtain_token_by_client_secret(scopes, self._secret, **kwargs)
