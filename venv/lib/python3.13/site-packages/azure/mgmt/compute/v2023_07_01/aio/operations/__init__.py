# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._operations import Operations
from ._operations import UsageOperations
from ._operations import VirtualMachineSizesOperations
from ._operations import VirtualMachineScaleSetsOperations
from ._operations import VirtualMachineScaleSetExtensionsOperations
from ._operations import VirtualMachineScaleSetRollingUpgradesOperations
from ._operations import VirtualMachineScaleSetVMExtensionsOperations
from ._operations import VirtualMachineScaleSetVMsOperations
from ._operations import VirtualMachineExtensionsOperations
from ._operations import VirtualMachinesOperations
from ._operations import VirtualMachineImagesOperations
from ._operations import VirtualMachineImagesEdgeZoneOperations
from ._operations import VirtualMachineExtensionImagesOperations
from ._operations import AvailabilitySetsOperations
from ._operations import ProximityPlacementGroupsOperations
from ._operations import DedicatedHostGroupsOperations
from ._operations import DedicatedHostsOperations
from ._operations import SshPublicKeysOperations
from ._operations import ImagesOperations
from ._operations import RestorePointCollectionsOperations
from ._operations import RestorePointsOperations
from ._operations import CapacityReservationGroupsOperations
from ._operations import CapacityReservationsOperations
from ._operations import LogAnalyticsOperations
from ._operations import VirtualMachineRunCommandsOperations
from ._operations import VirtualMachineScaleSetVMRunCommandsOperations

from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "Operations",
    "UsageOperations",
    "VirtualMachineSizesOperations",
    "VirtualMachineScaleSetsOperations",
    "VirtualMachineScaleSetExtensionsOperations",
    "VirtualMachineScaleSetRollingUpgradesOperations",
    "VirtualMachineScaleSetVMExtensionsOperations",
    "VirtualMachineScaleSetVMsOperations",
    "VirtualMachineExtensionsOperations",
    "VirtualMachinesOperations",
    "VirtualMachineImagesOperations",
    "VirtualMachineImagesEdgeZoneOperations",
    "VirtualMachineExtensionImagesOperations",
    "AvailabilitySetsOperations",
    "ProximityPlacementGroupsOperations",
    "DedicatedHostGroupsOperations",
    "DedicatedHostsOperations",
    "SshPublicKeysOperations",
    "ImagesOperations",
    "RestorePointCollectionsOperations",
    "RestorePointsOperations",
    "CapacityReservationGroupsOperations",
    "CapacityReservationsOperations",
    "LogAnalyticsOperations",
    "VirtualMachineRunCommandsOperations",
    "VirtualMachineScaleSetVMRunCommandsOperations",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
