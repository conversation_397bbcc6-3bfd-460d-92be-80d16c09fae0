# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import Any, Dict, List, Optional, TYPE_CHECKING, Union

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class AccessUri(_serialization.Model):
    """A disk access SAS uri.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar access_sas: A SAS uri for accessing a disk.
    :vartype access_sas: str
    :ivar security_data_access_sas: A SAS uri for accessing a VM guest state.
    :vartype security_data_access_sas: str
    """

    _validation = {
        "access_sas": {"readonly": True},
        "security_data_access_sas": {"readonly": True},
    }

    _attribute_map = {
        "access_sas": {"key": "accessSAS", "type": "str"},
        "security_data_access_sas": {"key": "securityDataAccessSAS", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.access_sas = None
        self.security_data_access_sas = None


class ApiError(_serialization.Model):
    """Api error.

    :ivar details: The Api error details.
    :vartype details: list[~azure.mgmt.compute.v2023_04_02.models.ApiErrorBase]
    :ivar innererror: The Api inner error.
    :vartype innererror: ~azure.mgmt.compute.v2023_04_02.models.InnerError
    :ivar code: The error code.
    :vartype code: str
    :ivar target: The target of the particular error.
    :vartype target: str
    :ivar message: The error message.
    :vartype message: str
    """

    _attribute_map = {
        "details": {"key": "details", "type": "[ApiErrorBase]"},
        "innererror": {"key": "innererror", "type": "InnerError"},
        "code": {"key": "code", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "message": {"key": "message", "type": "str"},
    }

    def __init__(
        self,
        *,
        details: Optional[List["_models.ApiErrorBase"]] = None,
        innererror: Optional["_models.InnerError"] = None,
        code: Optional[str] = None,
        target: Optional[str] = None,
        message: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword details: The Api error details.
        :paramtype details: list[~azure.mgmt.compute.v2023_04_02.models.ApiErrorBase]
        :keyword innererror: The Api inner error.
        :paramtype innererror: ~azure.mgmt.compute.v2023_04_02.models.InnerError
        :keyword code: The error code.
        :paramtype code: str
        :keyword target: The target of the particular error.
        :paramtype target: str
        :keyword message: The error message.
        :paramtype message: str
        """
        super().__init__(**kwargs)
        self.details = details
        self.innererror = innererror
        self.code = code
        self.target = target
        self.message = message


class ApiErrorBase(_serialization.Model):
    """Api error base.

    :ivar code: The error code.
    :vartype code: str
    :ivar target: The target of the particular error.
    :vartype target: str
    :ivar message: The error message.
    :vartype message: str
    """

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "message": {"key": "message", "type": "str"},
    }

    def __init__(
        self, *, code: Optional[str] = None, target: Optional[str] = None, message: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword code: The error code.
        :paramtype code: str
        :keyword target: The target of the particular error.
        :paramtype target: str
        :keyword message: The error message.
        :paramtype message: str
        """
        super().__init__(**kwargs)
        self.code = code
        self.target = target
        self.message = message


class CopyCompletionError(_serialization.Model):
    """Indicates the error details if the background copy of a resource created via the CopyStart
    operation fails.

    All required parameters must be populated in order to send to Azure.

    :ivar error_code: Indicates the error code if the background copy of a resource created via the
     CopyStart operation fails. Required. "CopySourceNotFound"
    :vartype error_code: str or ~azure.mgmt.compute.v2023_04_02.models.CopyCompletionErrorReason
    :ivar error_message: Indicates the error message if the background copy of a resource created
     via the CopyStart operation fails. Required.
    :vartype error_message: str
    """

    _validation = {
        "error_code": {"required": True},
        "error_message": {"required": True},
    }

    _attribute_map = {
        "error_code": {"key": "errorCode", "type": "str"},
        "error_message": {"key": "errorMessage", "type": "str"},
    }

    def __init__(
        self, *, error_code: Union[str, "_models.CopyCompletionErrorReason"], error_message: str, **kwargs: Any
    ) -> None:
        """
        :keyword error_code: Indicates the error code if the background copy of a resource created via
         the CopyStart operation fails. Required. "CopySourceNotFound"
        :paramtype error_code: str or ~azure.mgmt.compute.v2023_04_02.models.CopyCompletionErrorReason
        :keyword error_message: Indicates the error message if the background copy of a resource
         created via the CopyStart operation fails. Required.
        :paramtype error_message: str
        """
        super().__init__(**kwargs)
        self.error_code = error_code
        self.error_message = error_message


class CreationData(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Data used when creating a disk.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar create_option: This enumerates the possible sources of a disk's creation. Required. Known
     values are: "Empty", "Attach", "FromImage", "Import", "Copy", "Restore", "Upload", "CopyStart",
     "ImportSecure", "UploadPreparedSecure", "CopyFromSanSnapshot", and "Import".
    :vartype create_option: str or ~azure.mgmt.compute.v2023_04_02.models.DiskCreateOption
    :ivar storage_account_id: Required if createOption is Import. The Azure Resource Manager
     identifier of the storage account containing the blob to import as a disk.
    :vartype storage_account_id: str
    :ivar image_reference: Disk source information for PIR or user images.
    :vartype image_reference: ~azure.mgmt.compute.v2023_04_02.models.ImageDiskReference
    :ivar gallery_image_reference: Required if creating from a Gallery Image. The
     id/sharedGalleryImageId/communityGalleryImageId of the ImageDiskReference will be the ARM id of
     the shared galley image version from which to create a disk.
    :vartype gallery_image_reference: ~azure.mgmt.compute.v2023_04_02.models.ImageDiskReference
    :ivar source_uri: If createOption is Import, this is the URI of a blob to be imported into a
     managed disk.
    :vartype source_uri: str
    :ivar source_resource_id: If createOption is Copy, this is the ARM id of the source snapshot or
     disk.
    :vartype source_resource_id: str
    :ivar source_unique_id: If this field is set, this is the unique id identifying the source of
     this resource.
    :vartype source_unique_id: str
    :ivar upload_size_bytes: If createOption is Upload, this is the size of the contents of the
     upload including the VHD footer. This value should be between 20972032 (20 MiB + 512 bytes for
     the VHD footer) and 35183298347520 bytes (32 TiB + 512 bytes for the VHD footer).
    :vartype upload_size_bytes: int
    :ivar logical_sector_size: Logical sector size in bytes for Ultra disks. Supported values are
     512 ad 4096. 4096 is the default.
    :vartype logical_sector_size: int
    :ivar security_data_uri: If createOption is ImportSecure, this is the URI of a blob to be
     imported into VM guest state.
    :vartype security_data_uri: str
    :ivar performance_plus: Set this flag to true to get a boost on the performance target of the
     disk deployed, see here on the respective performance target. This flag can only be set on disk
     creation time and cannot be disabled after enabled.
    :vartype performance_plus: bool
    :ivar elastic_san_resource_id: Required if createOption is CopyFromSanSnapshot. This is the ARM
     id of the source elastic san volume snapshot.
    :vartype elastic_san_resource_id: str
    """

    _validation = {
        "create_option": {"required": True},
        "source_unique_id": {"readonly": True},
    }

    _attribute_map = {
        "create_option": {"key": "createOption", "type": "str"},
        "storage_account_id": {"key": "storageAccountId", "type": "str"},
        "image_reference": {"key": "imageReference", "type": "ImageDiskReference"},
        "gallery_image_reference": {"key": "galleryImageReference", "type": "ImageDiskReference"},
        "source_uri": {"key": "sourceUri", "type": "str"},
        "source_resource_id": {"key": "sourceResourceId", "type": "str"},
        "source_unique_id": {"key": "sourceUniqueId", "type": "str"},
        "upload_size_bytes": {"key": "uploadSizeBytes", "type": "int"},
        "logical_sector_size": {"key": "logicalSectorSize", "type": "int"},
        "security_data_uri": {"key": "securityDataUri", "type": "str"},
        "performance_plus": {"key": "performancePlus", "type": "bool"},
        "elastic_san_resource_id": {"key": "elasticSanResourceId", "type": "str"},
    }

    def __init__(
        self,
        *,
        create_option: Union[str, "_models.DiskCreateOption"],
        storage_account_id: Optional[str] = None,
        image_reference: Optional["_models.ImageDiskReference"] = None,
        gallery_image_reference: Optional["_models.ImageDiskReference"] = None,
        source_uri: Optional[str] = None,
        source_resource_id: Optional[str] = None,
        upload_size_bytes: Optional[int] = None,
        logical_sector_size: Optional[int] = None,
        security_data_uri: Optional[str] = None,
        performance_plus: Optional[bool] = None,
        elastic_san_resource_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword create_option: This enumerates the possible sources of a disk's creation. Required.
         Known values are: "Empty", "Attach", "FromImage", "Import", "Copy", "Restore", "Upload",
         "CopyStart", "ImportSecure", "UploadPreparedSecure", "CopyFromSanSnapshot", and "Import".
        :paramtype create_option: str or ~azure.mgmt.compute.v2023_04_02.models.DiskCreateOption
        :keyword storage_account_id: Required if createOption is Import. The Azure Resource Manager
         identifier of the storage account containing the blob to import as a disk.
        :paramtype storage_account_id: str
        :keyword image_reference: Disk source information for PIR or user images.
        :paramtype image_reference: ~azure.mgmt.compute.v2023_04_02.models.ImageDiskReference
        :keyword gallery_image_reference: Required if creating from a Gallery Image. The
         id/sharedGalleryImageId/communityGalleryImageId of the ImageDiskReference will be the ARM id of
         the shared galley image version from which to create a disk.
        :paramtype gallery_image_reference: ~azure.mgmt.compute.v2023_04_02.models.ImageDiskReference
        :keyword source_uri: If createOption is Import, this is the URI of a blob to be imported into a
         managed disk.
        :paramtype source_uri: str
        :keyword source_resource_id: If createOption is Copy, this is the ARM id of the source snapshot
         or disk.
        :paramtype source_resource_id: str
        :keyword upload_size_bytes: If createOption is Upload, this is the size of the contents of the
         upload including the VHD footer. This value should be between 20972032 (20 MiB + 512 bytes for
         the VHD footer) and 35183298347520 bytes (32 TiB + 512 bytes for the VHD footer).
        :paramtype upload_size_bytes: int
        :keyword logical_sector_size: Logical sector size in bytes for Ultra disks. Supported values
         are 512 ad 4096. 4096 is the default.
        :paramtype logical_sector_size: int
        :keyword security_data_uri: If createOption is ImportSecure, this is the URI of a blob to be
         imported into VM guest state.
        :paramtype security_data_uri: str
        :keyword performance_plus: Set this flag to true to get a boost on the performance target of
         the disk deployed, see here on the respective performance target. This flag can only be set on
         disk creation time and cannot be disabled after enabled.
        :paramtype performance_plus: bool
        :keyword elastic_san_resource_id: Required if createOption is CopyFromSanSnapshot. This is the
         ARM id of the source elastic san volume snapshot.
        :paramtype elastic_san_resource_id: str
        """
        super().__init__(**kwargs)
        self.create_option = create_option
        self.storage_account_id = storage_account_id
        self.image_reference = image_reference
        self.gallery_image_reference = gallery_image_reference
        self.source_uri = source_uri
        self.source_resource_id = source_resource_id
        self.source_unique_id = None
        self.upload_size_bytes = upload_size_bytes
        self.logical_sector_size = logical_sector_size
        self.security_data_uri = security_data_uri
        self.performance_plus = performance_plus
        self.elastic_san_resource_id = elastic_san_resource_id


class Resource(_serialization.Model):
    """The Resource model definition.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Resource Id.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :ivar location: Resource location. Required.
    :vartype location: str
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "location": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, *, location: str, tags: Optional[Dict[str, str]] = None, **kwargs: Any) -> None:
        """
        :keyword location: Resource location. Required.
        :paramtype location: str
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.location = location
        self.tags = tags


class Disk(Resource):  # pylint: disable=too-many-instance-attributes
    """Disk resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Resource Id.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :ivar location: Resource location. Required.
    :vartype location: str
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar managed_by: A relative URI containing the ID of the VM that has the disk attached.
    :vartype managed_by: str
    :ivar managed_by_extended: List of relative URIs containing the IDs of the VMs that have the
     disk attached. maxShares should be set to a value greater than one for disks to allow attaching
     them to multiple VMs.
    :vartype managed_by_extended: list[str]
    :ivar sku: The disks sku name. Can be Standard_LRS, Premium_LRS, StandardSSD_LRS, UltraSSD_LRS,
     Premium_ZRS, StandardSSD_ZRS, or PremiumV2_LRS.
    :vartype sku: ~azure.mgmt.compute.v2023_04_02.models.DiskSku
    :ivar zones: The Logical zone list for Disk.
    :vartype zones: list[str]
    :ivar extended_location: The extended location where the disk will be created. Extended
     location cannot be changed.
    :vartype extended_location: ~azure.mgmt.compute.v2023_04_02.models.ExtendedLocation
    :ivar time_created: The time when the disk was created.
    :vartype time_created: ~datetime.datetime
    :ivar os_type: The Operating System type. Known values are: "Windows" and "Linux".
    :vartype os_type: str or ~azure.mgmt.compute.v2023_04_02.models.OperatingSystemTypes
    :ivar hyper_v_generation: The hypervisor generation of the Virtual Machine. Applicable to OS
     disks only. Known values are: "V1" and "V2".
    :vartype hyper_v_generation: str or ~azure.mgmt.compute.v2023_04_02.models.HyperVGeneration
    :ivar purchase_plan: Purchase plan information for the the image from which the OS disk was
     created. E.g. - {name: 2019-Datacenter, publisher: MicrosoftWindowsServer, product:
     WindowsServer}.
    :vartype purchase_plan: ~azure.mgmt.compute.v2023_04_02.models.PurchasePlan
    :ivar supported_capabilities: List of supported capabilities for the image from which the OS
     disk was created.
    :vartype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
    :ivar creation_data: Disk source information. CreationData information cannot be changed after
     the disk has been created.
    :vartype creation_data: ~azure.mgmt.compute.v2023_04_02.models.CreationData
    :ivar disk_size_gb: If creationData.createOption is Empty, this field is mandatory and it
     indicates the size of the disk to create. If this field is present for updates or creation with
     other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a
     running VM, and can only increase the disk's size.
    :vartype disk_size_gb: int
    :ivar disk_size_bytes: The size of the disk in bytes. This field is read only.
    :vartype disk_size_bytes: int
    :ivar unique_id: Unique Guid identifying the resource.
    :vartype unique_id: str
    :ivar encryption_settings_collection: Encryption settings collection used for Azure Disk
     Encryption, can contain multiple encryption settings per disk or snapshot.
    :vartype encryption_settings_collection:
     ~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsCollection
    :ivar provisioning_state: The disk provisioning state.
    :vartype provisioning_state: str
    :ivar disk_iops_read_write: The number of IOPS allowed for this disk; only settable for
     UltraSSD disks. One operation can transfer between 4k and 256k bytes.
    :vartype disk_iops_read_write: int
    :ivar disk_m_bps_read_write: The bandwidth allowed for this disk; only settable for UltraSSD
     disks. MBps means millions of bytes per second - MB here uses the ISO notation, of powers of
     10.
    :vartype disk_m_bps_read_write: int
    :ivar disk_iops_read_only: The total number of IOPS that will be allowed across all VMs
     mounting the shared disk as ReadOnly. One operation can transfer between 4k and 256k bytes.
    :vartype disk_iops_read_only: int
    :ivar disk_m_bps_read_only: The total throughput (MBps) that will be allowed across all VMs
     mounting the shared disk as ReadOnly. MBps means millions of bytes per second - MB here uses
     the ISO notation, of powers of 10.
    :vartype disk_m_bps_read_only: int
    :ivar disk_state: The state of the disk. Known values are: "Unattached", "Attached",
     "Reserved", "Frozen", "ActiveSAS", "ActiveSASFrozen", "ReadyToUpload", and "ActiveUpload".
    :vartype disk_state: str or ~azure.mgmt.compute.v2023_04_02.models.DiskState
    :ivar encryption: Encryption property can be used to encrypt data at rest with customer managed
     keys or platform managed keys.
    :vartype encryption: ~azure.mgmt.compute.v2023_04_02.models.Encryption
    :ivar max_shares: The maximum number of VMs that can attach to the disk at the same time. Value
     greater than one indicates a disk that can be mounted on multiple VMs at the same time.
    :vartype max_shares: int
    :ivar share_info: Details of the list of all VMs that have the disk attached. maxShares should
     be set to a value greater than one for disks to allow attaching them to multiple VMs.
    :vartype share_info: list[~azure.mgmt.compute.v2023_04_02.models.ShareInfoElement]
    :ivar network_access_policy: Policy for accessing the disk via network. Known values are:
     "AllowAll", "AllowPrivate", and "DenyAll".
    :vartype network_access_policy: str or
     ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
    :ivar disk_access_id: ARM id of the DiskAccess resource for using private endpoints on disks.
    :vartype disk_access_id: str
    :ivar bursting_enabled_time: Latest time when bursting was last enabled on a disk.
    :vartype bursting_enabled_time: ~datetime.datetime
    :ivar tier: Performance tier of the disk (e.g, P4, S10) as described here:
     https://azure.microsoft.com/en-us/pricing/details/managed-disks/. Does not apply to Ultra
     disks.
    :vartype tier: str
    :ivar bursting_enabled: Set to true to enable bursting beyond the provisioned performance
     target of the disk. Bursting is disabled by default. Does not apply to Ultra disks.
    :vartype bursting_enabled: bool
    :ivar property_updates_in_progress: Properties of the disk for which update is pending.
    :vartype property_updates_in_progress:
     ~azure.mgmt.compute.v2023_04_02.models.PropertyUpdatesInProgress
    :ivar supports_hibernation: Indicates the OS on a disk supports hibernation.
    :vartype supports_hibernation: bool
    :ivar security_profile: Contains the security related information for the resource.
    :vartype security_profile: ~azure.mgmt.compute.v2023_04_02.models.DiskSecurityProfile
    :ivar completion_percent: Percentage complete for the background copy when a resource is
     created via the CopyStart operation.
    :vartype completion_percent: float
    :ivar public_network_access: Policy for controlling export on the disk. Known values are:
     "Enabled" and "Disabled".
    :vartype public_network_access: str or
     ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
    :ivar data_access_auth_mode: Additional authentication requirements when exporting or uploading
     to a disk or snapshot. Known values are: "AzureActiveDirectory" and "None".
    :vartype data_access_auth_mode: str or
     ~azure.mgmt.compute.v2023_04_02.models.DataAccessAuthMode
    :ivar optimized_for_frequent_attach: Setting this property to true improves reliability and
     performance of data disks that are frequently (more than 5 times a day) by detached from one
     virtual machine and attached to another. This property should not be set for disks that are not
     detached and attached frequently as it causes the disks to not align with the fault domain of
     the virtual machine.
    :vartype optimized_for_frequent_attach: bool
    :ivar last_ownership_update_time: The UTC time when the ownership state of the disk was last
     changed i.e., the time the disk was last attached or detached from a VM or the time when the VM
     to which the disk was attached was deallocated or started.
    :vartype last_ownership_update_time: ~datetime.datetime
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "location": {"required": True},
        "managed_by": {"readonly": True},
        "managed_by_extended": {"readonly": True},
        "time_created": {"readonly": True},
        "disk_size_bytes": {"readonly": True},
        "unique_id": {"readonly": True},
        "provisioning_state": {"readonly": True},
        "disk_state": {"readonly": True},
        "share_info": {"readonly": True},
        "bursting_enabled_time": {"readonly": True},
        "property_updates_in_progress": {"readonly": True},
        "last_ownership_update_time": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "managed_by": {"key": "managedBy", "type": "str"},
        "managed_by_extended": {"key": "managedByExtended", "type": "[str]"},
        "sku": {"key": "sku", "type": "DiskSku"},
        "zones": {"key": "zones", "type": "[str]"},
        "extended_location": {"key": "extendedLocation", "type": "ExtendedLocation"},
        "time_created": {"key": "properties.timeCreated", "type": "iso-8601"},
        "os_type": {"key": "properties.osType", "type": "str"},
        "hyper_v_generation": {"key": "properties.hyperVGeneration", "type": "str"},
        "purchase_plan": {"key": "properties.purchasePlan", "type": "PurchasePlan"},
        "supported_capabilities": {"key": "properties.supportedCapabilities", "type": "SupportedCapabilities"},
        "creation_data": {"key": "properties.creationData", "type": "CreationData"},
        "disk_size_gb": {"key": "properties.diskSizeGB", "type": "int"},
        "disk_size_bytes": {"key": "properties.diskSizeBytes", "type": "int"},
        "unique_id": {"key": "properties.uniqueId", "type": "str"},
        "encryption_settings_collection": {
            "key": "properties.encryptionSettingsCollection",
            "type": "EncryptionSettingsCollection",
        },
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "disk_iops_read_write": {"key": "properties.diskIOPSReadWrite", "type": "int"},
        "disk_m_bps_read_write": {"key": "properties.diskMBpsReadWrite", "type": "int"},
        "disk_iops_read_only": {"key": "properties.diskIOPSReadOnly", "type": "int"},
        "disk_m_bps_read_only": {"key": "properties.diskMBpsReadOnly", "type": "int"},
        "disk_state": {"key": "properties.diskState", "type": "str"},
        "encryption": {"key": "properties.encryption", "type": "Encryption"},
        "max_shares": {"key": "properties.maxShares", "type": "int"},
        "share_info": {"key": "properties.shareInfo", "type": "[ShareInfoElement]"},
        "network_access_policy": {"key": "properties.networkAccessPolicy", "type": "str"},
        "disk_access_id": {"key": "properties.diskAccessId", "type": "str"},
        "bursting_enabled_time": {"key": "properties.burstingEnabledTime", "type": "iso-8601"},
        "tier": {"key": "properties.tier", "type": "str"},
        "bursting_enabled": {"key": "properties.burstingEnabled", "type": "bool"},
        "property_updates_in_progress": {
            "key": "properties.propertyUpdatesInProgress",
            "type": "PropertyUpdatesInProgress",
        },
        "supports_hibernation": {"key": "properties.supportsHibernation", "type": "bool"},
        "security_profile": {"key": "properties.securityProfile", "type": "DiskSecurityProfile"},
        "completion_percent": {"key": "properties.completionPercent", "type": "float"},
        "public_network_access": {"key": "properties.publicNetworkAccess", "type": "str"},
        "data_access_auth_mode": {"key": "properties.dataAccessAuthMode", "type": "str"},
        "optimized_for_frequent_attach": {"key": "properties.optimizedForFrequentAttach", "type": "bool"},
        "last_ownership_update_time": {"key": "properties.LastOwnershipUpdateTime", "type": "iso-8601"},
    }

    def __init__(  # pylint: disable=too-many-locals
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        sku: Optional["_models.DiskSku"] = None,
        zones: Optional[List[str]] = None,
        extended_location: Optional["_models.ExtendedLocation"] = None,
        os_type: Optional[Union[str, "_models.OperatingSystemTypes"]] = None,
        hyper_v_generation: Optional[Union[str, "_models.HyperVGeneration"]] = None,
        purchase_plan: Optional["_models.PurchasePlan"] = None,
        supported_capabilities: Optional["_models.SupportedCapabilities"] = None,
        creation_data: Optional["_models.CreationData"] = None,
        disk_size_gb: Optional[int] = None,
        encryption_settings_collection: Optional["_models.EncryptionSettingsCollection"] = None,
        disk_iops_read_write: Optional[int] = None,
        disk_m_bps_read_write: Optional[int] = None,
        disk_iops_read_only: Optional[int] = None,
        disk_m_bps_read_only: Optional[int] = None,
        encryption: Optional["_models.Encryption"] = None,
        max_shares: Optional[int] = None,
        network_access_policy: Optional[Union[str, "_models.NetworkAccessPolicy"]] = None,
        disk_access_id: Optional[str] = None,
        tier: Optional[str] = None,
        bursting_enabled: Optional[bool] = None,
        supports_hibernation: Optional[bool] = None,
        security_profile: Optional["_models.DiskSecurityProfile"] = None,
        completion_percent: Optional[float] = None,
        public_network_access: Optional[Union[str, "_models.PublicNetworkAccess"]] = None,
        data_access_auth_mode: Optional[Union[str, "_models.DataAccessAuthMode"]] = None,
        optimized_for_frequent_attach: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword location: Resource location. Required.
        :paramtype location: str
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword sku: The disks sku name. Can be Standard_LRS, Premium_LRS, StandardSSD_LRS,
         UltraSSD_LRS, Premium_ZRS, StandardSSD_ZRS, or PremiumV2_LRS.
        :paramtype sku: ~azure.mgmt.compute.v2023_04_02.models.DiskSku
        :keyword zones: The Logical zone list for Disk.
        :paramtype zones: list[str]
        :keyword extended_location: The extended location where the disk will be created. Extended
         location cannot be changed.
        :paramtype extended_location: ~azure.mgmt.compute.v2023_04_02.models.ExtendedLocation
        :keyword os_type: The Operating System type. Known values are: "Windows" and "Linux".
        :paramtype os_type: str or ~azure.mgmt.compute.v2023_04_02.models.OperatingSystemTypes
        :keyword hyper_v_generation: The hypervisor generation of the Virtual Machine. Applicable to OS
         disks only. Known values are: "V1" and "V2".
        :paramtype hyper_v_generation: str or ~azure.mgmt.compute.v2023_04_02.models.HyperVGeneration
        :keyword purchase_plan: Purchase plan information for the the image from which the OS disk was
         created. E.g. - {name: 2019-Datacenter, publisher: MicrosoftWindowsServer, product:
         WindowsServer}.
        :paramtype purchase_plan: ~azure.mgmt.compute.v2023_04_02.models.PurchasePlan
        :keyword supported_capabilities: List of supported capabilities for the image from which the OS
         disk was created.
        :paramtype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
        :keyword creation_data: Disk source information. CreationData information cannot be changed
         after the disk has been created.
        :paramtype creation_data: ~azure.mgmt.compute.v2023_04_02.models.CreationData
        :keyword disk_size_gb: If creationData.createOption is Empty, this field is mandatory and it
         indicates the size of the disk to create. If this field is present for updates or creation with
         other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a
         running VM, and can only increase the disk's size.
        :paramtype disk_size_gb: int
        :keyword encryption_settings_collection: Encryption settings collection used for Azure Disk
         Encryption, can contain multiple encryption settings per disk or snapshot.
        :paramtype encryption_settings_collection:
         ~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsCollection
        :keyword disk_iops_read_write: The number of IOPS allowed for this disk; only settable for
         UltraSSD disks. One operation can transfer between 4k and 256k bytes.
        :paramtype disk_iops_read_write: int
        :keyword disk_m_bps_read_write: The bandwidth allowed for this disk; only settable for UltraSSD
         disks. MBps means millions of bytes per second - MB here uses the ISO notation, of powers of
         10.
        :paramtype disk_m_bps_read_write: int
        :keyword disk_iops_read_only: The total number of IOPS that will be allowed across all VMs
         mounting the shared disk as ReadOnly. One operation can transfer between 4k and 256k bytes.
        :paramtype disk_iops_read_only: int
        :keyword disk_m_bps_read_only: The total throughput (MBps) that will be allowed across all VMs
         mounting the shared disk as ReadOnly. MBps means millions of bytes per second - MB here uses
         the ISO notation, of powers of 10.
        :paramtype disk_m_bps_read_only: int
        :keyword encryption: Encryption property can be used to encrypt data at rest with customer
         managed keys or platform managed keys.
        :paramtype encryption: ~azure.mgmt.compute.v2023_04_02.models.Encryption
        :keyword max_shares: The maximum number of VMs that can attach to the disk at the same time.
         Value greater than one indicates a disk that can be mounted on multiple VMs at the same time.
        :paramtype max_shares: int
        :keyword network_access_policy: Policy for accessing the disk via network. Known values are:
         "AllowAll", "AllowPrivate", and "DenyAll".
        :paramtype network_access_policy: str or
         ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
        :keyword disk_access_id: ARM id of the DiskAccess resource for using private endpoints on
         disks.
        :paramtype disk_access_id: str
        :keyword tier: Performance tier of the disk (e.g, P4, S10) as described here:
         https://azure.microsoft.com/en-us/pricing/details/managed-disks/. Does not apply to Ultra
         disks.
        :paramtype tier: str
        :keyword bursting_enabled: Set to true to enable bursting beyond the provisioned performance
         target of the disk. Bursting is disabled by default. Does not apply to Ultra disks.
        :paramtype bursting_enabled: bool
        :keyword supports_hibernation: Indicates the OS on a disk supports hibernation.
        :paramtype supports_hibernation: bool
        :keyword security_profile: Contains the security related information for the resource.
        :paramtype security_profile: ~azure.mgmt.compute.v2023_04_02.models.DiskSecurityProfile
        :keyword completion_percent: Percentage complete for the background copy when a resource is
         created via the CopyStart operation.
        :paramtype completion_percent: float
        :keyword public_network_access: Policy for controlling export on the disk. Known values are:
         "Enabled" and "Disabled".
        :paramtype public_network_access: str or
         ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
        :keyword data_access_auth_mode: Additional authentication requirements when exporting or
         uploading to a disk or snapshot. Known values are: "AzureActiveDirectory" and "None".
        :paramtype data_access_auth_mode: str or
         ~azure.mgmt.compute.v2023_04_02.models.DataAccessAuthMode
        :keyword optimized_for_frequent_attach: Setting this property to true improves reliability and
         performance of data disks that are frequently (more than 5 times a day) by detached from one
         virtual machine and attached to another. This property should not be set for disks that are not
         detached and attached frequently as it causes the disks to not align with the fault domain of
         the virtual machine.
        :paramtype optimized_for_frequent_attach: bool
        """
        super().__init__(location=location, tags=tags, **kwargs)
        self.managed_by = None
        self.managed_by_extended = None
        self.sku = sku
        self.zones = zones
        self.extended_location = extended_location
        self.time_created = None
        self.os_type = os_type
        self.hyper_v_generation = hyper_v_generation
        self.purchase_plan = purchase_plan
        self.supported_capabilities = supported_capabilities
        self.creation_data = creation_data
        self.disk_size_gb = disk_size_gb
        self.disk_size_bytes = None
        self.unique_id = None
        self.encryption_settings_collection = encryption_settings_collection
        self.provisioning_state = None
        self.disk_iops_read_write = disk_iops_read_write
        self.disk_m_bps_read_write = disk_m_bps_read_write
        self.disk_iops_read_only = disk_iops_read_only
        self.disk_m_bps_read_only = disk_m_bps_read_only
        self.disk_state = None
        self.encryption = encryption
        self.max_shares = max_shares
        self.share_info = None
        self.network_access_policy = network_access_policy
        self.disk_access_id = disk_access_id
        self.bursting_enabled_time = None
        self.tier = tier
        self.bursting_enabled = bursting_enabled
        self.property_updates_in_progress = None
        self.supports_hibernation = supports_hibernation
        self.security_profile = security_profile
        self.completion_percent = completion_percent
        self.public_network_access = public_network_access
        self.data_access_auth_mode = data_access_auth_mode
        self.optimized_for_frequent_attach = optimized_for_frequent_attach
        self.last_ownership_update_time = None


class DiskAccess(Resource):
    """disk access resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Resource Id.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :ivar location: Resource location. Required.
    :vartype location: str
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar extended_location: The extended location where the disk access will be created. Extended
     location cannot be changed.
    :vartype extended_location: ~azure.mgmt.compute.v2023_04_02.models.ExtendedLocation
    :ivar private_endpoint_connections: A readonly collection of private endpoint connections
     created on the disk. Currently only one endpoint connection is supported.
    :vartype private_endpoint_connections:
     list[~azure.mgmt.compute.v2023_04_02.models.PrivateEndpointConnection]
    :ivar provisioning_state: The disk access resource provisioning state.
    :vartype provisioning_state: str
    :ivar time_created: The time when the disk access was created.
    :vartype time_created: ~datetime.datetime
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "location": {"required": True},
        "private_endpoint_connections": {"readonly": True},
        "provisioning_state": {"readonly": True},
        "time_created": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "extended_location": {"key": "extendedLocation", "type": "ExtendedLocation"},
        "private_endpoint_connections": {
            "key": "properties.privateEndpointConnections",
            "type": "[PrivateEndpointConnection]",
        },
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "time_created": {"key": "properties.timeCreated", "type": "iso-8601"},
    }

    def __init__(
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        extended_location: Optional["_models.ExtendedLocation"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword location: Resource location. Required.
        :paramtype location: str
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword extended_location: The extended location where the disk access will be created.
         Extended location cannot be changed.
        :paramtype extended_location: ~azure.mgmt.compute.v2023_04_02.models.ExtendedLocation
        """
        super().__init__(location=location, tags=tags, **kwargs)
        self.extended_location = extended_location
        self.private_endpoint_connections = None
        self.provisioning_state = None
        self.time_created = None


class DiskAccessList(_serialization.Model):
    """The List disk access operation response.

    All required parameters must be populated in order to send to Azure.

    :ivar value: A list of disk access resources. Required.
    :vartype value: list[~azure.mgmt.compute.v2023_04_02.models.DiskAccess]
    :ivar next_link: The uri to fetch the next page of disk access resources. Call ListNext() with
     this to fetch the next page of disk access resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[DiskAccess]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["_models.DiskAccess"], next_link: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword value: A list of disk access resources. Required.
        :paramtype value: list[~azure.mgmt.compute.v2023_04_02.models.DiskAccess]
        :keyword next_link: The uri to fetch the next page of disk access resources. Call ListNext()
         with this to fetch the next page of disk access resources.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class DiskAccessUpdate(_serialization.Model):
    """Used for updating a disk access resource.

    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    """

    _attribute_map = {
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, *, tags: Optional[Dict[str, str]] = None, **kwargs: Any) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        """
        super().__init__(**kwargs)
        self.tags = tags


class DiskEncryptionSet(Resource):  # pylint: disable=too-many-instance-attributes
    """disk encryption set resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Resource Id.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :ivar location: Resource location. Required.
    :vartype location: str
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar identity: The managed identity for the disk encryption set. It should be given permission
     on the key vault before it can be used to encrypt disks.
    :vartype identity: ~azure.mgmt.compute.v2023_04_02.models.EncryptionSetIdentity
    :ivar encryption_type: The type of key used to encrypt the data of the disk. Known values are:
     "EncryptionAtRestWithCustomerKey", "EncryptionAtRestWithPlatformAndCustomerKeys", and
     "ConfidentialVmEncryptedWithCustomerKey".
    :vartype encryption_type: str or ~azure.mgmt.compute.v2023_04_02.models.DiskEncryptionSetType
    :ivar active_key: The key vault key which is currently used by this disk encryption set.
    :vartype active_key: ~azure.mgmt.compute.v2023_04_02.models.KeyForDiskEncryptionSet
    :ivar previous_keys: A readonly collection of key vault keys previously used by this disk
     encryption set while a key rotation is in progress. It will be empty if there is no ongoing key
     rotation.
    :vartype previous_keys: list[~azure.mgmt.compute.v2023_04_02.models.KeyForDiskEncryptionSet]
    :ivar provisioning_state: The disk encryption set provisioning state.
    :vartype provisioning_state: str
    :ivar rotation_to_latest_key_version_enabled: Set this flag to true to enable auto-updating of
     this disk encryption set to the latest key version.
    :vartype rotation_to_latest_key_version_enabled: bool
    :ivar last_key_rotation_timestamp: The time when the active key of this disk encryption set was
     updated.
    :vartype last_key_rotation_timestamp: ~datetime.datetime
    :ivar auto_key_rotation_error: The error that was encountered during auto-key rotation. If an
     error is present, then auto-key rotation will not be attempted until the error on this disk
     encryption set is fixed.
    :vartype auto_key_rotation_error: ~azure.mgmt.compute.v2023_04_02.models.ApiError
    :ivar federated_client_id: Multi-tenant application client id to access key vault in a
     different tenant. Setting the value to 'None' will clear the property.
    :vartype federated_client_id: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "location": {"required": True},
        "previous_keys": {"readonly": True},
        "provisioning_state": {"readonly": True},
        "last_key_rotation_timestamp": {"readonly": True},
        "auto_key_rotation_error": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "identity": {"key": "identity", "type": "EncryptionSetIdentity"},
        "encryption_type": {"key": "properties.encryptionType", "type": "str"},
        "active_key": {"key": "properties.activeKey", "type": "KeyForDiskEncryptionSet"},
        "previous_keys": {"key": "properties.previousKeys", "type": "[KeyForDiskEncryptionSet]"},
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "rotation_to_latest_key_version_enabled": {
            "key": "properties.rotationToLatestKeyVersionEnabled",
            "type": "bool",
        },
        "last_key_rotation_timestamp": {"key": "properties.lastKeyRotationTimestamp", "type": "iso-8601"},
        "auto_key_rotation_error": {"key": "properties.autoKeyRotationError", "type": "ApiError"},
        "federated_client_id": {"key": "properties.federatedClientId", "type": "str"},
    }

    def __init__(
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        identity: Optional["_models.EncryptionSetIdentity"] = None,
        encryption_type: Optional[Union[str, "_models.DiskEncryptionSetType"]] = None,
        active_key: Optional["_models.KeyForDiskEncryptionSet"] = None,
        rotation_to_latest_key_version_enabled: Optional[bool] = None,
        federated_client_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword location: Resource location. Required.
        :paramtype location: str
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword identity: The managed identity for the disk encryption set. It should be given
         permission on the key vault before it can be used to encrypt disks.
        :paramtype identity: ~azure.mgmt.compute.v2023_04_02.models.EncryptionSetIdentity
        :keyword encryption_type: The type of key used to encrypt the data of the disk. Known values
         are: "EncryptionAtRestWithCustomerKey", "EncryptionAtRestWithPlatformAndCustomerKeys", and
         "ConfidentialVmEncryptedWithCustomerKey".
        :paramtype encryption_type: str or ~azure.mgmt.compute.v2023_04_02.models.DiskEncryptionSetType
        :keyword active_key: The key vault key which is currently used by this disk encryption set.
        :paramtype active_key: ~azure.mgmt.compute.v2023_04_02.models.KeyForDiskEncryptionSet
        :keyword rotation_to_latest_key_version_enabled: Set this flag to true to enable auto-updating
         of this disk encryption set to the latest key version.
        :paramtype rotation_to_latest_key_version_enabled: bool
        :keyword federated_client_id: Multi-tenant application client id to access key vault in a
         different tenant. Setting the value to 'None' will clear the property.
        :paramtype federated_client_id: str
        """
        super().__init__(location=location, tags=tags, **kwargs)
        self.identity = identity
        self.encryption_type = encryption_type
        self.active_key = active_key
        self.previous_keys = None
        self.provisioning_state = None
        self.rotation_to_latest_key_version_enabled = rotation_to_latest_key_version_enabled
        self.last_key_rotation_timestamp = None
        self.auto_key_rotation_error = None
        self.federated_client_id = federated_client_id


class DiskEncryptionSetList(_serialization.Model):
    """The List disk encryption set operation response.

    All required parameters must be populated in order to send to Azure.

    :ivar value: A list of disk encryption sets. Required.
    :vartype value: list[~azure.mgmt.compute.v2023_04_02.models.DiskEncryptionSet]
    :ivar next_link: The uri to fetch the next page of disk encryption sets. Call ListNext() with
     this to fetch the next page of disk encryption sets.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[DiskEncryptionSet]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: List["_models.DiskEncryptionSet"], next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: A list of disk encryption sets. Required.
        :paramtype value: list[~azure.mgmt.compute.v2023_04_02.models.DiskEncryptionSet]
        :keyword next_link: The uri to fetch the next page of disk encryption sets. Call ListNext()
         with this to fetch the next page of disk encryption sets.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class DiskEncryptionSetUpdate(_serialization.Model):
    """disk encryption set update resource.

    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar identity: The managed identity for the disk encryption set. It should be given permission
     on the key vault before it can be used to encrypt disks.
    :vartype identity: ~azure.mgmt.compute.v2023_04_02.models.EncryptionSetIdentity
    :ivar encryption_type: The type of key used to encrypt the data of the disk. Known values are:
     "EncryptionAtRestWithCustomerKey", "EncryptionAtRestWithPlatformAndCustomerKeys", and
     "ConfidentialVmEncryptedWithCustomerKey".
    :vartype encryption_type: str or ~azure.mgmt.compute.v2023_04_02.models.DiskEncryptionSetType
    :ivar active_key: Key Vault Key Url to be used for server side encryption of Managed Disks and
     Snapshots.
    :vartype active_key: ~azure.mgmt.compute.v2023_04_02.models.KeyForDiskEncryptionSet
    :ivar rotation_to_latest_key_version_enabled: Set this flag to true to enable auto-updating of
     this disk encryption set to the latest key version.
    :vartype rotation_to_latest_key_version_enabled: bool
    :ivar federated_client_id: Multi-tenant application client id to access key vault in a
     different tenant. Setting the value to 'None' will clear the property.
    :vartype federated_client_id: str
    """

    _attribute_map = {
        "tags": {"key": "tags", "type": "{str}"},
        "identity": {"key": "identity", "type": "EncryptionSetIdentity"},
        "encryption_type": {"key": "properties.encryptionType", "type": "str"},
        "active_key": {"key": "properties.activeKey", "type": "KeyForDiskEncryptionSet"},
        "rotation_to_latest_key_version_enabled": {
            "key": "properties.rotationToLatestKeyVersionEnabled",
            "type": "bool",
        },
        "federated_client_id": {"key": "properties.federatedClientId", "type": "str"},
    }

    def __init__(
        self,
        *,
        tags: Optional[Dict[str, str]] = None,
        identity: Optional["_models.EncryptionSetIdentity"] = None,
        encryption_type: Optional[Union[str, "_models.DiskEncryptionSetType"]] = None,
        active_key: Optional["_models.KeyForDiskEncryptionSet"] = None,
        rotation_to_latest_key_version_enabled: Optional[bool] = None,
        federated_client_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword identity: The managed identity for the disk encryption set. It should be given
         permission on the key vault before it can be used to encrypt disks.
        :paramtype identity: ~azure.mgmt.compute.v2023_04_02.models.EncryptionSetIdentity
        :keyword encryption_type: The type of key used to encrypt the data of the disk. Known values
         are: "EncryptionAtRestWithCustomerKey", "EncryptionAtRestWithPlatformAndCustomerKeys", and
         "ConfidentialVmEncryptedWithCustomerKey".
        :paramtype encryption_type: str or ~azure.mgmt.compute.v2023_04_02.models.DiskEncryptionSetType
        :keyword active_key: Key Vault Key Url to be used for server side encryption of Managed Disks
         and Snapshots.
        :paramtype active_key: ~azure.mgmt.compute.v2023_04_02.models.KeyForDiskEncryptionSet
        :keyword rotation_to_latest_key_version_enabled: Set this flag to true to enable auto-updating
         of this disk encryption set to the latest key version.
        :paramtype rotation_to_latest_key_version_enabled: bool
        :keyword federated_client_id: Multi-tenant application client id to access key vault in a
         different tenant. Setting the value to 'None' will clear the property.
        :paramtype federated_client_id: str
        """
        super().__init__(**kwargs)
        self.tags = tags
        self.identity = identity
        self.encryption_type = encryption_type
        self.active_key = active_key
        self.rotation_to_latest_key_version_enabled = rotation_to_latest_key_version_enabled
        self.federated_client_id = federated_client_id


class DiskList(_serialization.Model):
    """The List Disks operation response.

    All required parameters must be populated in order to send to Azure.

    :ivar value: A list of disks. Required.
    :vartype value: list[~azure.mgmt.compute.v2023_04_02.models.Disk]
    :ivar next_link: The uri to fetch the next page of disks. Call ListNext() with this to fetch
     the next page of disks.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[Disk]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["_models.Disk"], next_link: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword value: A list of disks. Required.
        :paramtype value: list[~azure.mgmt.compute.v2023_04_02.models.Disk]
        :keyword next_link: The uri to fetch the next page of disks. Call ListNext() with this to fetch
         the next page of disks.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class ProxyOnlyResource(_serialization.Model):
    """The ProxyOnly Resource model definition.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Resource Id.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None


class DiskRestorePoint(ProxyOnlyResource):  # pylint: disable=too-many-instance-attributes
    """Properties of disk restore point.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Resource Id.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :ivar time_created: The timestamp of restorePoint creation.
    :vartype time_created: ~datetime.datetime
    :ivar source_resource_id: arm id of source disk or source disk restore point.
    :vartype source_resource_id: str
    :ivar os_type: The Operating System type. Known values are: "Windows" and "Linux".
    :vartype os_type: str or ~azure.mgmt.compute.v2023_04_02.models.OperatingSystemTypes
    :ivar hyper_v_generation: The hypervisor generation of the Virtual Machine. Applicable to OS
     disks only. Known values are: "V1" and "V2".
    :vartype hyper_v_generation: str or ~azure.mgmt.compute.v2023_04_02.models.HyperVGeneration
    :ivar purchase_plan: Purchase plan information for the the image from which the OS disk was
     created.
    :vartype purchase_plan: ~azure.mgmt.compute.v2023_04_02.models.PurchasePlan
    :ivar supported_capabilities: List of supported capabilities for the image from which the OS
     disk was created.
    :vartype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
    :ivar family_id: id of the backing snapshot's MIS family.
    :vartype family_id: str
    :ivar source_unique_id: unique incarnation id of the source disk.
    :vartype source_unique_id: str
    :ivar encryption: Encryption property can be used to encrypt data at rest with customer managed
     keys or platform managed keys.
    :vartype encryption: ~azure.mgmt.compute.v2023_04_02.models.Encryption
    :ivar supports_hibernation: Indicates the OS on a disk supports hibernation.
    :vartype supports_hibernation: bool
    :ivar network_access_policy: Policy for accessing the disk via network. Known values are:
     "AllowAll", "AllowPrivate", and "DenyAll".
    :vartype network_access_policy: str or
     ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
    :ivar public_network_access: Policy for controlling export on the disk. Known values are:
     "Enabled" and "Disabled".
    :vartype public_network_access: str or
     ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
    :ivar disk_access_id: ARM id of the DiskAccess resource for using private endpoints on disks.
    :vartype disk_access_id: str
    :ivar completion_percent: Percentage complete for the background copy of disk restore point
     when source resource is from a different region.
    :vartype completion_percent: float
    :ivar replication_state: Replication state of disk restore point when source resource is from a
     different region.
    :vartype replication_state: str
    :ivar source_resource_location: Location of source disk or source disk restore point when
     source resource is from a different region.
    :vartype source_resource_location: str
    :ivar security_profile: Contains the security related information for the resource.
    :vartype security_profile: ~azure.mgmt.compute.v2023_04_02.models.DiskSecurityProfile
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "time_created": {"readonly": True},
        "source_resource_id": {"readonly": True},
        "os_type": {"readonly": True},
        "family_id": {"readonly": True},
        "source_unique_id": {"readonly": True},
        "encryption": {"readonly": True},
        "replication_state": {"readonly": True},
        "source_resource_location": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "time_created": {"key": "properties.timeCreated", "type": "iso-8601"},
        "source_resource_id": {"key": "properties.sourceResourceId", "type": "str"},
        "os_type": {"key": "properties.osType", "type": "str"},
        "hyper_v_generation": {"key": "properties.hyperVGeneration", "type": "str"},
        "purchase_plan": {"key": "properties.purchasePlan", "type": "PurchasePlan"},
        "supported_capabilities": {"key": "properties.supportedCapabilities", "type": "SupportedCapabilities"},
        "family_id": {"key": "properties.familyId", "type": "str"},
        "source_unique_id": {"key": "properties.sourceUniqueId", "type": "str"},
        "encryption": {"key": "properties.encryption", "type": "Encryption"},
        "supports_hibernation": {"key": "properties.supportsHibernation", "type": "bool"},
        "network_access_policy": {"key": "properties.networkAccessPolicy", "type": "str"},
        "public_network_access": {"key": "properties.publicNetworkAccess", "type": "str"},
        "disk_access_id": {"key": "properties.diskAccessId", "type": "str"},
        "completion_percent": {"key": "properties.completionPercent", "type": "float"},
        "replication_state": {"key": "properties.replicationState", "type": "str"},
        "source_resource_location": {"key": "properties.sourceResourceLocation", "type": "str"},
        "security_profile": {"key": "properties.securityProfile", "type": "DiskSecurityProfile"},
    }

    def __init__(
        self,
        *,
        hyper_v_generation: Optional[Union[str, "_models.HyperVGeneration"]] = None,
        purchase_plan: Optional["_models.PurchasePlan"] = None,
        supported_capabilities: Optional["_models.SupportedCapabilities"] = None,
        supports_hibernation: Optional[bool] = None,
        network_access_policy: Optional[Union[str, "_models.NetworkAccessPolicy"]] = None,
        public_network_access: Optional[Union[str, "_models.PublicNetworkAccess"]] = None,
        disk_access_id: Optional[str] = None,
        completion_percent: Optional[float] = None,
        security_profile: Optional["_models.DiskSecurityProfile"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword hyper_v_generation: The hypervisor generation of the Virtual Machine. Applicable to OS
         disks only. Known values are: "V1" and "V2".
        :paramtype hyper_v_generation: str or ~azure.mgmt.compute.v2023_04_02.models.HyperVGeneration
        :keyword purchase_plan: Purchase plan information for the the image from which the OS disk was
         created.
        :paramtype purchase_plan: ~azure.mgmt.compute.v2023_04_02.models.PurchasePlan
        :keyword supported_capabilities: List of supported capabilities for the image from which the OS
         disk was created.
        :paramtype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
        :keyword supports_hibernation: Indicates the OS on a disk supports hibernation.
        :paramtype supports_hibernation: bool
        :keyword network_access_policy: Policy for accessing the disk via network. Known values are:
         "AllowAll", "AllowPrivate", and "DenyAll".
        :paramtype network_access_policy: str or
         ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
        :keyword public_network_access: Policy for controlling export on the disk. Known values are:
         "Enabled" and "Disabled".
        :paramtype public_network_access: str or
         ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
        :keyword disk_access_id: ARM id of the DiskAccess resource for using private endpoints on
         disks.
        :paramtype disk_access_id: str
        :keyword completion_percent: Percentage complete for the background copy of disk restore point
         when source resource is from a different region.
        :paramtype completion_percent: float
        :keyword security_profile: Contains the security related information for the resource.
        :paramtype security_profile: ~azure.mgmt.compute.v2023_04_02.models.DiskSecurityProfile
        """
        super().__init__(**kwargs)
        self.time_created = None
        self.source_resource_id = None
        self.os_type = None
        self.hyper_v_generation = hyper_v_generation
        self.purchase_plan = purchase_plan
        self.supported_capabilities = supported_capabilities
        self.family_id = None
        self.source_unique_id = None
        self.encryption = None
        self.supports_hibernation = supports_hibernation
        self.network_access_policy = network_access_policy
        self.public_network_access = public_network_access
        self.disk_access_id = disk_access_id
        self.completion_percent = completion_percent
        self.replication_state = None
        self.source_resource_location = None
        self.security_profile = security_profile


class DiskRestorePointList(_serialization.Model):
    """The List Disk Restore Points operation response.

    All required parameters must be populated in order to send to Azure.

    :ivar value: A list of disk restore points. Required.
    :vartype value: list[~azure.mgmt.compute.v2023_04_02.models.DiskRestorePoint]
    :ivar next_link: The uri to fetch the next page of disk restore points. Call ListNext() with
     this to fetch the next page of disk restore points.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[DiskRestorePoint]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: List["_models.DiskRestorePoint"], next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: A list of disk restore points. Required.
        :paramtype value: list[~azure.mgmt.compute.v2023_04_02.models.DiskRestorePoint]
        :keyword next_link: The uri to fetch the next page of disk restore points. Call ListNext() with
         this to fetch the next page of disk restore points.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class DiskSecurityProfile(_serialization.Model):
    """Contains the security related information for the resource.

    :ivar security_type: Specifies the SecurityType of the VM. Applicable for OS disks only. Known
     values are: "TrustedLaunch", "ConfidentialVM_VMGuestStateOnlyEncryptedWithPlatformKey",
     "ConfidentialVM_DiskEncryptedWithPlatformKey", and
     "ConfidentialVM_DiskEncryptedWithCustomerKey".
    :vartype security_type: str or ~azure.mgmt.compute.v2023_04_02.models.DiskSecurityTypes
    :ivar secure_vm_disk_encryption_set_id: ResourceId of the disk encryption set associated to
     Confidential VM supported disk encrypted with customer managed key.
    :vartype secure_vm_disk_encryption_set_id: str
    """

    _attribute_map = {
        "security_type": {"key": "securityType", "type": "str"},
        "secure_vm_disk_encryption_set_id": {"key": "secureVMDiskEncryptionSetId", "type": "str"},
    }

    def __init__(
        self,
        *,
        security_type: Optional[Union[str, "_models.DiskSecurityTypes"]] = None,
        secure_vm_disk_encryption_set_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword security_type: Specifies the SecurityType of the VM. Applicable for OS disks only.
         Known values are: "TrustedLaunch", "ConfidentialVM_VMGuestStateOnlyEncryptedWithPlatformKey",
         "ConfidentialVM_DiskEncryptedWithPlatformKey", and
         "ConfidentialVM_DiskEncryptedWithCustomerKey".
        :paramtype security_type: str or ~azure.mgmt.compute.v2023_04_02.models.DiskSecurityTypes
        :keyword secure_vm_disk_encryption_set_id: ResourceId of the disk encryption set associated to
         Confidential VM supported disk encrypted with customer managed key.
        :paramtype secure_vm_disk_encryption_set_id: str
        """
        super().__init__(**kwargs)
        self.security_type = security_type
        self.secure_vm_disk_encryption_set_id = secure_vm_disk_encryption_set_id


class DiskSku(_serialization.Model):
    """The disks sku name. Can be Standard_LRS, Premium_LRS, StandardSSD_LRS, UltraSSD_LRS,
    Premium_ZRS, StandardSSD_ZRS, or PremiumV2_LRS.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar name: The sku name. Known values are: "Standard_LRS", "Premium_LRS", "StandardSSD_LRS",
     "UltraSSD_LRS", "Premium_ZRS", "StandardSSD_ZRS", and "PremiumV2_LRS".
    :vartype name: str or ~azure.mgmt.compute.v2023_04_02.models.DiskStorageAccountTypes
    :ivar tier: The sku tier.
    :vartype tier: str
    """

    _validation = {
        "tier": {"readonly": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "tier": {"key": "tier", "type": "str"},
    }

    def __init__(self, *, name: Optional[Union[str, "_models.DiskStorageAccountTypes"]] = None, **kwargs: Any) -> None:
        """
        :keyword name: The sku name. Known values are: "Standard_LRS", "Premium_LRS",
         "StandardSSD_LRS", "UltraSSD_LRS", "Premium_ZRS", "StandardSSD_ZRS", and "PremiumV2_LRS".
        :paramtype name: str or ~azure.mgmt.compute.v2023_04_02.models.DiskStorageAccountTypes
        """
        super().__init__(**kwargs)
        self.name = name
        self.tier = None


class DiskUpdate(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Disk update resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar sku: The disks sku name. Can be Standard_LRS, Premium_LRS, StandardSSD_LRS, UltraSSD_LRS,
     Premium_ZRS, StandardSSD_ZRS, or PremiumV2_LRS.
    :vartype sku: ~azure.mgmt.compute.v2023_04_02.models.DiskSku
    :ivar os_type: the Operating System type. Known values are: "Windows" and "Linux".
    :vartype os_type: str or ~azure.mgmt.compute.v2023_04_02.models.OperatingSystemTypes
    :ivar disk_size_gb: If creationData.createOption is Empty, this field is mandatory and it
     indicates the size of the disk to create. If this field is present for updates or creation with
     other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a
     running VM, and can only increase the disk's size.
    :vartype disk_size_gb: int
    :ivar encryption_settings_collection: Encryption settings collection used be Azure Disk
     Encryption, can contain multiple encryption settings per disk or snapshot.
    :vartype encryption_settings_collection:
     ~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsCollection
    :ivar disk_iops_read_write: The number of IOPS allowed for this disk; only settable for
     UltraSSD disks. One operation can transfer between 4k and 256k bytes.
    :vartype disk_iops_read_write: int
    :ivar disk_m_bps_read_write: The bandwidth allowed for this disk; only settable for UltraSSD
     disks. MBps means millions of bytes per second - MB here uses the ISO notation, of powers of
     10.
    :vartype disk_m_bps_read_write: int
    :ivar disk_iops_read_only: The total number of IOPS that will be allowed across all VMs
     mounting the shared disk as ReadOnly. One operation can transfer between 4k and 256k bytes.
    :vartype disk_iops_read_only: int
    :ivar disk_m_bps_read_only: The total throughput (MBps) that will be allowed across all VMs
     mounting the shared disk as ReadOnly. MBps means millions of bytes per second - MB here uses
     the ISO notation, of powers of 10.
    :vartype disk_m_bps_read_only: int
    :ivar max_shares: The maximum number of VMs that can attach to the disk at the same time. Value
     greater than one indicates a disk that can be mounted on multiple VMs at the same time.
    :vartype max_shares: int
    :ivar encryption: Encryption property can be used to encrypt data at rest with customer managed
     keys or platform managed keys.
    :vartype encryption: ~azure.mgmt.compute.v2023_04_02.models.Encryption
    :ivar network_access_policy: Policy for accessing the disk via network. Known values are:
     "AllowAll", "AllowPrivate", and "DenyAll".
    :vartype network_access_policy: str or
     ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
    :ivar disk_access_id: ARM id of the DiskAccess resource for using private endpoints on disks.
    :vartype disk_access_id: str
    :ivar tier: Performance tier of the disk (e.g, P4, S10) as described here:
     https://azure.microsoft.com/en-us/pricing/details/managed-disks/. Does not apply to Ultra
     disks.
    :vartype tier: str
    :ivar bursting_enabled: Set to true to enable bursting beyond the provisioned performance
     target of the disk. Bursting is disabled by default. Does not apply to Ultra disks.
    :vartype bursting_enabled: bool
    :ivar purchase_plan: Purchase plan information to be added on the OS disk.
    :vartype purchase_plan: ~azure.mgmt.compute.v2023_04_02.models.PurchasePlan
    :ivar supported_capabilities: List of supported capabilities to be added on the OS disk.
    :vartype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
    :ivar property_updates_in_progress: Properties of the disk for which update is pending.
    :vartype property_updates_in_progress:
     ~azure.mgmt.compute.v2023_04_02.models.PropertyUpdatesInProgress
    :ivar supports_hibernation: Indicates the OS on a disk supports hibernation.
    :vartype supports_hibernation: bool
    :ivar public_network_access: Policy for controlling export on the disk. Known values are:
     "Enabled" and "Disabled".
    :vartype public_network_access: str or
     ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
    :ivar data_access_auth_mode: Additional authentication requirements when exporting or uploading
     to a disk or snapshot. Known values are: "AzureActiveDirectory" and "None".
    :vartype data_access_auth_mode: str or
     ~azure.mgmt.compute.v2023_04_02.models.DataAccessAuthMode
    :ivar optimized_for_frequent_attach: Setting this property to true improves reliability and
     performance of data disks that are frequently (more than 5 times a day) by detached from one
     virtual machine and attached to another. This property should not be set for disks that are not
     detached and attached frequently as it causes the disks to not align with the fault domain of
     the virtual machine.
    :vartype optimized_for_frequent_attach: bool
    """

    _validation = {
        "property_updates_in_progress": {"readonly": True},
    }

    _attribute_map = {
        "tags": {"key": "tags", "type": "{str}"},
        "sku": {"key": "sku", "type": "DiskSku"},
        "os_type": {"key": "properties.osType", "type": "str"},
        "disk_size_gb": {"key": "properties.diskSizeGB", "type": "int"},
        "encryption_settings_collection": {
            "key": "properties.encryptionSettingsCollection",
            "type": "EncryptionSettingsCollection",
        },
        "disk_iops_read_write": {"key": "properties.diskIOPSReadWrite", "type": "int"},
        "disk_m_bps_read_write": {"key": "properties.diskMBpsReadWrite", "type": "int"},
        "disk_iops_read_only": {"key": "properties.diskIOPSReadOnly", "type": "int"},
        "disk_m_bps_read_only": {"key": "properties.diskMBpsReadOnly", "type": "int"},
        "max_shares": {"key": "properties.maxShares", "type": "int"},
        "encryption": {"key": "properties.encryption", "type": "Encryption"},
        "network_access_policy": {"key": "properties.networkAccessPolicy", "type": "str"},
        "disk_access_id": {"key": "properties.diskAccessId", "type": "str"},
        "tier": {"key": "properties.tier", "type": "str"},
        "bursting_enabled": {"key": "properties.burstingEnabled", "type": "bool"},
        "purchase_plan": {"key": "properties.purchasePlan", "type": "PurchasePlan"},
        "supported_capabilities": {"key": "properties.supportedCapabilities", "type": "SupportedCapabilities"},
        "property_updates_in_progress": {
            "key": "properties.propertyUpdatesInProgress",
            "type": "PropertyUpdatesInProgress",
        },
        "supports_hibernation": {"key": "properties.supportsHibernation", "type": "bool"},
        "public_network_access": {"key": "properties.publicNetworkAccess", "type": "str"},
        "data_access_auth_mode": {"key": "properties.dataAccessAuthMode", "type": "str"},
        "optimized_for_frequent_attach": {"key": "properties.optimizedForFrequentAttach", "type": "bool"},
    }

    def __init__(
        self,
        *,
        tags: Optional[Dict[str, str]] = None,
        sku: Optional["_models.DiskSku"] = None,
        os_type: Optional[Union[str, "_models.OperatingSystemTypes"]] = None,
        disk_size_gb: Optional[int] = None,
        encryption_settings_collection: Optional["_models.EncryptionSettingsCollection"] = None,
        disk_iops_read_write: Optional[int] = None,
        disk_m_bps_read_write: Optional[int] = None,
        disk_iops_read_only: Optional[int] = None,
        disk_m_bps_read_only: Optional[int] = None,
        max_shares: Optional[int] = None,
        encryption: Optional["_models.Encryption"] = None,
        network_access_policy: Optional[Union[str, "_models.NetworkAccessPolicy"]] = None,
        disk_access_id: Optional[str] = None,
        tier: Optional[str] = None,
        bursting_enabled: Optional[bool] = None,
        purchase_plan: Optional["_models.PurchasePlan"] = None,
        supported_capabilities: Optional["_models.SupportedCapabilities"] = None,
        supports_hibernation: Optional[bool] = None,
        public_network_access: Optional[Union[str, "_models.PublicNetworkAccess"]] = None,
        data_access_auth_mode: Optional[Union[str, "_models.DataAccessAuthMode"]] = None,
        optimized_for_frequent_attach: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword sku: The disks sku name. Can be Standard_LRS, Premium_LRS, StandardSSD_LRS,
         UltraSSD_LRS, Premium_ZRS, StandardSSD_ZRS, or PremiumV2_LRS.
        :paramtype sku: ~azure.mgmt.compute.v2023_04_02.models.DiskSku
        :keyword os_type: the Operating System type. Known values are: "Windows" and "Linux".
        :paramtype os_type: str or ~azure.mgmt.compute.v2023_04_02.models.OperatingSystemTypes
        :keyword disk_size_gb: If creationData.createOption is Empty, this field is mandatory and it
         indicates the size of the disk to create. If this field is present for updates or creation with
         other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a
         running VM, and can only increase the disk's size.
        :paramtype disk_size_gb: int
        :keyword encryption_settings_collection: Encryption settings collection used be Azure Disk
         Encryption, can contain multiple encryption settings per disk or snapshot.
        :paramtype encryption_settings_collection:
         ~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsCollection
        :keyword disk_iops_read_write: The number of IOPS allowed for this disk; only settable for
         UltraSSD disks. One operation can transfer between 4k and 256k bytes.
        :paramtype disk_iops_read_write: int
        :keyword disk_m_bps_read_write: The bandwidth allowed for this disk; only settable for UltraSSD
         disks. MBps means millions of bytes per second - MB here uses the ISO notation, of powers of
         10.
        :paramtype disk_m_bps_read_write: int
        :keyword disk_iops_read_only: The total number of IOPS that will be allowed across all VMs
         mounting the shared disk as ReadOnly. One operation can transfer between 4k and 256k bytes.
        :paramtype disk_iops_read_only: int
        :keyword disk_m_bps_read_only: The total throughput (MBps) that will be allowed across all VMs
         mounting the shared disk as ReadOnly. MBps means millions of bytes per second - MB here uses
         the ISO notation, of powers of 10.
        :paramtype disk_m_bps_read_only: int
        :keyword max_shares: The maximum number of VMs that can attach to the disk at the same time.
         Value greater than one indicates a disk that can be mounted on multiple VMs at the same time.
        :paramtype max_shares: int
        :keyword encryption: Encryption property can be used to encrypt data at rest with customer
         managed keys or platform managed keys.
        :paramtype encryption: ~azure.mgmt.compute.v2023_04_02.models.Encryption
        :keyword network_access_policy: Policy for accessing the disk via network. Known values are:
         "AllowAll", "AllowPrivate", and "DenyAll".
        :paramtype network_access_policy: str or
         ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
        :keyword disk_access_id: ARM id of the DiskAccess resource for using private endpoints on
         disks.
        :paramtype disk_access_id: str
        :keyword tier: Performance tier of the disk (e.g, P4, S10) as described here:
         https://azure.microsoft.com/en-us/pricing/details/managed-disks/. Does not apply to Ultra
         disks.
        :paramtype tier: str
        :keyword bursting_enabled: Set to true to enable bursting beyond the provisioned performance
         target of the disk. Bursting is disabled by default. Does not apply to Ultra disks.
        :paramtype bursting_enabled: bool
        :keyword purchase_plan: Purchase plan information to be added on the OS disk.
        :paramtype purchase_plan: ~azure.mgmt.compute.v2023_04_02.models.PurchasePlan
        :keyword supported_capabilities: List of supported capabilities to be added on the OS disk.
        :paramtype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
        :keyword supports_hibernation: Indicates the OS on a disk supports hibernation.
        :paramtype supports_hibernation: bool
        :keyword public_network_access: Policy for controlling export on the disk. Known values are:
         "Enabled" and "Disabled".
        :paramtype public_network_access: str or
         ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
        :keyword data_access_auth_mode: Additional authentication requirements when exporting or
         uploading to a disk or snapshot. Known values are: "AzureActiveDirectory" and "None".
        :paramtype data_access_auth_mode: str or
         ~azure.mgmt.compute.v2023_04_02.models.DataAccessAuthMode
        :keyword optimized_for_frequent_attach: Setting this property to true improves reliability and
         performance of data disks that are frequently (more than 5 times a day) by detached from one
         virtual machine and attached to another. This property should not be set for disks that are not
         detached and attached frequently as it causes the disks to not align with the fault domain of
         the virtual machine.
        :paramtype optimized_for_frequent_attach: bool
        """
        super().__init__(**kwargs)
        self.tags = tags
        self.sku = sku
        self.os_type = os_type
        self.disk_size_gb = disk_size_gb
        self.encryption_settings_collection = encryption_settings_collection
        self.disk_iops_read_write = disk_iops_read_write
        self.disk_m_bps_read_write = disk_m_bps_read_write
        self.disk_iops_read_only = disk_iops_read_only
        self.disk_m_bps_read_only = disk_m_bps_read_only
        self.max_shares = max_shares
        self.encryption = encryption
        self.network_access_policy = network_access_policy
        self.disk_access_id = disk_access_id
        self.tier = tier
        self.bursting_enabled = bursting_enabled
        self.purchase_plan = purchase_plan
        self.supported_capabilities = supported_capabilities
        self.property_updates_in_progress = None
        self.supports_hibernation = supports_hibernation
        self.public_network_access = public_network_access
        self.data_access_auth_mode = data_access_auth_mode
        self.optimized_for_frequent_attach = optimized_for_frequent_attach


class Encryption(_serialization.Model):
    """Encryption at rest settings for disk or snapshot.

    :ivar disk_encryption_set_id: ResourceId of the disk encryption set to use for enabling
     encryption at rest.
    :vartype disk_encryption_set_id: str
    :ivar type: The type of key used to encrypt the data of the disk. Known values are:
     "EncryptionAtRestWithPlatformKey", "EncryptionAtRestWithCustomerKey", and
     "EncryptionAtRestWithPlatformAndCustomerKeys".
    :vartype type: str or ~azure.mgmt.compute.v2023_04_02.models.EncryptionType
    """

    _attribute_map = {
        "disk_encryption_set_id": {"key": "diskEncryptionSetId", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        disk_encryption_set_id: Optional[str] = None,
        type: Optional[Union[str, "_models.EncryptionType"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword disk_encryption_set_id: ResourceId of the disk encryption set to use for enabling
         encryption at rest.
        :paramtype disk_encryption_set_id: str
        :keyword type: The type of key used to encrypt the data of the disk. Known values are:
         "EncryptionAtRestWithPlatformKey", "EncryptionAtRestWithCustomerKey", and
         "EncryptionAtRestWithPlatformAndCustomerKeys".
        :paramtype type: str or ~azure.mgmt.compute.v2023_04_02.models.EncryptionType
        """
        super().__init__(**kwargs)
        self.disk_encryption_set_id = disk_encryption_set_id
        self.type = type


class EncryptionSetIdentity(_serialization.Model):
    """The managed identity for the disk encryption set. It should be given permission on the key
    vault before it can be used to encrypt disks.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The type of Managed Identity used by the DiskEncryptionSet. Only SystemAssigned is
     supported for new creations. Disk Encryption Sets can be updated with Identity type None during
     migration of subscription to a new Azure Active Directory tenant; it will cause the encrypted
     resources to lose access to the keys. Known values are: "SystemAssigned", "UserAssigned",
     "SystemAssigned, UserAssigned", and "None".
    :vartype type: str or ~azure.mgmt.compute.v2023_04_02.models.DiskEncryptionSetIdentityType
    :ivar principal_id: The object id of the Managed Identity Resource. This will be sent to the RP
     from ARM via the x-ms-identity-principal-id header in the PUT request if the resource has a
     systemAssigned(implicit) identity.
    :vartype principal_id: str
    :ivar tenant_id: The tenant id of the Managed Identity Resource. This will be sent to the RP
     from ARM via the x-ms-client-tenant-id header in the PUT request if the resource has a
     systemAssigned(implicit) identity.
    :vartype tenant_id: str
    :ivar user_assigned_identities: The list of user identities associated with the disk encryption
     set. The user identity dictionary key references will be ARM resource ids in the form:
     '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}'.
    :vartype user_assigned_identities: dict[str,
     ~azure.mgmt.compute.v2023_04_02.models.UserAssignedIdentitiesValue]
    """

    _validation = {
        "principal_id": {"readonly": True},
        "tenant_id": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "principal_id": {"key": "principalId", "type": "str"},
        "tenant_id": {"key": "tenantId", "type": "str"},
        "user_assigned_identities": {"key": "userAssignedIdentities", "type": "{UserAssignedIdentitiesValue}"},
    }

    def __init__(
        self,
        *,
        type: Optional[Union[str, "_models.DiskEncryptionSetIdentityType"]] = None,
        user_assigned_identities: Optional[Dict[str, "_models.UserAssignedIdentitiesValue"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: The type of Managed Identity used by the DiskEncryptionSet. Only SystemAssigned
         is supported for new creations. Disk Encryption Sets can be updated with Identity type None
         during migration of subscription to a new Azure Active Directory tenant; it will cause the
         encrypted resources to lose access to the keys. Known values are: "SystemAssigned",
         "UserAssigned", "SystemAssigned, UserAssigned", and "None".
        :paramtype type: str or ~azure.mgmt.compute.v2023_04_02.models.DiskEncryptionSetIdentityType
        :keyword user_assigned_identities: The list of user identities associated with the disk
         encryption set. The user identity dictionary key references will be ARM resource ids in the
         form:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}'.
        :paramtype user_assigned_identities: dict[str,
         ~azure.mgmt.compute.v2023_04_02.models.UserAssignedIdentitiesValue]
        """
        super().__init__(**kwargs)
        self.type = type
        self.principal_id = None
        self.tenant_id = None
        self.user_assigned_identities = user_assigned_identities


class EncryptionSettingsCollection(_serialization.Model):
    """Encryption settings for disk or snapshot.

    All required parameters must be populated in order to send to Azure.

    :ivar enabled: Set this flag to true and provide DiskEncryptionKey and optional
     KeyEncryptionKey to enable encryption. Set this flag to false and remove DiskEncryptionKey and
     KeyEncryptionKey to disable encryption. If EncryptionSettings is null in the request object,
     the existing settings remain unchanged. Required.
    :vartype enabled: bool
    :ivar encryption_settings: A collection of encryption settings, one for each disk volume.
    :vartype encryption_settings:
     list[~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsElement]
    :ivar encryption_settings_version: Describes what type of encryption is used for the disks.
     Once this field is set, it cannot be overwritten. '1.0' corresponds to Azure Disk Encryption
     with AAD app.'1.1' corresponds to Azure Disk Encryption.
    :vartype encryption_settings_version: str
    """

    _validation = {
        "enabled": {"required": True},
    }

    _attribute_map = {
        "enabled": {"key": "enabled", "type": "bool"},
        "encryption_settings": {"key": "encryptionSettings", "type": "[EncryptionSettingsElement]"},
        "encryption_settings_version": {"key": "encryptionSettingsVersion", "type": "str"},
    }

    def __init__(
        self,
        *,
        enabled: bool,
        encryption_settings: Optional[List["_models.EncryptionSettingsElement"]] = None,
        encryption_settings_version: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: Set this flag to true and provide DiskEncryptionKey and optional
         KeyEncryptionKey to enable encryption. Set this flag to false and remove DiskEncryptionKey and
         KeyEncryptionKey to disable encryption. If EncryptionSettings is null in the request object,
         the existing settings remain unchanged. Required.
        :paramtype enabled: bool
        :keyword encryption_settings: A collection of encryption settings, one for each disk volume.
        :paramtype encryption_settings:
         list[~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsElement]
        :keyword encryption_settings_version: Describes what type of encryption is used for the disks.
         Once this field is set, it cannot be overwritten. '1.0' corresponds to Azure Disk Encryption
         with AAD app.'1.1' corresponds to Azure Disk Encryption.
        :paramtype encryption_settings_version: str
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.encryption_settings = encryption_settings
        self.encryption_settings_version = encryption_settings_version


class EncryptionSettingsElement(_serialization.Model):
    """Encryption settings for one disk volume.

    :ivar disk_encryption_key: Key Vault Secret Url and vault id of the disk encryption key.
    :vartype disk_encryption_key: ~azure.mgmt.compute.v2023_04_02.models.KeyVaultAndSecretReference
    :ivar key_encryption_key: Key Vault Key Url and vault id of the key encryption key.
     KeyEncryptionKey is optional and when provided is used to unwrap the disk encryption key.
    :vartype key_encryption_key: ~azure.mgmt.compute.v2023_04_02.models.KeyVaultAndKeyReference
    """

    _attribute_map = {
        "disk_encryption_key": {"key": "diskEncryptionKey", "type": "KeyVaultAndSecretReference"},
        "key_encryption_key": {"key": "keyEncryptionKey", "type": "KeyVaultAndKeyReference"},
    }

    def __init__(
        self,
        *,
        disk_encryption_key: Optional["_models.KeyVaultAndSecretReference"] = None,
        key_encryption_key: Optional["_models.KeyVaultAndKeyReference"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword disk_encryption_key: Key Vault Secret Url and vault id of the disk encryption key.
        :paramtype disk_encryption_key:
         ~azure.mgmt.compute.v2023_04_02.models.KeyVaultAndSecretReference
        :keyword key_encryption_key: Key Vault Key Url and vault id of the key encryption key.
         KeyEncryptionKey is optional and when provided is used to unwrap the disk encryption key.
        :paramtype key_encryption_key: ~azure.mgmt.compute.v2023_04_02.models.KeyVaultAndKeyReference
        """
        super().__init__(**kwargs)
        self.disk_encryption_key = disk_encryption_key
        self.key_encryption_key = key_encryption_key


class ExtendedLocation(_serialization.Model):
    """The complex type of the extended location.

    :ivar name: The name of the extended location.
    :vartype name: str
    :ivar type: The type of the extended location. "EdgeZone"
    :vartype type: str or ~azure.mgmt.compute.v2023_04_02.models.ExtendedLocationTypes
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        type: Optional[Union[str, "_models.ExtendedLocationTypes"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: The name of the extended location.
        :paramtype name: str
        :keyword type: The type of the extended location. "EdgeZone"
        :paramtype type: str or ~azure.mgmt.compute.v2023_04_02.models.ExtendedLocationTypes
        """
        super().__init__(**kwargs)
        self.name = name
        self.type = type


class GrantAccessData(_serialization.Model):
    """Data used for requesting a SAS.

    All required parameters must be populated in order to send to Azure.

    :ivar access: Required. Known values are: "None", "Read", and "Write".
    :vartype access: str or ~azure.mgmt.compute.v2023_04_02.models.AccessLevel
    :ivar duration_in_seconds: Time duration in seconds until the SAS access expires. Required.
    :vartype duration_in_seconds: int
    :ivar get_secure_vm_guest_state_sas: Set this flag to true to get additional SAS for VM guest
     state.
    :vartype get_secure_vm_guest_state_sas: bool
    :ivar file_format: Used to specify the file format when making request for SAS on a VHDX file
     format snapshot. Known values are: "VHD" and "VHDX".
    :vartype file_format: str or ~azure.mgmt.compute.v2023_04_02.models.FileFormat
    """

    _validation = {
        "access": {"required": True},
        "duration_in_seconds": {"required": True},
    }

    _attribute_map = {
        "access": {"key": "access", "type": "str"},
        "duration_in_seconds": {"key": "durationInSeconds", "type": "int"},
        "get_secure_vm_guest_state_sas": {"key": "getSecureVMGuestStateSAS", "type": "bool"},
        "file_format": {"key": "fileFormat", "type": "str"},
    }

    def __init__(
        self,
        *,
        access: Union[str, "_models.AccessLevel"],
        duration_in_seconds: int,
        get_secure_vm_guest_state_sas: Optional[bool] = None,
        file_format: Optional[Union[str, "_models.FileFormat"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword access: Required. Known values are: "None", "Read", and "Write".
        :paramtype access: str or ~azure.mgmt.compute.v2023_04_02.models.AccessLevel
        :keyword duration_in_seconds: Time duration in seconds until the SAS access expires. Required.
        :paramtype duration_in_seconds: int
        :keyword get_secure_vm_guest_state_sas: Set this flag to true to get additional SAS for VM
         guest state.
        :paramtype get_secure_vm_guest_state_sas: bool
        :keyword file_format: Used to specify the file format when making request for SAS on a VHDX
         file format snapshot. Known values are: "VHD" and "VHDX".
        :paramtype file_format: str or ~azure.mgmt.compute.v2023_04_02.models.FileFormat
        """
        super().__init__(**kwargs)
        self.access = access
        self.duration_in_seconds = duration_in_seconds
        self.get_secure_vm_guest_state_sas = get_secure_vm_guest_state_sas
        self.file_format = file_format


class ImageDiskReference(_serialization.Model):
    """The source image used for creating the disk.

    :ivar id: A relative uri containing either a Platform Image Repository, user image, or Azure
     Compute Gallery image reference.
    :vartype id: str
    :ivar shared_gallery_image_id: A relative uri containing a direct shared Azure Compute Gallery
     image reference.
    :vartype shared_gallery_image_id: str
    :ivar community_gallery_image_id: A relative uri containing a community Azure Compute Gallery
     image reference.
    :vartype community_gallery_image_id: str
    :ivar lun: If the disk is created from an image's data disk, this is an index that indicates
     which of the data disks in the image to use. For OS disks, this field is null.
    :vartype lun: int
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "shared_gallery_image_id": {"key": "sharedGalleryImageId", "type": "str"},
        "community_gallery_image_id": {"key": "communityGalleryImageId", "type": "str"},
        "lun": {"key": "lun", "type": "int"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        shared_gallery_image_id: Optional[str] = None,
        community_gallery_image_id: Optional[str] = None,
        lun: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: A relative uri containing either a Platform Image Repository, user image, or Azure
         Compute Gallery image reference.
        :paramtype id: str
        :keyword shared_gallery_image_id: A relative uri containing a direct shared Azure Compute
         Gallery image reference.
        :paramtype shared_gallery_image_id: str
        :keyword community_gallery_image_id: A relative uri containing a community Azure Compute
         Gallery image reference.
        :paramtype community_gallery_image_id: str
        :keyword lun: If the disk is created from an image's data disk, this is an index that indicates
         which of the data disks in the image to use. For OS disks, this field is null.
        :paramtype lun: int
        """
        super().__init__(**kwargs)
        self.id = id
        self.shared_gallery_image_id = shared_gallery_image_id
        self.community_gallery_image_id = community_gallery_image_id
        self.lun = lun


class InnerError(_serialization.Model):
    """Inner error details.

    :ivar exceptiontype: The exception type.
    :vartype exceptiontype: str
    :ivar errordetail: The internal error message or exception dump.
    :vartype errordetail: str
    """

    _attribute_map = {
        "exceptiontype": {"key": "exceptiontype", "type": "str"},
        "errordetail": {"key": "errordetail", "type": "str"},
    }

    def __init__(
        self, *, exceptiontype: Optional[str] = None, errordetail: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword exceptiontype: The exception type.
        :paramtype exceptiontype: str
        :keyword errordetail: The internal error message or exception dump.
        :paramtype errordetail: str
        """
        super().__init__(**kwargs)
        self.exceptiontype = exceptiontype
        self.errordetail = errordetail


class KeyForDiskEncryptionSet(_serialization.Model):
    """Key Vault Key Url to be used for server side encryption of Managed Disks and Snapshots.

    All required parameters must be populated in order to send to Azure.

    :ivar source_vault: Resource id of the KeyVault containing the key or secret. This property is
     optional and cannot be used if the KeyVault subscription is not the same as the Disk Encryption
     Set subscription.
    :vartype source_vault: ~azure.mgmt.compute.v2023_04_02.models.SourceVault
    :ivar key_url: Fully versioned Key Url pointing to a key in KeyVault. Version segment of the
     Url is required regardless of rotationToLatestKeyVersionEnabled value. Required.
    :vartype key_url: str
    """

    _validation = {
        "key_url": {"required": True},
    }

    _attribute_map = {
        "source_vault": {"key": "sourceVault", "type": "SourceVault"},
        "key_url": {"key": "keyUrl", "type": "str"},
    }

    def __init__(self, *, key_url: str, source_vault: Optional["_models.SourceVault"] = None, **kwargs: Any) -> None:
        """
        :keyword source_vault: Resource id of the KeyVault containing the key or secret. This property
         is optional and cannot be used if the KeyVault subscription is not the same as the Disk
         Encryption Set subscription.
        :paramtype source_vault: ~azure.mgmt.compute.v2023_04_02.models.SourceVault
        :keyword key_url: Fully versioned Key Url pointing to a key in KeyVault. Version segment of the
         Url is required regardless of rotationToLatestKeyVersionEnabled value. Required.
        :paramtype key_url: str
        """
        super().__init__(**kwargs)
        self.source_vault = source_vault
        self.key_url = key_url


class KeyVaultAndKeyReference(_serialization.Model):
    """Key Vault Key Url and vault id of KeK, KeK is optional and when provided is used to unwrap the
    encryptionKey.

    All required parameters must be populated in order to send to Azure.

    :ivar source_vault: Resource id of the KeyVault containing the key or secret. Required.
    :vartype source_vault: ~azure.mgmt.compute.v2023_04_02.models.SourceVault
    :ivar key_url: Url pointing to a key or secret in KeyVault. Required.
    :vartype key_url: str
    """

    _validation = {
        "source_vault": {"required": True},
        "key_url": {"required": True},
    }

    _attribute_map = {
        "source_vault": {"key": "sourceVault", "type": "SourceVault"},
        "key_url": {"key": "keyUrl", "type": "str"},
    }

    def __init__(self, *, source_vault: "_models.SourceVault", key_url: str, **kwargs: Any) -> None:
        """
        :keyword source_vault: Resource id of the KeyVault containing the key or secret. Required.
        :paramtype source_vault: ~azure.mgmt.compute.v2023_04_02.models.SourceVault
        :keyword key_url: Url pointing to a key or secret in KeyVault. Required.
        :paramtype key_url: str
        """
        super().__init__(**kwargs)
        self.source_vault = source_vault
        self.key_url = key_url


class KeyVaultAndSecretReference(_serialization.Model):
    """Key Vault Secret Url and vault id of the encryption key.

    All required parameters must be populated in order to send to Azure.

    :ivar source_vault: Resource id of the KeyVault containing the key or secret. Required.
    :vartype source_vault: ~azure.mgmt.compute.v2023_04_02.models.SourceVault
    :ivar secret_url: Url pointing to a key or secret in KeyVault. Required.
    :vartype secret_url: str
    """

    _validation = {
        "source_vault": {"required": True},
        "secret_url": {"required": True},
    }

    _attribute_map = {
        "source_vault": {"key": "sourceVault", "type": "SourceVault"},
        "secret_url": {"key": "secretUrl", "type": "str"},
    }

    def __init__(self, *, source_vault: "_models.SourceVault", secret_url: str, **kwargs: Any) -> None:
        """
        :keyword source_vault: Resource id of the KeyVault containing the key or secret. Required.
        :paramtype source_vault: ~azure.mgmt.compute.v2023_04_02.models.SourceVault
        :keyword secret_url: Url pointing to a key or secret in KeyVault. Required.
        :paramtype secret_url: str
        """
        super().__init__(**kwargs)
        self.source_vault = source_vault
        self.secret_url = secret_url


class PrivateEndpoint(_serialization.Model):
    """The Private Endpoint resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The ARM identifier for Private Endpoint.
    :vartype id: str
    """

    _validation = {
        "id": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.id = None


class PrivateEndpointConnection(_serialization.Model):
    """The Private Endpoint Connection resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: private endpoint connection Id.
    :vartype id: str
    :ivar name: private endpoint connection name.
    :vartype name: str
    :ivar type: private endpoint connection type.
    :vartype type: str
    :ivar private_endpoint: The resource of private end point.
    :vartype private_endpoint: ~azure.mgmt.compute.v2023_04_02.models.PrivateEndpoint
    :ivar private_link_service_connection_state: A collection of information about the state of the
     connection between DiskAccess and Virtual Network.
    :vartype private_link_service_connection_state:
     ~azure.mgmt.compute.v2023_04_02.models.PrivateLinkServiceConnectionState
    :ivar provisioning_state: The provisioning state of the private endpoint connection resource.
     Known values are: "Succeeded", "Creating", "Deleting", and "Failed".
    :vartype provisioning_state: str or
     ~azure.mgmt.compute.v2023_04_02.models.PrivateEndpointConnectionProvisioningState
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "private_endpoint": {"readonly": True},
        "provisioning_state": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "private_endpoint": {"key": "properties.privateEndpoint", "type": "PrivateEndpoint"},
        "private_link_service_connection_state": {
            "key": "properties.privateLinkServiceConnectionState",
            "type": "PrivateLinkServiceConnectionState",
        },
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
    }

    def __init__(
        self,
        *,
        private_link_service_connection_state: Optional["_models.PrivateLinkServiceConnectionState"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword private_link_service_connection_state: A collection of information about the state of
         the connection between DiskAccess and Virtual Network.
        :paramtype private_link_service_connection_state:
         ~azure.mgmt.compute.v2023_04_02.models.PrivateLinkServiceConnectionState
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.private_endpoint = None
        self.private_link_service_connection_state = private_link_service_connection_state
        self.provisioning_state = None


class PrivateEndpointConnectionListResult(_serialization.Model):
    """A list of private link resources.

    :ivar value: Array of private endpoint connections.
    :vartype value: list[~azure.mgmt.compute.v2023_04_02.models.PrivateEndpointConnection]
    :ivar next_link: The uri to fetch the next page of snapshots. Call ListNext() with this to
     fetch the next page of snapshots.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[PrivateEndpointConnection]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.PrivateEndpointConnection"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Array of private endpoint connections.
        :paramtype value: list[~azure.mgmt.compute.v2023_04_02.models.PrivateEndpointConnection]
        :keyword next_link: The uri to fetch the next page of snapshots. Call ListNext() with this to
         fetch the next page of snapshots.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class PrivateLinkResource(_serialization.Model):
    """A private link resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: private link resource Id.
    :vartype id: str
    :ivar name: private link resource name.
    :vartype name: str
    :ivar type: private link resource type.
    :vartype type: str
    :ivar group_id: The private link resource group id.
    :vartype group_id: str
    :ivar required_members: The private link resource required member names.
    :vartype required_members: list[str]
    :ivar required_zone_names: The private link resource DNS zone name.
    :vartype required_zone_names: list[str]
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "group_id": {"readonly": True},
        "required_members": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "group_id": {"key": "properties.groupId", "type": "str"},
        "required_members": {"key": "properties.requiredMembers", "type": "[str]"},
        "required_zone_names": {"key": "properties.requiredZoneNames", "type": "[str]"},
    }

    def __init__(self, *, required_zone_names: Optional[List[str]] = None, **kwargs: Any) -> None:
        """
        :keyword required_zone_names: The private link resource DNS zone name.
        :paramtype required_zone_names: list[str]
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.group_id = None
        self.required_members = None
        self.required_zone_names = required_zone_names


class PrivateLinkResourceListResult(_serialization.Model):
    """A list of private link resources.

    :ivar value: Array of private link resources.
    :vartype value: list[~azure.mgmt.compute.v2023_04_02.models.PrivateLinkResource]
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[PrivateLinkResource]"},
    }

    def __init__(self, *, value: Optional[List["_models.PrivateLinkResource"]] = None, **kwargs: Any) -> None:
        """
        :keyword value: Array of private link resources.
        :paramtype value: list[~azure.mgmt.compute.v2023_04_02.models.PrivateLinkResource]
        """
        super().__init__(**kwargs)
        self.value = value


class PrivateLinkServiceConnectionState(_serialization.Model):
    """A collection of information about the state of the connection between service consumer and
    provider.

    :ivar status: Indicates whether the connection has been Approved/Rejected/Removed by the owner
     of the service. Known values are: "Pending", "Approved", and "Rejected".
    :vartype status: str or
     ~azure.mgmt.compute.v2023_04_02.models.PrivateEndpointServiceConnectionStatus
    :ivar description: The reason for approval/rejection of the connection.
    :vartype description: str
    :ivar actions_required: A message indicating if changes on the service provider require any
     updates on the consumer.
    :vartype actions_required: str
    """

    _attribute_map = {
        "status": {"key": "status", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "actions_required": {"key": "actionsRequired", "type": "str"},
    }

    def __init__(
        self,
        *,
        status: Optional[Union[str, "_models.PrivateEndpointServiceConnectionStatus"]] = None,
        description: Optional[str] = None,
        actions_required: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword status: Indicates whether the connection has been Approved/Rejected/Removed by the
         owner of the service. Known values are: "Pending", "Approved", and "Rejected".
        :paramtype status: str or
         ~azure.mgmt.compute.v2023_04_02.models.PrivateEndpointServiceConnectionStatus
        :keyword description: The reason for approval/rejection of the connection.
        :paramtype description: str
        :keyword actions_required: A message indicating if changes on the service provider require any
         updates on the consumer.
        :paramtype actions_required: str
        """
        super().__init__(**kwargs)
        self.status = status
        self.description = description
        self.actions_required = actions_required


class PropertyUpdatesInProgress(_serialization.Model):
    """Properties of the disk for which update is pending.

    :ivar target_tier: The target performance tier of the disk if a tier change operation is in
     progress.
    :vartype target_tier: str
    """

    _attribute_map = {
        "target_tier": {"key": "targetTier", "type": "str"},
    }

    def __init__(self, *, target_tier: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword target_tier: The target performance tier of the disk if a tier change operation is in
         progress.
        :paramtype target_tier: str
        """
        super().__init__(**kwargs)
        self.target_tier = target_tier


class PurchasePlan(_serialization.Model):
    """Used for establishing the purchase context of any 3rd Party artifact through MarketPlace.

    All required parameters must be populated in order to send to Azure.

    :ivar name: The plan ID. Required.
    :vartype name: str
    :ivar publisher: The publisher ID. Required.
    :vartype publisher: str
    :ivar product: Specifies the product of the image from the marketplace. This is the same value
     as Offer under the imageReference element. Required.
    :vartype product: str
    :ivar promotion_code: The Offer Promotion Code.
    :vartype promotion_code: str
    """

    _validation = {
        "name": {"required": True},
        "publisher": {"required": True},
        "product": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "publisher": {"key": "publisher", "type": "str"},
        "product": {"key": "product", "type": "str"},
        "promotion_code": {"key": "promotionCode", "type": "str"},
    }

    def __init__(
        self, *, name: str, publisher: str, product: str, promotion_code: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword name: The plan ID. Required.
        :paramtype name: str
        :keyword publisher: The publisher ID. Required.
        :paramtype publisher: str
        :keyword product: Specifies the product of the image from the marketplace. This is the same
         value as Offer under the imageReference element. Required.
        :paramtype product: str
        :keyword promotion_code: The Offer Promotion Code.
        :paramtype promotion_code: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.publisher = publisher
        self.product = product
        self.promotion_code = promotion_code


class ResourceUriList(_serialization.Model):
    """The List resources which are encrypted with the disk encryption set.

    All required parameters must be populated in order to send to Azure.

    :ivar value: A list of IDs or Owner IDs of resources which are encrypted with the disk
     encryption set. Required.
    :vartype value: list[str]
    :ivar next_link: The uri to fetch the next page of encrypted resources. Call ListNext() with
     this to fetch the next page of encrypted resources.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[str]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List[str], next_link: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword value: A list of IDs or Owner IDs of resources which are encrypted with the disk
         encryption set. Required.
        :paramtype value: list[str]
        :keyword next_link: The uri to fetch the next page of encrypted resources. Call ListNext() with
         this to fetch the next page of encrypted resources.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class ResourceWithOptionalLocation(_serialization.Model):
    """The Resource model definition with location property as optional.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar location: Resource location.
    :vartype location: str
    :ivar id: Resource Id.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "location": {"key": "location", "type": "str"},
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
    }

    def __init__(self, *, location: Optional[str] = None, tags: Optional[Dict[str, str]] = None, **kwargs: Any) -> None:
        """
        :keyword location: Resource location.
        :paramtype location: str
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        """
        super().__init__(**kwargs)
        self.location = location
        self.id = None
        self.name = None
        self.type = None
        self.tags = tags


class ShareInfoElement(_serialization.Model):
    """ShareInfoElement.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar vm_uri: A relative URI containing the ID of the VM that has the disk attached.
    :vartype vm_uri: str
    """

    _validation = {
        "vm_uri": {"readonly": True},
    }

    _attribute_map = {
        "vm_uri": {"key": "vmUri", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.vm_uri = None


class Snapshot(Resource):  # pylint: disable=too-many-instance-attributes
    """Snapshot resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar id: Resource Id.
    :vartype id: str
    :ivar name: Resource name.
    :vartype name: str
    :ivar type: Resource type.
    :vartype type: str
    :ivar location: Resource location. Required.
    :vartype location: str
    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar managed_by: Unused. Always Null.
    :vartype managed_by: str
    :ivar sku: The snapshots sku name. Can be Standard_LRS, Premium_LRS, or Standard_ZRS. This is
     an optional parameter for incremental snapshot and the default behavior is the SKU will be set
     to the same sku as the previous snapshot.
    :vartype sku: ~azure.mgmt.compute.v2023_04_02.models.SnapshotSku
    :ivar extended_location: The extended location where the snapshot will be created. Extended
     location cannot be changed.
    :vartype extended_location: ~azure.mgmt.compute.v2023_04_02.models.ExtendedLocation
    :ivar time_created: The time when the snapshot was created.
    :vartype time_created: ~datetime.datetime
    :ivar os_type: The Operating System type. Known values are: "Windows" and "Linux".
    :vartype os_type: str or ~azure.mgmt.compute.v2023_04_02.models.OperatingSystemTypes
    :ivar hyper_v_generation: The hypervisor generation of the Virtual Machine. Applicable to OS
     disks only. Known values are: "V1" and "V2".
    :vartype hyper_v_generation: str or ~azure.mgmt.compute.v2023_04_02.models.HyperVGeneration
    :ivar purchase_plan: Purchase plan information for the image from which the source disk for the
     snapshot was originally created.
    :vartype purchase_plan: ~azure.mgmt.compute.v2023_04_02.models.PurchasePlan
    :ivar supported_capabilities: List of supported capabilities for the image from which the
     source disk from the snapshot was originally created.
    :vartype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
    :ivar creation_data: Disk source information. CreationData information cannot be changed after
     the disk has been created.
    :vartype creation_data: ~azure.mgmt.compute.v2023_04_02.models.CreationData
    :ivar disk_size_gb: If creationData.createOption is Empty, this field is mandatory and it
     indicates the size of the disk to create. If this field is present for updates or creation with
     other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a
     running VM, and can only increase the disk's size.
    :vartype disk_size_gb: int
    :ivar disk_size_bytes: The size of the disk in bytes. This field is read only.
    :vartype disk_size_bytes: int
    :ivar disk_state: The state of the snapshot. Known values are: "Unattached", "Attached",
     "Reserved", "Frozen", "ActiveSAS", "ActiveSASFrozen", "ReadyToUpload", and "ActiveUpload".
    :vartype disk_state: str or ~azure.mgmt.compute.v2023_04_02.models.DiskState
    :ivar unique_id: Unique Guid identifying the resource.
    :vartype unique_id: str
    :ivar encryption_settings_collection: Encryption settings collection used be Azure Disk
     Encryption, can contain multiple encryption settings per disk or snapshot.
    :vartype encryption_settings_collection:
     ~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsCollection
    :ivar provisioning_state: The disk provisioning state.
    :vartype provisioning_state: str
    :ivar incremental: Whether a snapshot is incremental. Incremental snapshots on the same disk
     occupy less space than full snapshots and can be diffed.
    :vartype incremental: bool
    :ivar incremental_snapshot_family_id: Incremental snapshots for a disk share an incremental
     snapshot family id. The Get Page Range Diff API can only be called on incremental snapshots
     with the same family id.
    :vartype incremental_snapshot_family_id: str
    :ivar encryption: Encryption property can be used to encrypt data at rest with customer managed
     keys or platform managed keys.
    :vartype encryption: ~azure.mgmt.compute.v2023_04_02.models.Encryption
    :ivar network_access_policy: Policy for accessing the disk via network. Known values are:
     "AllowAll", "AllowPrivate", and "DenyAll".
    :vartype network_access_policy: str or
     ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
    :ivar disk_access_id: ARM id of the DiskAccess resource for using private endpoints on disks.
    :vartype disk_access_id: str
    :ivar security_profile: Contains the security related information for the resource.
    :vartype security_profile: ~azure.mgmt.compute.v2023_04_02.models.DiskSecurityProfile
    :ivar supports_hibernation: Indicates the OS on a snapshot supports hibernation.
    :vartype supports_hibernation: bool
    :ivar public_network_access: Policy for controlling export on the disk. Known values are:
     "Enabled" and "Disabled".
    :vartype public_network_access: str or
     ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
    :ivar completion_percent: Percentage complete for the background copy when a resource is
     created via the CopyStart operation.
    :vartype completion_percent: float
    :ivar copy_completion_error: Indicates the error details if the background copy of a resource
     created via the CopyStart operation fails.
    :vartype copy_completion_error: ~azure.mgmt.compute.v2023_04_02.models.CopyCompletionError
    :ivar data_access_auth_mode: Additional authentication requirements when exporting or uploading
     to a disk or snapshot. Known values are: "AzureActiveDirectory" and "None".
    :vartype data_access_auth_mode: str or
     ~azure.mgmt.compute.v2023_04_02.models.DataAccessAuthMode
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "location": {"required": True},
        "managed_by": {"readonly": True},
        "time_created": {"readonly": True},
        "disk_size_bytes": {"readonly": True},
        "disk_state": {"readonly": True},
        "unique_id": {"readonly": True},
        "provisioning_state": {"readonly": True},
        "incremental_snapshot_family_id": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "location": {"key": "location", "type": "str"},
        "tags": {"key": "tags", "type": "{str}"},
        "managed_by": {"key": "managedBy", "type": "str"},
        "sku": {"key": "sku", "type": "SnapshotSku"},
        "extended_location": {"key": "extendedLocation", "type": "ExtendedLocation"},
        "time_created": {"key": "properties.timeCreated", "type": "iso-8601"},
        "os_type": {"key": "properties.osType", "type": "str"},
        "hyper_v_generation": {"key": "properties.hyperVGeneration", "type": "str"},
        "purchase_plan": {"key": "properties.purchasePlan", "type": "PurchasePlan"},
        "supported_capabilities": {"key": "properties.supportedCapabilities", "type": "SupportedCapabilities"},
        "creation_data": {"key": "properties.creationData", "type": "CreationData"},
        "disk_size_gb": {"key": "properties.diskSizeGB", "type": "int"},
        "disk_size_bytes": {"key": "properties.diskSizeBytes", "type": "int"},
        "disk_state": {"key": "properties.diskState", "type": "str"},
        "unique_id": {"key": "properties.uniqueId", "type": "str"},
        "encryption_settings_collection": {
            "key": "properties.encryptionSettingsCollection",
            "type": "EncryptionSettingsCollection",
        },
        "provisioning_state": {"key": "properties.provisioningState", "type": "str"},
        "incremental": {"key": "properties.incremental", "type": "bool"},
        "incremental_snapshot_family_id": {"key": "properties.incrementalSnapshotFamilyId", "type": "str"},
        "encryption": {"key": "properties.encryption", "type": "Encryption"},
        "network_access_policy": {"key": "properties.networkAccessPolicy", "type": "str"},
        "disk_access_id": {"key": "properties.diskAccessId", "type": "str"},
        "security_profile": {"key": "properties.securityProfile", "type": "DiskSecurityProfile"},
        "supports_hibernation": {"key": "properties.supportsHibernation", "type": "bool"},
        "public_network_access": {"key": "properties.publicNetworkAccess", "type": "str"},
        "completion_percent": {"key": "properties.completionPercent", "type": "float"},
        "copy_completion_error": {"key": "properties.copyCompletionError", "type": "CopyCompletionError"},
        "data_access_auth_mode": {"key": "properties.dataAccessAuthMode", "type": "str"},
    }

    def __init__(  # pylint: disable=too-many-locals
        self,
        *,
        location: str,
        tags: Optional[Dict[str, str]] = None,
        sku: Optional["_models.SnapshotSku"] = None,
        extended_location: Optional["_models.ExtendedLocation"] = None,
        os_type: Optional[Union[str, "_models.OperatingSystemTypes"]] = None,
        hyper_v_generation: Optional[Union[str, "_models.HyperVGeneration"]] = None,
        purchase_plan: Optional["_models.PurchasePlan"] = None,
        supported_capabilities: Optional["_models.SupportedCapabilities"] = None,
        creation_data: Optional["_models.CreationData"] = None,
        disk_size_gb: Optional[int] = None,
        encryption_settings_collection: Optional["_models.EncryptionSettingsCollection"] = None,
        incremental: Optional[bool] = None,
        encryption: Optional["_models.Encryption"] = None,
        network_access_policy: Optional[Union[str, "_models.NetworkAccessPolicy"]] = None,
        disk_access_id: Optional[str] = None,
        security_profile: Optional["_models.DiskSecurityProfile"] = None,
        supports_hibernation: Optional[bool] = None,
        public_network_access: Optional[Union[str, "_models.PublicNetworkAccess"]] = None,
        completion_percent: Optional[float] = None,
        copy_completion_error: Optional["_models.CopyCompletionError"] = None,
        data_access_auth_mode: Optional[Union[str, "_models.DataAccessAuthMode"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword location: Resource location. Required.
        :paramtype location: str
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword sku: The snapshots sku name. Can be Standard_LRS, Premium_LRS, or Standard_ZRS. This
         is an optional parameter for incremental snapshot and the default behavior is the SKU will be
         set to the same sku as the previous snapshot.
        :paramtype sku: ~azure.mgmt.compute.v2023_04_02.models.SnapshotSku
        :keyword extended_location: The extended location where the snapshot will be created. Extended
         location cannot be changed.
        :paramtype extended_location: ~azure.mgmt.compute.v2023_04_02.models.ExtendedLocation
        :keyword os_type: The Operating System type. Known values are: "Windows" and "Linux".
        :paramtype os_type: str or ~azure.mgmt.compute.v2023_04_02.models.OperatingSystemTypes
        :keyword hyper_v_generation: The hypervisor generation of the Virtual Machine. Applicable to OS
         disks only. Known values are: "V1" and "V2".
        :paramtype hyper_v_generation: str or ~azure.mgmt.compute.v2023_04_02.models.HyperVGeneration
        :keyword purchase_plan: Purchase plan information for the image from which the source disk for
         the snapshot was originally created.
        :paramtype purchase_plan: ~azure.mgmt.compute.v2023_04_02.models.PurchasePlan
        :keyword supported_capabilities: List of supported capabilities for the image from which the
         source disk from the snapshot was originally created.
        :paramtype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
        :keyword creation_data: Disk source information. CreationData information cannot be changed
         after the disk has been created.
        :paramtype creation_data: ~azure.mgmt.compute.v2023_04_02.models.CreationData
        :keyword disk_size_gb: If creationData.createOption is Empty, this field is mandatory and it
         indicates the size of the disk to create. If this field is present for updates or creation with
         other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a
         running VM, and can only increase the disk's size.
        :paramtype disk_size_gb: int
        :keyword encryption_settings_collection: Encryption settings collection used be Azure Disk
         Encryption, can contain multiple encryption settings per disk or snapshot.
        :paramtype encryption_settings_collection:
         ~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsCollection
        :keyword incremental: Whether a snapshot is incremental. Incremental snapshots on the same disk
         occupy less space than full snapshots and can be diffed.
        :paramtype incremental: bool
        :keyword encryption: Encryption property can be used to encrypt data at rest with customer
         managed keys or platform managed keys.
        :paramtype encryption: ~azure.mgmt.compute.v2023_04_02.models.Encryption
        :keyword network_access_policy: Policy for accessing the disk via network. Known values are:
         "AllowAll", "AllowPrivate", and "DenyAll".
        :paramtype network_access_policy: str or
         ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
        :keyword disk_access_id: ARM id of the DiskAccess resource for using private endpoints on
         disks.
        :paramtype disk_access_id: str
        :keyword security_profile: Contains the security related information for the resource.
        :paramtype security_profile: ~azure.mgmt.compute.v2023_04_02.models.DiskSecurityProfile
        :keyword supports_hibernation: Indicates the OS on a snapshot supports hibernation.
        :paramtype supports_hibernation: bool
        :keyword public_network_access: Policy for controlling export on the disk. Known values are:
         "Enabled" and "Disabled".
        :paramtype public_network_access: str or
         ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
        :keyword completion_percent: Percentage complete for the background copy when a resource is
         created via the CopyStart operation.
        :paramtype completion_percent: float
        :keyword copy_completion_error: Indicates the error details if the background copy of a
         resource created via the CopyStart operation fails.
        :paramtype copy_completion_error: ~azure.mgmt.compute.v2023_04_02.models.CopyCompletionError
        :keyword data_access_auth_mode: Additional authentication requirements when exporting or
         uploading to a disk or snapshot. Known values are: "AzureActiveDirectory" and "None".
        :paramtype data_access_auth_mode: str or
         ~azure.mgmt.compute.v2023_04_02.models.DataAccessAuthMode
        """
        super().__init__(location=location, tags=tags, **kwargs)
        self.managed_by = None
        self.sku = sku
        self.extended_location = extended_location
        self.time_created = None
        self.os_type = os_type
        self.hyper_v_generation = hyper_v_generation
        self.purchase_plan = purchase_plan
        self.supported_capabilities = supported_capabilities
        self.creation_data = creation_data
        self.disk_size_gb = disk_size_gb
        self.disk_size_bytes = None
        self.disk_state = None
        self.unique_id = None
        self.encryption_settings_collection = encryption_settings_collection
        self.provisioning_state = None
        self.incremental = incremental
        self.incremental_snapshot_family_id = None
        self.encryption = encryption
        self.network_access_policy = network_access_policy
        self.disk_access_id = disk_access_id
        self.security_profile = security_profile
        self.supports_hibernation = supports_hibernation
        self.public_network_access = public_network_access
        self.completion_percent = completion_percent
        self.copy_completion_error = copy_completion_error
        self.data_access_auth_mode = data_access_auth_mode


class SnapshotList(_serialization.Model):
    """The List Snapshots operation response.

    All required parameters must be populated in order to send to Azure.

    :ivar value: A list of snapshots. Required.
    :vartype value: list[~azure.mgmt.compute.v2023_04_02.models.Snapshot]
    :ivar next_link: The uri to fetch the next page of snapshots. Call ListNext() with this to
     fetch the next page of snapshots.
    :vartype next_link: str
    """

    _validation = {
        "value": {"required": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[Snapshot]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: List["_models.Snapshot"], next_link: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword value: A list of snapshots. Required.
        :paramtype value: list[~azure.mgmt.compute.v2023_04_02.models.Snapshot]
        :keyword next_link: The uri to fetch the next page of snapshots. Call ListNext() with this to
         fetch the next page of snapshots.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class SnapshotSku(_serialization.Model):
    """The snapshots sku name. Can be Standard_LRS, Premium_LRS, or Standard_ZRS. This is an optional
    parameter for incremental snapshot and the default behavior is the SKU will be set to the same
    sku as the previous snapshot.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar name: The sku name. Known values are: "Standard_LRS", "Premium_LRS", and "Standard_ZRS".
    :vartype name: str or ~azure.mgmt.compute.v2023_04_02.models.SnapshotStorageAccountTypes
    :ivar tier: The sku tier.
    :vartype tier: str
    """

    _validation = {
        "tier": {"readonly": True},
    }

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "tier": {"key": "tier", "type": "str"},
    }

    def __init__(
        self, *, name: Optional[Union[str, "_models.SnapshotStorageAccountTypes"]] = None, **kwargs: Any
    ) -> None:
        """
        :keyword name: The sku name. Known values are: "Standard_LRS", "Premium_LRS", and
         "Standard_ZRS".
        :paramtype name: str or ~azure.mgmt.compute.v2023_04_02.models.SnapshotStorageAccountTypes
        """
        super().__init__(**kwargs)
        self.name = name
        self.tier = None


class SnapshotUpdate(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Snapshot update resource.

    :ivar tags: Resource tags.
    :vartype tags: dict[str, str]
    :ivar sku: The snapshots sku name. Can be Standard_LRS, Premium_LRS, or Standard_ZRS. This is
     an optional parameter for incremental snapshot and the default behavior is the SKU will be set
     to the same sku as the previous snapshot.
    :vartype sku: ~azure.mgmt.compute.v2023_04_02.models.SnapshotSku
    :ivar os_type: the Operating System type. Known values are: "Windows" and "Linux".
    :vartype os_type: str or ~azure.mgmt.compute.v2023_04_02.models.OperatingSystemTypes
    :ivar disk_size_gb: If creationData.createOption is Empty, this field is mandatory and it
     indicates the size of the disk to create. If this field is present for updates or creation with
     other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a
     running VM, and can only increase the disk's size.
    :vartype disk_size_gb: int
    :ivar encryption_settings_collection: Encryption settings collection used be Azure Disk
     Encryption, can contain multiple encryption settings per disk or snapshot.
    :vartype encryption_settings_collection:
     ~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsCollection
    :ivar encryption: Encryption property can be used to encrypt data at rest with customer managed
     keys or platform managed keys.
    :vartype encryption: ~azure.mgmt.compute.v2023_04_02.models.Encryption
    :ivar network_access_policy: Policy for accessing the disk via network. Known values are:
     "AllowAll", "AllowPrivate", and "DenyAll".
    :vartype network_access_policy: str or
     ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
    :ivar disk_access_id: ARM id of the DiskAccess resource for using private endpoints on disks.
    :vartype disk_access_id: str
    :ivar supports_hibernation: Indicates the OS on a snapshot supports hibernation.
    :vartype supports_hibernation: bool
    :ivar public_network_access: Policy for controlling export on the disk. Known values are:
     "Enabled" and "Disabled".
    :vartype public_network_access: str or
     ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
    :ivar data_access_auth_mode: Additional authentication requirements when exporting or uploading
     to a disk or snapshot. Known values are: "AzureActiveDirectory" and "None".
    :vartype data_access_auth_mode: str or
     ~azure.mgmt.compute.v2023_04_02.models.DataAccessAuthMode
    :ivar supported_capabilities: List of supported capabilities for the image from which the OS
     disk was created.
    :vartype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
    """

    _attribute_map = {
        "tags": {"key": "tags", "type": "{str}"},
        "sku": {"key": "sku", "type": "SnapshotSku"},
        "os_type": {"key": "properties.osType", "type": "str"},
        "disk_size_gb": {"key": "properties.diskSizeGB", "type": "int"},
        "encryption_settings_collection": {
            "key": "properties.encryptionSettingsCollection",
            "type": "EncryptionSettingsCollection",
        },
        "encryption": {"key": "properties.encryption", "type": "Encryption"},
        "network_access_policy": {"key": "properties.networkAccessPolicy", "type": "str"},
        "disk_access_id": {"key": "properties.diskAccessId", "type": "str"},
        "supports_hibernation": {"key": "properties.supportsHibernation", "type": "bool"},
        "public_network_access": {"key": "properties.publicNetworkAccess", "type": "str"},
        "data_access_auth_mode": {"key": "properties.dataAccessAuthMode", "type": "str"},
        "supported_capabilities": {"key": "properties.supportedCapabilities", "type": "SupportedCapabilities"},
    }

    def __init__(
        self,
        *,
        tags: Optional[Dict[str, str]] = None,
        sku: Optional["_models.SnapshotSku"] = None,
        os_type: Optional[Union[str, "_models.OperatingSystemTypes"]] = None,
        disk_size_gb: Optional[int] = None,
        encryption_settings_collection: Optional["_models.EncryptionSettingsCollection"] = None,
        encryption: Optional["_models.Encryption"] = None,
        network_access_policy: Optional[Union[str, "_models.NetworkAccessPolicy"]] = None,
        disk_access_id: Optional[str] = None,
        supports_hibernation: Optional[bool] = None,
        public_network_access: Optional[Union[str, "_models.PublicNetworkAccess"]] = None,
        data_access_auth_mode: Optional[Union[str, "_models.DataAccessAuthMode"]] = None,
        supported_capabilities: Optional["_models.SupportedCapabilities"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword tags: Resource tags.
        :paramtype tags: dict[str, str]
        :keyword sku: The snapshots sku name. Can be Standard_LRS, Premium_LRS, or Standard_ZRS. This
         is an optional parameter for incremental snapshot and the default behavior is the SKU will be
         set to the same sku as the previous snapshot.
        :paramtype sku: ~azure.mgmt.compute.v2023_04_02.models.SnapshotSku
        :keyword os_type: the Operating System type. Known values are: "Windows" and "Linux".
        :paramtype os_type: str or ~azure.mgmt.compute.v2023_04_02.models.OperatingSystemTypes
        :keyword disk_size_gb: If creationData.createOption is Empty, this field is mandatory and it
         indicates the size of the disk to create. If this field is present for updates or creation with
         other options, it indicates a resize. Resizes are only allowed if the disk is not attached to a
         running VM, and can only increase the disk's size.
        :paramtype disk_size_gb: int
        :keyword encryption_settings_collection: Encryption settings collection used be Azure Disk
         Encryption, can contain multiple encryption settings per disk or snapshot.
        :paramtype encryption_settings_collection:
         ~azure.mgmt.compute.v2023_04_02.models.EncryptionSettingsCollection
        :keyword encryption: Encryption property can be used to encrypt data at rest with customer
         managed keys or platform managed keys.
        :paramtype encryption: ~azure.mgmt.compute.v2023_04_02.models.Encryption
        :keyword network_access_policy: Policy for accessing the disk via network. Known values are:
         "AllowAll", "AllowPrivate", and "DenyAll".
        :paramtype network_access_policy: str or
         ~azure.mgmt.compute.v2023_04_02.models.NetworkAccessPolicy
        :keyword disk_access_id: ARM id of the DiskAccess resource for using private endpoints on
         disks.
        :paramtype disk_access_id: str
        :keyword supports_hibernation: Indicates the OS on a snapshot supports hibernation.
        :paramtype supports_hibernation: bool
        :keyword public_network_access: Policy for controlling export on the disk. Known values are:
         "Enabled" and "Disabled".
        :paramtype public_network_access: str or
         ~azure.mgmt.compute.v2023_04_02.models.PublicNetworkAccess
        :keyword data_access_auth_mode: Additional authentication requirements when exporting or
         uploading to a disk or snapshot. Known values are: "AzureActiveDirectory" and "None".
        :paramtype data_access_auth_mode: str or
         ~azure.mgmt.compute.v2023_04_02.models.DataAccessAuthMode
        :keyword supported_capabilities: List of supported capabilities for the image from which the OS
         disk was created.
        :paramtype supported_capabilities: ~azure.mgmt.compute.v2023_04_02.models.SupportedCapabilities
        """
        super().__init__(**kwargs)
        self.tags = tags
        self.sku = sku
        self.os_type = os_type
        self.disk_size_gb = disk_size_gb
        self.encryption_settings_collection = encryption_settings_collection
        self.encryption = encryption
        self.network_access_policy = network_access_policy
        self.disk_access_id = disk_access_id
        self.supports_hibernation = supports_hibernation
        self.public_network_access = public_network_access
        self.data_access_auth_mode = data_access_auth_mode
        self.supported_capabilities = supported_capabilities


class SourceVault(_serialization.Model):
    """The vault id is an Azure Resource Manager Resource id in the form
    /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.KeyVault/vaults/{vaultName}.

    :ivar id: Resource Id.
    :vartype id: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
    }

    def __init__(self, *, id: Optional[str] = None, **kwargs: Any) -> None:  # pylint: disable=redefined-builtin
        """
        :keyword id: Resource Id.
        :paramtype id: str
        """
        super().__init__(**kwargs)
        self.id = id


class SubResource(_serialization.Model):
    """SubResource.

    :ivar id: Resource Id.
    :vartype id: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
    }

    def __init__(self, *, id: Optional[str] = None, **kwargs: Any) -> None:  # pylint: disable=redefined-builtin
        """
        :keyword id: Resource Id.
        :paramtype id: str
        """
        super().__init__(**kwargs)
        self.id = id


class SubResourceReadOnly(_serialization.Model):
    """SubResourceReadOnly.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Resource Id.
    :vartype id: str
    """

    _validation = {
        "id": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.id = None


class SupportedCapabilities(_serialization.Model):
    """List of supported capabilities persisted on the disk resource for VM use.

    :ivar disk_controller_types: The disk controllers that an OS disk supports. If set it can be
     SCSI or SCSI, NVME or NVME, SCSI.
    :vartype disk_controller_types: str
    :ivar accelerated_network: True if the image from which the OS disk is created supports
     accelerated networking.
    :vartype accelerated_network: bool
    :ivar architecture: CPU architecture supported by an OS disk. Known values are: "x64" and
     "Arm64".
    :vartype architecture: str or ~azure.mgmt.compute.v2023_04_02.models.Architecture
    """

    _attribute_map = {
        "disk_controller_types": {"key": "diskControllerTypes", "type": "str"},
        "accelerated_network": {"key": "acceleratedNetwork", "type": "bool"},
        "architecture": {"key": "architecture", "type": "str"},
    }

    def __init__(
        self,
        *,
        disk_controller_types: Optional[str] = None,
        accelerated_network: Optional[bool] = None,
        architecture: Optional[Union[str, "_models.Architecture"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword disk_controller_types: The disk controllers that an OS disk supports. If set it can be
         SCSI or SCSI, NVME or NVME, SCSI.
        :paramtype disk_controller_types: str
        :keyword accelerated_network: True if the image from which the OS disk is created supports
         accelerated networking.
        :paramtype accelerated_network: bool
        :keyword architecture: CPU architecture supported by an OS disk. Known values are: "x64" and
         "Arm64".
        :paramtype architecture: str or ~azure.mgmt.compute.v2023_04_02.models.Architecture
        """
        super().__init__(**kwargs)
        self.disk_controller_types = disk_controller_types
        self.accelerated_network = accelerated_network
        self.architecture = architecture


class SystemData(_serialization.Model):
    """The system meta data relating to this resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar created_at: Specifies the time in UTC at which the Cloud Service (extended support)
     resource was created. :code:`<br />`Minimum api-version: 2022-04-04.
    :vartype created_at: ~datetime.datetime
    :ivar last_modified_at: Specifies the time in UTC at which the Cloud Service (extended support)
     resource was last modified. :code:`<br />`Minimum api-version: 2022-04-04.
    :vartype last_modified_at: ~datetime.datetime
    """

    _validation = {
        "created_at": {"readonly": True},
        "last_modified_at": {"readonly": True},
    }

    _attribute_map = {
        "created_at": {"key": "createdAt", "type": "iso-8601"},
        "last_modified_at": {"key": "lastModifiedAt", "type": "iso-8601"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.created_at = None
        self.last_modified_at = None


class UserAssignedIdentitiesValue(_serialization.Model):
    """UserAssignedIdentitiesValue.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar principal_id: The principal id of user assigned identity.
    :vartype principal_id: str
    :ivar client_id: The client id of user assigned identity.
    :vartype client_id: str
    """

    _validation = {
        "principal_id": {"readonly": True},
        "client_id": {"readonly": True},
    }

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "client_id": {"key": "clientId", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.principal_id = None
        self.client_id = None
