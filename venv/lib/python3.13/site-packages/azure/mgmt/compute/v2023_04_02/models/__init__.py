# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._models_py3 import AccessUri
from ._models_py3 import ApiError
from ._models_py3 import ApiErrorBase
from ._models_py3 import CopyCompletionError
from ._models_py3 import CreationData
from ._models_py3 import Disk
from ._models_py3 import DiskAccess
from ._models_py3 import DiskAccessList
from ._models_py3 import DiskAccessUpdate
from ._models_py3 import DiskEncryptionSet
from ._models_py3 import DiskEncryptionSetList
from ._models_py3 import DiskEncryptionSetUpdate
from ._models_py3 import DiskList
from ._models_py3 import DiskRestorePoint
from ._models_py3 import DiskRestorePointList
from ._models_py3 import DiskSecurityProfile
from ._models_py3 import DiskSku
from ._models_py3 import DiskUpdate
from ._models_py3 import Encryption
from ._models_py3 import EncryptionSetIdentity
from ._models_py3 import EncryptionSettingsCollection
from ._models_py3 import EncryptionSettingsElement
from ._models_py3 import ExtendedLocation
from ._models_py3 import GrantAccessData
from ._models_py3 import ImageDiskReference
from ._models_py3 import InnerError
from ._models_py3 import KeyForDiskEncryptionSet
from ._models_py3 import KeyVaultAndKeyReference
from ._models_py3 import KeyVaultAndSecretReference
from ._models_py3 import PrivateEndpoint
from ._models_py3 import PrivateEndpointConnection
from ._models_py3 import PrivateEndpointConnectionListResult
from ._models_py3 import PrivateLinkResource
from ._models_py3 import PrivateLinkResourceListResult
from ._models_py3 import PrivateLinkServiceConnectionState
from ._models_py3 import PropertyUpdatesInProgress
from ._models_py3 import ProxyOnlyResource
from ._models_py3 import PurchasePlan
from ._models_py3 import Resource
from ._models_py3 import ResourceUriList
from ._models_py3 import ResourceWithOptionalLocation
from ._models_py3 import ShareInfoElement
from ._models_py3 import Snapshot
from ._models_py3 import SnapshotList
from ._models_py3 import SnapshotSku
from ._models_py3 import SnapshotUpdate
from ._models_py3 import SourceVault
from ._models_py3 import SubResource
from ._models_py3 import SubResourceReadOnly
from ._models_py3 import SupportedCapabilities
from ._models_py3 import SystemData
from ._models_py3 import UserAssignedIdentitiesValue

from ._compute_management_client_enums import AccessLevel
from ._compute_management_client_enums import Architecture
from ._compute_management_client_enums import CopyCompletionErrorReason
from ._compute_management_client_enums import DataAccessAuthMode
from ._compute_management_client_enums import DiskCreateOption
from ._compute_management_client_enums import DiskEncryptionSetIdentityType
from ._compute_management_client_enums import DiskEncryptionSetType
from ._compute_management_client_enums import DiskSecurityTypes
from ._compute_management_client_enums import DiskState
from ._compute_management_client_enums import DiskStorageAccountTypes
from ._compute_management_client_enums import EncryptionType
from ._compute_management_client_enums import ExtendedLocationTypes
from ._compute_management_client_enums import FileFormat
from ._compute_management_client_enums import HyperVGeneration
from ._compute_management_client_enums import NetworkAccessPolicy
from ._compute_management_client_enums import OperatingSystemTypes
from ._compute_management_client_enums import PrivateEndpointConnectionProvisioningState
from ._compute_management_client_enums import PrivateEndpointServiceConnectionStatus
from ._compute_management_client_enums import PublicNetworkAccess
from ._compute_management_client_enums import SnapshotStorageAccountTypes
from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "AccessUri",
    "ApiError",
    "ApiErrorBase",
    "CopyCompletionError",
    "CreationData",
    "Disk",
    "DiskAccess",
    "DiskAccessList",
    "DiskAccessUpdate",
    "DiskEncryptionSet",
    "DiskEncryptionSetList",
    "DiskEncryptionSetUpdate",
    "DiskList",
    "DiskRestorePoint",
    "DiskRestorePointList",
    "DiskSecurityProfile",
    "DiskSku",
    "DiskUpdate",
    "Encryption",
    "EncryptionSetIdentity",
    "EncryptionSettingsCollection",
    "EncryptionSettingsElement",
    "ExtendedLocation",
    "GrantAccessData",
    "ImageDiskReference",
    "InnerError",
    "KeyForDiskEncryptionSet",
    "KeyVaultAndKeyReference",
    "KeyVaultAndSecretReference",
    "PrivateEndpoint",
    "PrivateEndpointConnection",
    "PrivateEndpointConnectionListResult",
    "PrivateLinkResource",
    "PrivateLinkResourceListResult",
    "PrivateLinkServiceConnectionState",
    "PropertyUpdatesInProgress",
    "ProxyOnlyResource",
    "PurchasePlan",
    "Resource",
    "ResourceUriList",
    "ResourceWithOptionalLocation",
    "ShareInfoElement",
    "Snapshot",
    "SnapshotList",
    "SnapshotSku",
    "SnapshotUpdate",
    "SourceVault",
    "SubResource",
    "SubResourceReadOnly",
    "SupportedCapabilities",
    "SystemData",
    "UserAssignedIdentitiesValue",
    "AccessLevel",
    "Architecture",
    "CopyCompletionErrorReason",
    "DataAccessAuthMode",
    "DiskCreateOption",
    "DiskEncryptionSetIdentityType",
    "DiskEncryptionSetType",
    "DiskSecurityTypes",
    "DiskState",
    "DiskStorageAccountTypes",
    "EncryptionType",
    "ExtendedLocationTypes",
    "FileFormat",
    "HyperVGeneration",
    "NetworkAccessPolicy",
    "OperatingSystemTypes",
    "PrivateEndpointConnectionProvisioningState",
    "PrivateEndpointServiceConnectionStatus",
    "PublicNetworkAccess",
    "SnapshotStorageAccountTypes",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
