# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._operations import Operations
from ._access_review_schedule_definitions_operations import AccessReviewScheduleDefinitionsOperations
from ._access_review_instances_operations import AccessReviewInstancesOperations
from ._access_review_instance_operations import AccessReviewInstanceOperations
from ._access_review_instance_decisions_operations import AccessReviewInstanceDecisionsOperations
from ._access_review_default_settings_operations import AccessReviewDefaultSettingsOperations
from ._access_review_schedule_definitions_assigned_for_my_approval_operations import (
    AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations,
)
from ._access_review_instances_assigned_for_my_approval_operations import (
    AccessReviewInstancesAssignedForMyApprovalOperations,
)
from ._access_review_instance_my_decisions_operations import AccessReviewInstanceMyDecisionsOperations

from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "Operations",
    "AccessReviewScheduleDefinitionsOperations",
    "AccessReviewInstancesOperations",
    "AccessReviewInstanceOperations",
    "AccessReviewInstanceDecisionsOperations",
    "AccessReviewDefaultSettingsOperations",
    "AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations",
    "AccessReviewInstancesAssignedForMyApprovalOperations",
    "AccessReviewInstanceMyDecisionsOperations",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
