# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import Any, List, Optional, TYPE_CHECKING, Union

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class ApprovalSettings(_serialization.Model):
    """The approval settings.

    :ivar is_approval_required: Determines whether approval is required or not.
    :vartype is_approval_required: bool
    :ivar is_approval_required_for_extension: Determines whether approval is required for
     assignment extension.
    :vartype is_approval_required_for_extension: bool
    :ivar is_requestor_justification_required: Determine whether requestor justification is
     required.
    :vartype is_requestor_justification_required: bool
    :ivar approval_mode: The type of rule. Known values are: "SingleStage", "Serial", "Parallel",
     and "NoApproval".
    :vartype approval_mode: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.ApprovalMode
    :ivar approval_stages: The approval stages of the request.
    :vartype approval_stages:
     list[~azure.mgmt.authorization.v2022_05_01_preview.models.ApprovalStage]
    """

    _attribute_map = {
        "is_approval_required": {"key": "isApprovalRequired", "type": "bool"},
        "is_approval_required_for_extension": {"key": "isApprovalRequiredForExtension", "type": "bool"},
        "is_requestor_justification_required": {"key": "isRequestorJustificationRequired", "type": "bool"},
        "approval_mode": {"key": "approvalMode", "type": "str"},
        "approval_stages": {"key": "approvalStages", "type": "[ApprovalStage]"},
    }

    def __init__(
        self,
        *,
        is_approval_required: Optional[bool] = None,
        is_approval_required_for_extension: Optional[bool] = None,
        is_requestor_justification_required: Optional[bool] = None,
        approval_mode: Optional[Union[str, "_models.ApprovalMode"]] = None,
        approval_stages: Optional[List["_models.ApprovalStage"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword is_approval_required: Determines whether approval is required or not.
        :paramtype is_approval_required: bool
        :keyword is_approval_required_for_extension: Determines whether approval is required for
         assignment extension.
        :paramtype is_approval_required_for_extension: bool
        :keyword is_requestor_justification_required: Determine whether requestor justification is
         required.
        :paramtype is_requestor_justification_required: bool
        :keyword approval_mode: The type of rule. Known values are: "SingleStage", "Serial",
         "Parallel", and "NoApproval".
        :paramtype approval_mode: str or
         ~azure.mgmt.authorization.v2022_05_01_preview.models.ApprovalMode
        :keyword approval_stages: The approval stages of the request.
        :paramtype approval_stages:
         list[~azure.mgmt.authorization.v2022_05_01_preview.models.ApprovalStage]
        """
        super().__init__(**kwargs)
        self.is_approval_required = is_approval_required
        self.is_approval_required_for_extension = is_approval_required_for_extension
        self.is_requestor_justification_required = is_requestor_justification_required
        self.approval_mode = approval_mode
        self.approval_stages = approval_stages


class ApprovalStage(_serialization.Model):
    """The approval stage.

    :ivar approval_stage_time_out_in_days: The time in days when approval request would be timed
     out.
    :vartype approval_stage_time_out_in_days: int
    :ivar is_approver_justification_required: Determines whether approver need to provide
     justification for his decision.
    :vartype is_approver_justification_required: bool
    :ivar escalation_time_in_minutes: The time in minutes when the approval request would be
     escalated if the primary approver does not approve.
    :vartype escalation_time_in_minutes: int
    :ivar primary_approvers: The primary approver of the request.
    :vartype primary_approvers: list[~azure.mgmt.authorization.v2022_05_01_preview.models.UserSet]
    :ivar is_escalation_enabled: The value determine whether escalation feature is enabled.
    :vartype is_escalation_enabled: bool
    :ivar escalation_approvers: The escalation approver of the request.
    :vartype escalation_approvers:
     list[~azure.mgmt.authorization.v2022_05_01_preview.models.UserSet]
    """

    _attribute_map = {
        "approval_stage_time_out_in_days": {"key": "approvalStageTimeOutInDays", "type": "int"},
        "is_approver_justification_required": {"key": "isApproverJustificationRequired", "type": "bool"},
        "escalation_time_in_minutes": {"key": "escalationTimeInMinutes", "type": "int"},
        "primary_approvers": {"key": "primaryApprovers", "type": "[UserSet]"},
        "is_escalation_enabled": {"key": "isEscalationEnabled", "type": "bool"},
        "escalation_approvers": {"key": "escalationApprovers", "type": "[UserSet]"},
    }

    def __init__(
        self,
        *,
        approval_stage_time_out_in_days: Optional[int] = None,
        is_approver_justification_required: Optional[bool] = None,
        escalation_time_in_minutes: Optional[int] = None,
        primary_approvers: Optional[List["_models.UserSet"]] = None,
        is_escalation_enabled: Optional[bool] = None,
        escalation_approvers: Optional[List["_models.UserSet"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword approval_stage_time_out_in_days: The time in days when approval request would be timed
         out.
        :paramtype approval_stage_time_out_in_days: int
        :keyword is_approver_justification_required: Determines whether approver need to provide
         justification for his decision.
        :paramtype is_approver_justification_required: bool
        :keyword escalation_time_in_minutes: The time in minutes when the approval request would be
         escalated if the primary approver does not approve.
        :paramtype escalation_time_in_minutes: int
        :keyword primary_approvers: The primary approver of the request.
        :paramtype primary_approvers:
         list[~azure.mgmt.authorization.v2022_05_01_preview.models.UserSet]
        :keyword is_escalation_enabled: The value determine whether escalation feature is enabled.
        :paramtype is_escalation_enabled: bool
        :keyword escalation_approvers: The escalation approver of the request.
        :paramtype escalation_approvers:
         list[~azure.mgmt.authorization.v2022_05_01_preview.models.UserSet]
        """
        super().__init__(**kwargs)
        self.approval_stage_time_out_in_days = approval_stage_time_out_in_days
        self.is_approver_justification_required = is_approver_justification_required
        self.escalation_time_in_minutes = escalation_time_in_minutes
        self.primary_approvers = primary_approvers
        self.is_escalation_enabled = is_escalation_enabled
        self.escalation_approvers = escalation_approvers


class ErrorAdditionalInfo(_serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: JSON
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(_serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.authorization.v2022_05_01_preview.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info:
     list[~azure.mgmt.authorization.v2022_05_01_preview.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(_serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed
    operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.mgmt.authorization.v2022_05_01_preview.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["_models.ErrorDetail"] = None, **kwargs: Any) -> None:
        """
        :keyword error: The error object.
        :paramtype error: ~azure.mgmt.authorization.v2022_05_01_preview.models.ErrorDetail
        """
        super().__init__(**kwargs)
        self.error = error


class Permission(_serialization.Model):
    """Role definition permissions.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar actions: Allowed actions.
    :vartype actions: list[str]
    :ivar not_actions: Denied actions.
    :vartype not_actions: list[str]
    :ivar data_actions: Allowed Data actions.
    :vartype data_actions: list[str]
    :ivar not_data_actions: Denied Data actions.
    :vartype not_data_actions: list[str]
    :ivar condition: The conditions on the role definition. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently the only accepted value is '2.0'.
    :vartype condition_version: str
    """

    _validation = {
        "condition": {"readonly": True},
        "condition_version": {"readonly": True},
    }

    _attribute_map = {
        "actions": {"key": "actions", "type": "[str]"},
        "not_actions": {"key": "notActions", "type": "[str]"},
        "data_actions": {"key": "dataActions", "type": "[str]"},
        "not_data_actions": {"key": "notDataActions", "type": "[str]"},
        "condition": {"key": "condition", "type": "str"},
        "condition_version": {"key": "conditionVersion", "type": "str"},
    }

    def __init__(
        self,
        *,
        actions: Optional[List[str]] = None,
        not_actions: Optional[List[str]] = None,
        data_actions: Optional[List[str]] = None,
        not_data_actions: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword actions: Allowed actions.
        :paramtype actions: list[str]
        :keyword not_actions: Denied actions.
        :paramtype not_actions: list[str]
        :keyword data_actions: Allowed Data actions.
        :paramtype data_actions: list[str]
        :keyword not_data_actions: Denied Data actions.
        :paramtype not_data_actions: list[str]
        """
        super().__init__(**kwargs)
        self.actions = actions
        self.not_actions = not_actions
        self.data_actions = data_actions
        self.not_data_actions = not_data_actions
        self.condition = None
        self.condition_version = None


class PermissionGetResult(_serialization.Model):
    """Permissions information.

    :ivar value: An array of permissions.
    :vartype value: list[~azure.mgmt.authorization.v2022_05_01_preview.models.Permission]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[Permission]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.Permission"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: An array of permissions.
        :paramtype value: list[~azure.mgmt.authorization.v2022_05_01_preview.models.Permission]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class Principal(_serialization.Model):
    """The name of the entity last modified it.

    :ivar id: The id of the principal made changes.
    :vartype id: str
    :ivar display_name: The name of the principal made changes.
    :vartype display_name: str
    :ivar type: Type of principal such as user , group etc.
    :vartype type: str
    :ivar email: Email of principal.
    :vartype email: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "email": {"key": "email", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        email: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the principal made changes.
        :paramtype id: str
        :keyword display_name: The name of the principal made changes.
        :paramtype display_name: str
        :keyword type: Type of principal such as user , group etc.
        :paramtype type: str
        :keyword email: Email of principal.
        :paramtype email: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type
        self.email = email


class RoleDefinition(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role definition.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role definition ID.
    :vartype id: str
    :ivar name: The role definition name.
    :vartype name: str
    :ivar type: The role definition type.
    :vartype type: str
    :ivar role_name: The role name.
    :vartype role_name: str
    :ivar description: The role definition description.
    :vartype description: str
    :ivar role_type: The role type.
    :vartype role_type: str
    :ivar permissions: Role definition permissions.
    :vartype permissions: list[~azure.mgmt.authorization.v2022_05_01_preview.models.Permission]
    :ivar assignable_scopes: Role definition assignable scopes.
    :vartype assignable_scopes: list[str]
    :ivar created_on: Time it was created.
    :vartype created_on: ~datetime.datetime
    :ivar updated_on: Time it was updated.
    :vartype updated_on: ~datetime.datetime
    :ivar created_by: Id of the user who created the assignment.
    :vartype created_by: str
    :ivar updated_by: Id of the user who updated the assignment.
    :vartype updated_by: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "created_on": {"readonly": True},
        "updated_on": {"readonly": True},
        "created_by": {"readonly": True},
        "updated_by": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "role_name": {"key": "properties.roleName", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "role_type": {"key": "properties.type", "type": "str"},
        "permissions": {"key": "properties.permissions", "type": "[Permission]"},
        "assignable_scopes": {"key": "properties.assignableScopes", "type": "[str]"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "updated_on": {"key": "properties.updatedOn", "type": "iso-8601"},
        "created_by": {"key": "properties.createdBy", "type": "str"},
        "updated_by": {"key": "properties.updatedBy", "type": "str"},
    }

    def __init__(
        self,
        *,
        role_name: Optional[str] = None,
        description: Optional[str] = None,
        role_type: Optional[str] = None,
        permissions: Optional[List["_models.Permission"]] = None,
        assignable_scopes: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_name: The role name.
        :paramtype role_name: str
        :keyword description: The role definition description.
        :paramtype description: str
        :keyword role_type: The role type.
        :paramtype role_type: str
        :keyword permissions: Role definition permissions.
        :paramtype permissions: list[~azure.mgmt.authorization.v2022_05_01_preview.models.Permission]
        :keyword assignable_scopes: Role definition assignable scopes.
        :paramtype assignable_scopes: list[str]
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.role_name = role_name
        self.description = description
        self.role_type = role_type
        self.permissions = permissions
        self.assignable_scopes = assignable_scopes
        self.created_on = None
        self.updated_on = None
        self.created_by = None
        self.updated_by = None


class RoleDefinitionFilter(_serialization.Model):
    """Role Definitions filter.

    :ivar role_name: Returns role definition with the specific name.
    :vartype role_name: str
    :ivar type: Returns role definition with the specific type.
    :vartype type: str
    """

    _attribute_map = {
        "role_name": {"key": "roleName", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(self, *, role_name: Optional[str] = None, type: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword role_name: Returns role definition with the specific name.
        :paramtype role_name: str
        :keyword type: Returns role definition with the specific type.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.role_name = role_name
        self.type = type


class RoleDefinitionListResult(_serialization.Model):
    """Role definition list operation result.

    :ivar value: Role definition list.
    :vartype value: list[~azure.mgmt.authorization.v2022_05_01_preview.models.RoleDefinition]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleDefinition]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.RoleDefinition"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: Role definition list.
        :paramtype value: list[~azure.mgmt.authorization.v2022_05_01_preview.models.RoleDefinition]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleManagementPolicyRule(_serialization.Model):
    """The role management policy rule.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RoleManagementPolicyApprovalRule, RoleManagementPolicyAuthenticationContextRule,
    RoleManagementPolicyEnablementRule, RoleManagementPolicyExpirationRule,
    RoleManagementPolicyNotificationRule

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
    }

    _subtype_map = {
        "rule_type": {
            "RoleManagementPolicyApprovalRule": "RoleManagementPolicyApprovalRule",
            "RoleManagementPolicyAuthenticationContextRule": "RoleManagementPolicyAuthenticationContextRule",
            "RoleManagementPolicyEnablementRule": "RoleManagementPolicyEnablementRule",
            "RoleManagementPolicyExpirationRule": "RoleManagementPolicyExpirationRule",
            "RoleManagementPolicyNotificationRule": "RoleManagementPolicyNotificationRule",
        }
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
        """
        super().__init__(**kwargs)
        self.id = id
        self.rule_type: Optional[str] = None
        self.target = target


class RoleManagementPolicyApprovalRule(RoleManagementPolicyRule):
    """The role management policy approval rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar setting: The approval setting.
    :vartype setting: ~azure.mgmt.authorization.v2022_05_01_preview.models.ApprovalSettings
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "setting": {"key": "setting", "type": "ApprovalSettings"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        setting: Optional["_models.ApprovalSettings"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword setting: The approval setting.
        :paramtype setting: ~azure.mgmt.authorization.v2022_05_01_preview.models.ApprovalSettings
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyApprovalRule"
        self.setting = setting


class RoleManagementPolicyAuthenticationContextRule(RoleManagementPolicyRule):
    """The role management policy authentication context rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar is_enabled: The value indicating if rule is enabled.
    :vartype is_enabled: bool
    :ivar claim_value: The claim value.
    :vartype claim_value: str
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "is_enabled": {"key": "isEnabled", "type": "bool"},
        "claim_value": {"key": "claimValue", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        is_enabled: Optional[bool] = None,
        claim_value: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword is_enabled: The value indicating if rule is enabled.
        :paramtype is_enabled: bool
        :keyword claim_value: The claim value.
        :paramtype claim_value: str
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyAuthenticationContextRule"
        self.is_enabled = is_enabled
        self.claim_value = claim_value


class RoleManagementPolicyEnablementRule(RoleManagementPolicyRule):
    """The role management policy enablement rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar enabled_rules: The list of enabled rules.
    :vartype enabled_rules: list[str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.EnablementRules]
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "enabled_rules": {"key": "enabledRules", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        enabled_rules: Optional[List[Union[str, "_models.EnablementRules"]]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword enabled_rules: The list of enabled rules.
        :paramtype enabled_rules: list[str or
         ~azure.mgmt.authorization.v2022_05_01_preview.models.EnablementRules]
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyEnablementRule"
        self.enabled_rules = enabled_rules


class RoleManagementPolicyExpirationRule(RoleManagementPolicyRule):
    """The role management policy expiration rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar is_expiration_required: The value indicating whether expiration is required.
    :vartype is_expiration_required: bool
    :ivar maximum_duration: The maximum duration of expiration in timespan.
    :vartype maximum_duration: str
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "is_expiration_required": {"key": "isExpirationRequired", "type": "bool"},
        "maximum_duration": {"key": "maximumDuration", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        is_expiration_required: Optional[bool] = None,
        maximum_duration: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword is_expiration_required: The value indicating whether expiration is required.
        :paramtype is_expiration_required: bool
        :keyword maximum_duration: The maximum duration of expiration in timespan.
        :paramtype maximum_duration: str
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyExpirationRule"
        self.is_expiration_required = is_expiration_required
        self.maximum_duration = maximum_duration


class RoleManagementPolicyNotificationRule(RoleManagementPolicyRule):
    """The role management policy notification rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar notification_type: The type of notification. "Email"
    :vartype notification_type: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.NotificationDeliveryMechanism
    :ivar notification_level: The notification level. Known values are: "None", "Critical", and
     "All".
    :vartype notification_level: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.NotificationLevel
    :ivar recipient_type: The recipient type. Known values are: "Requestor", "Approver", and
     "Admin".
    :vartype recipient_type: str or
     ~azure.mgmt.authorization.v2022_05_01_preview.models.RecipientType
    :ivar notification_recipients: The list of notification recipients.
    :vartype notification_recipients: list[str]
    :ivar is_default_recipients_enabled: Determines if the notification will be sent to the
     recipient type specified in the policy rule.
    :vartype is_default_recipients_enabled: bool
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "notification_type": {"key": "notificationType", "type": "str"},
        "notification_level": {"key": "notificationLevel", "type": "str"},
        "recipient_type": {"key": "recipientType", "type": "str"},
        "notification_recipients": {"key": "notificationRecipients", "type": "[str]"},
        "is_default_recipients_enabled": {"key": "isDefaultRecipientsEnabled", "type": "bool"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        notification_type: Optional[Union[str, "_models.NotificationDeliveryMechanism"]] = None,
        notification_level: Optional[Union[str, "_models.NotificationLevel"]] = None,
        recipient_type: Optional[Union[str, "_models.RecipientType"]] = None,
        notification_recipients: Optional[List[str]] = None,
        is_default_recipients_enabled: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2022_05_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword notification_type: The type of notification. "Email"
        :paramtype notification_type: str or
         ~azure.mgmt.authorization.v2022_05_01_preview.models.NotificationDeliveryMechanism
        :keyword notification_level: The notification level. Known values are: "None", "Critical", and
         "All".
        :paramtype notification_level: str or
         ~azure.mgmt.authorization.v2022_05_01_preview.models.NotificationLevel
        :keyword recipient_type: The recipient type. Known values are: "Requestor", "Approver", and
         "Admin".
        :paramtype recipient_type: str or
         ~azure.mgmt.authorization.v2022_05_01_preview.models.RecipientType
        :keyword notification_recipients: The list of notification recipients.
        :paramtype notification_recipients: list[str]
        :keyword is_default_recipients_enabled: Determines if the notification will be sent to the
         recipient type specified in the policy rule.
        :paramtype is_default_recipients_enabled: bool
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyNotificationRule"
        self.notification_type = notification_type
        self.notification_level = notification_level
        self.recipient_type = recipient_type
        self.notification_recipients = notification_recipients
        self.is_default_recipients_enabled = is_default_recipients_enabled


class RoleManagementPolicyRuleTarget(_serialization.Model):
    """The role management policy rule target.

    :ivar caller: The caller of the setting.
    :vartype caller: str
    :ivar operations: The type of operation.
    :vartype operations: list[str]
    :ivar level: The assignment level to which rule is applied.
    :vartype level: str
    :ivar target_objects: The list of target objects.
    :vartype target_objects: list[str]
    :ivar inheritable_settings: The list of inheritable settings.
    :vartype inheritable_settings: list[str]
    :ivar enforced_settings: The list of enforced settings.
    :vartype enforced_settings: list[str]
    """

    _attribute_map = {
        "caller": {"key": "caller", "type": "str"},
        "operations": {"key": "operations", "type": "[str]"},
        "level": {"key": "level", "type": "str"},
        "target_objects": {"key": "targetObjects", "type": "[str]"},
        "inheritable_settings": {"key": "inheritableSettings", "type": "[str]"},
        "enforced_settings": {"key": "enforcedSettings", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        caller: Optional[str] = None,
        operations: Optional[List[str]] = None,
        level: Optional[str] = None,
        target_objects: Optional[List[str]] = None,
        inheritable_settings: Optional[List[str]] = None,
        enforced_settings: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword caller: The caller of the setting.
        :paramtype caller: str
        :keyword operations: The type of operation.
        :paramtype operations: list[str]
        :keyword level: The assignment level to which rule is applied.
        :paramtype level: str
        :keyword target_objects: The list of target objects.
        :paramtype target_objects: list[str]
        :keyword inheritable_settings: The list of inheritable settings.
        :paramtype inheritable_settings: list[str]
        :keyword enforced_settings: The list of enforced settings.
        :paramtype enforced_settings: list[str]
        """
        super().__init__(**kwargs)
        self.caller = caller
        self.operations = operations
        self.level = level
        self.target_objects = target_objects
        self.inheritable_settings = inheritable_settings
        self.enforced_settings = enforced_settings


class UserSet(_serialization.Model):
    """The detail of a user.

    :ivar user_type: The type of user. Known values are: "User" and "Group".
    :vartype user_type: str or ~azure.mgmt.authorization.v2022_05_01_preview.models.UserType
    :ivar is_backup: The value indicating whether the user is a backup fallback approver.
    :vartype is_backup: bool
    :ivar id: The object id of the user.
    :vartype id: str
    :ivar description: The description of the user.
    :vartype description: str
    """

    _attribute_map = {
        "user_type": {"key": "userType", "type": "str"},
        "is_backup": {"key": "isBackup", "type": "bool"},
        "id": {"key": "id", "type": "str"},
        "description": {"key": "description", "type": "str"},
    }

    def __init__(
        self,
        *,
        user_type: Optional[Union[str, "_models.UserType"]] = None,
        is_backup: Optional[bool] = None,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        description: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword user_type: The type of user. Known values are: "User" and "Group".
        :paramtype user_type: str or ~azure.mgmt.authorization.v2022_05_01_preview.models.UserType
        :keyword is_backup: The value indicating whether the user is a backup fallback approver.
        :paramtype is_backup: bool
        :keyword id: The object id of the user.
        :paramtype id: str
        :keyword description: The description of the user.
        :paramtype description: str
        """
        super().__init__(**kwargs)
        self.user_type = user_type
        self.is_backup = is_backup
        self.id = id
        self.description = description
