# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._models_py3 import AccessReviewContactedReviewer
from ._models_py3 import AccessReviewContactedReviewerListResult
from ._models_py3 import AccessReviewDecision
from ._models_py3 import AccessReviewDecisionIdentity
from ._models_py3 import AccessReviewDecisionInsight
from ._models_py3 import AccessReviewDecisionInsightProperties
from ._models_py3 import AccessReviewDecisionListResult
from ._models_py3 import AccessReviewDecisionProperties
from ._models_py3 import AccessReviewDecisionServicePrincipalIdentity
from ._models_py3 import AccessReviewDecisionUserIdentity
from ._models_py3 import AccessReviewDecisionUserSignInInsightProperties
from ._models_py3 import AccessReviewDefaultSettings
from ._models_py3 import AccessReviewHistoryDefinition
from ._models_py3 import AccessReviewHistoryDefinitionInstanceListResult
from ._models_py3 import AccessReviewHistoryDefinitionListResult
from ._models_py3 import AccessReviewHistoryDefinitionProperties
from ._models_py3 import AccessReviewHistoryInstance
from ._models_py3 import AccessReviewInstance
from ._models_py3 import AccessReviewInstanceListResult
from ._models_py3 import AccessReviewInstanceProperties
from ._models_py3 import AccessReviewReviewer
from ._models_py3 import AccessReviewScheduleDefinition
from ._models_py3 import AccessReviewScheduleDefinitionListResult
from ._models_py3 import AccessReviewScheduleDefinitionProperties
from ._models_py3 import AccessReviewScheduleSettings
from ._models_py3 import AccessReviewScope
from ._models_py3 import ErrorDefinition
from ._models_py3 import ErrorDefinitionProperties
from ._models_py3 import Operation
from ._models_py3 import OperationDisplay
from ._models_py3 import OperationListResult
from ._models_py3 import RecordAllDecisionsProperties

from ._authorization_management_client_enums import AccessRecommendationType
from ._authorization_management_client_enums import AccessReviewActorIdentityType
from ._authorization_management_client_enums import AccessReviewApplyResult
from ._authorization_management_client_enums import AccessReviewDecisionInsightType
from ._authorization_management_client_enums import AccessReviewDecisionPrincipalResourceMembershipType
from ._authorization_management_client_enums import AccessReviewHistoryDefinitionStatus
from ._authorization_management_client_enums import AccessReviewInstanceReviewersType
from ._authorization_management_client_enums import AccessReviewInstanceStatus
from ._authorization_management_client_enums import AccessReviewRecurrencePatternType
from ._authorization_management_client_enums import AccessReviewRecurrenceRangeType
from ._authorization_management_client_enums import AccessReviewResult
from ._authorization_management_client_enums import AccessReviewReviewerType
from ._authorization_management_client_enums import AccessReviewScheduleDefinitionReviewersType
from ._authorization_management_client_enums import AccessReviewScheduleDefinitionStatus
from ._authorization_management_client_enums import AccessReviewScopeAssignmentState
from ._authorization_management_client_enums import AccessReviewScopePrincipalType
from ._authorization_management_client_enums import DecisionResourceType
from ._authorization_management_client_enums import DecisionTargetType
from ._authorization_management_client_enums import DefaultDecisionType
from ._authorization_management_client_enums import RecordAllDecisionsResult
from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "AccessReviewContactedReviewer",
    "AccessReviewContactedReviewerListResult",
    "AccessReviewDecision",
    "AccessReviewDecisionIdentity",
    "AccessReviewDecisionInsight",
    "AccessReviewDecisionInsightProperties",
    "AccessReviewDecisionListResult",
    "AccessReviewDecisionProperties",
    "AccessReviewDecisionServicePrincipalIdentity",
    "AccessReviewDecisionUserIdentity",
    "AccessReviewDecisionUserSignInInsightProperties",
    "AccessReviewDefaultSettings",
    "AccessReviewHistoryDefinition",
    "AccessReviewHistoryDefinitionInstanceListResult",
    "AccessReviewHistoryDefinitionListResult",
    "AccessReviewHistoryDefinitionProperties",
    "AccessReviewHistoryInstance",
    "AccessReviewInstance",
    "AccessReviewInstanceListResult",
    "AccessReviewInstanceProperties",
    "AccessReviewReviewer",
    "AccessReviewScheduleDefinition",
    "AccessReviewScheduleDefinitionListResult",
    "AccessReviewScheduleDefinitionProperties",
    "AccessReviewScheduleSettings",
    "AccessReviewScope",
    "ErrorDefinition",
    "ErrorDefinitionProperties",
    "Operation",
    "OperationDisplay",
    "OperationListResult",
    "RecordAllDecisionsProperties",
    "AccessRecommendationType",
    "AccessReviewActorIdentityType",
    "AccessReviewApplyResult",
    "AccessReviewDecisionInsightType",
    "AccessReviewDecisionPrincipalResourceMembershipType",
    "AccessReviewHistoryDefinitionStatus",
    "AccessReviewInstanceReviewersType",
    "AccessReviewInstanceStatus",
    "AccessReviewRecurrencePatternType",
    "AccessReviewRecurrenceRangeType",
    "AccessReviewResult",
    "AccessReviewReviewerType",
    "AccessReviewScheduleDefinitionReviewersType",
    "AccessReviewScheduleDefinitionStatus",
    "AccessReviewScopeAssignmentState",
    "AccessReviewScopePrincipalType",
    "DecisionResourceType",
    "DecisionTargetType",
    "DefaultDecisionType",
    "RecordAllDecisionsResult",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
