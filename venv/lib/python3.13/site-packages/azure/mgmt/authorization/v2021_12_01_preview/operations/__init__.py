# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._operations import Operations
from ._access_review_history_definitions_operations import AccessReviewHistoryDefinitionsOperations
from ._access_review_history_definition_operations import AccessReviewHistoryDefinitionOperations
from ._access_review_history_definition_instance_operations import AccessReviewHistoryDefinitionInstanceOperations
from ._access_review_history_definition_instances_operations import AccessReviewHistoryDefinitionInstancesOperations
from ._access_review_schedule_definitions_operations import AccessReviewScheduleDefinitionsOperations
from ._access_review_instances_operations import AccessReviewInstancesOperations
from ._access_review_instance_operations import AccessReviewInstanceOperations
from ._access_review_instance_decisions_operations import AccessReviewInstanceDecisionsOperations
from ._access_review_instance_contacted_reviewers_operations import AccessReviewInstanceContactedReviewersOperations
from ._access_review_default_settings_operations import AccessReviewDefaultSettingsOperations
from ._scope_access_review_history_definitions_operations import ScopeAccessReviewHistoryDefinitionsOperations
from ._scope_access_review_history_definition_operations import ScopeAccessReviewHistoryDefinitionOperations
from ._scope_access_review_history_definition_instance_operations import (
    ScopeAccessReviewHistoryDefinitionInstanceOperations,
)
from ._scope_access_review_history_definition_instances_operations import (
    ScopeAccessReviewHistoryDefinitionInstancesOperations,
)
from ._scope_access_review_schedule_definitions_operations import ScopeAccessReviewScheduleDefinitionsOperations
from ._scope_access_review_instances_operations import ScopeAccessReviewInstancesOperations
from ._scope_access_review_instance_operations import ScopeAccessReviewInstanceOperations
from ._scope_access_review_instance_decisions_operations import ScopeAccessReviewInstanceDecisionsOperations
from ._scope_access_review_instance_contacted_reviewers_operations import (
    ScopeAccessReviewInstanceContactedReviewersOperations,
)
from ._scope_access_review_default_settings_operations import ScopeAccessReviewDefaultSettingsOperations
from ._access_review_schedule_definitions_assigned_for_my_approval_operations import (
    AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations,
)
from ._access_review_instances_assigned_for_my_approval_operations import (
    AccessReviewInstancesAssignedForMyApprovalOperations,
)
from ._access_review_instance_my_decisions_operations import AccessReviewInstanceMyDecisionsOperations
from ._tenant_level_access_review_instance_contacted_reviewers_operations import (
    TenantLevelAccessReviewInstanceContactedReviewersOperations,
)

from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "Operations",
    "AccessReviewHistoryDefinitionsOperations",
    "AccessReviewHistoryDefinitionOperations",
    "AccessReviewHistoryDefinitionInstanceOperations",
    "AccessReviewHistoryDefinitionInstancesOperations",
    "AccessReviewScheduleDefinitionsOperations",
    "AccessReviewInstancesOperations",
    "AccessReviewInstanceOperations",
    "AccessReviewInstanceDecisionsOperations",
    "AccessReviewInstanceContactedReviewersOperations",
    "AccessReviewDefaultSettingsOperations",
    "ScopeAccessReviewHistoryDefinitionsOperations",
    "ScopeAccessReviewHistoryDefinitionOperations",
    "ScopeAccessReviewHistoryDefinitionInstanceOperations",
    "ScopeAccessReviewHistoryDefinitionInstancesOperations",
    "ScopeAccessReviewScheduleDefinitionsOperations",
    "ScopeAccessReviewInstancesOperations",
    "ScopeAccessReviewInstanceOperations",
    "ScopeAccessReviewInstanceDecisionsOperations",
    "ScopeAccessReviewInstanceContactedReviewersOperations",
    "ScopeAccessReviewDefaultSettingsOperations",
    "AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations",
    "AccessReviewInstancesAssignedForMyApprovalOperations",
    "AccessReviewInstanceMyDecisionsOperations",
    "TenantLevelAccessReviewInstanceContactedReviewersOperations",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
