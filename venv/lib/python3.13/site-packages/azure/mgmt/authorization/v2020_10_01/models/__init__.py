# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._models_py3 import ApprovalSettings
from ._models_py3 import ApprovalStage
from ._models_py3 import CloudErrorBody
from ._models_py3 import EligibleChildResource
from ._models_py3 import EligibleChildResourcesListResult
from ._models_py3 import ExpandedProperties
from ._models_py3 import ExpandedPropertiesPrincipal
from ._models_py3 import ExpandedPropertiesRoleDefinition
from ._models_py3 import ExpandedPropertiesScope
from ._models_py3 import Permission
from ._models_py3 import PolicyAssignmentProperties
from ._models_py3 import PolicyAssignmentPropertiesPolicy
from ._models_py3 import PolicyAssignmentPropertiesRoleDefinition
from ._models_py3 import PolicyAssignmentPropertiesScope
from ._models_py3 import PolicyProperties
from ._models_py3 import PolicyPropertiesScope
from ._models_py3 import Principal
from ._models_py3 import RoleAssignmentSchedule
from ._models_py3 import RoleAssignmentScheduleFilter
from ._models_py3 import RoleAssignmentScheduleInstance
from ._models_py3 import RoleAssignmentScheduleInstanceFilter
from ._models_py3 import RoleAssignmentScheduleInstanceListResult
from ._models_py3 import RoleAssignmentScheduleListResult
from ._models_py3 import RoleAssignmentScheduleRequest
from ._models_py3 import RoleAssignmentScheduleRequestFilter
from ._models_py3 import RoleAssignmentScheduleRequestListResult
from ._models_py3 import RoleAssignmentScheduleRequestPropertiesScheduleInfo
from ._models_py3 import RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration
from ._models_py3 import RoleAssignmentScheduleRequestPropertiesTicketInfo
from ._models_py3 import RoleEligibilitySchedule
from ._models_py3 import RoleEligibilityScheduleFilter
from ._models_py3 import RoleEligibilityScheduleInstance
from ._models_py3 import RoleEligibilityScheduleInstanceFilter
from ._models_py3 import RoleEligibilityScheduleInstanceListResult
from ._models_py3 import RoleEligibilityScheduleListResult
from ._models_py3 import RoleEligibilityScheduleRequest
from ._models_py3 import RoleEligibilityScheduleRequestFilter
from ._models_py3 import RoleEligibilityScheduleRequestListResult
from ._models_py3 import RoleEligibilityScheduleRequestPropertiesScheduleInfo
from ._models_py3 import RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration
from ._models_py3 import RoleEligibilityScheduleRequestPropertiesTicketInfo
from ._models_py3 import RoleManagementPolicy
from ._models_py3 import RoleManagementPolicyApprovalRule
from ._models_py3 import RoleManagementPolicyAssignment
from ._models_py3 import RoleManagementPolicyAssignmentListResult
from ._models_py3 import RoleManagementPolicyAuthenticationContextRule
from ._models_py3 import RoleManagementPolicyEnablementRule
from ._models_py3 import RoleManagementPolicyExpirationRule
from ._models_py3 import RoleManagementPolicyListResult
from ._models_py3 import RoleManagementPolicyNotificationRule
from ._models_py3 import RoleManagementPolicyRule
from ._models_py3 import RoleManagementPolicyRuleTarget
from ._models_py3 import UserSet

from ._authorization_management_client_enums import ApprovalMode
from ._authorization_management_client_enums import AssignmentType
from ._authorization_management_client_enums import EnablementRules
from ._authorization_management_client_enums import MemberType
from ._authorization_management_client_enums import NotificationDeliveryMechanism
from ._authorization_management_client_enums import NotificationLevel
from ._authorization_management_client_enums import PrincipalType
from ._authorization_management_client_enums import RecipientType
from ._authorization_management_client_enums import RequestType
from ._authorization_management_client_enums import RoleManagementPolicyRuleType
from ._authorization_management_client_enums import Status
from ._authorization_management_client_enums import Type
from ._authorization_management_client_enums import UserType
from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "ApprovalSettings",
    "ApprovalStage",
    "CloudErrorBody",
    "EligibleChildResource",
    "EligibleChildResourcesListResult",
    "ExpandedProperties",
    "ExpandedPropertiesPrincipal",
    "ExpandedPropertiesRoleDefinition",
    "ExpandedPropertiesScope",
    "Permission",
    "PolicyAssignmentProperties",
    "PolicyAssignmentPropertiesPolicy",
    "PolicyAssignmentPropertiesRoleDefinition",
    "PolicyAssignmentPropertiesScope",
    "PolicyProperties",
    "PolicyPropertiesScope",
    "Principal",
    "RoleAssignmentSchedule",
    "RoleAssignmentScheduleFilter",
    "RoleAssignmentScheduleInstance",
    "RoleAssignmentScheduleInstanceFilter",
    "RoleAssignmentScheduleInstanceListResult",
    "RoleAssignmentScheduleListResult",
    "RoleAssignmentScheduleRequest",
    "RoleAssignmentScheduleRequestFilter",
    "RoleAssignmentScheduleRequestListResult",
    "RoleAssignmentScheduleRequestPropertiesScheduleInfo",
    "RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration",
    "RoleAssignmentScheduleRequestPropertiesTicketInfo",
    "RoleEligibilitySchedule",
    "RoleEligibilityScheduleFilter",
    "RoleEligibilityScheduleInstance",
    "RoleEligibilityScheduleInstanceFilter",
    "RoleEligibilityScheduleInstanceListResult",
    "RoleEligibilityScheduleListResult",
    "RoleEligibilityScheduleRequest",
    "RoleEligibilityScheduleRequestFilter",
    "RoleEligibilityScheduleRequestListResult",
    "RoleEligibilityScheduleRequestPropertiesScheduleInfo",
    "RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration",
    "RoleEligibilityScheduleRequestPropertiesTicketInfo",
    "RoleManagementPolicy",
    "RoleManagementPolicyApprovalRule",
    "RoleManagementPolicyAssignment",
    "RoleManagementPolicyAssignmentListResult",
    "RoleManagementPolicyAuthenticationContextRule",
    "RoleManagementPolicyEnablementRule",
    "RoleManagementPolicyExpirationRule",
    "RoleManagementPolicyListResult",
    "RoleManagementPolicyNotificationRule",
    "RoleManagementPolicyRule",
    "RoleManagementPolicyRuleTarget",
    "UserSet",
    "ApprovalMode",
    "AssignmentType",
    "EnablementRules",
    "MemberType",
    "NotificationDeliveryMechanism",
    "NotificationLevel",
    "PrincipalType",
    "RecipientType",
    "RequestType",
    "RoleManagementPolicyRuleType",
    "Status",
    "Type",
    "UserType",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
