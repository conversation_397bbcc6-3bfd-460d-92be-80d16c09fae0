# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import Any, List, Optional, TYPE_CHECKING

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class Alert(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """The alert.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The alert ID.
    :vartype id: str
    :ivar name: The alert name.
    :vartype name: str
    :ivar type: The alert type.
    :vartype type: str
    :ivar scope: The alert scope.
    :vartype scope: str
    :ivar is_active: False by default; true if the alert is active.
    :vartype is_active: bool
    :ivar incident_count: The number of generated incidents of the alert.
    :vartype incident_count: int
    :ivar last_modified_date_time: The date time when the alert configuration was updated or new
     incidents were generated.
    :vartype last_modified_date_time: ~datetime.datetime
    :ivar last_scanned_date_time: The date time when the alert was last scanned.
    :vartype last_scanned_date_time: ~datetime.datetime
    :ivar alert_definition: The alert definition.
    :vartype alert_definition: ~azure.mgmt.authorization.v2022_08_01_preview.models.AlertDefinition
    :ivar alert_incidents: The alert incidents.
    :vartype alert_incidents:
     list[~azure.mgmt.authorization.v2022_08_01_preview.models.AlertIncident]
    :ivar alert_configuration: The alert configuration.
    :vartype alert_configuration:
     ~azure.mgmt.authorization.v2022_08_01_preview.models.AlertConfiguration
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "scope": {"readonly": True},
        "incident_count": {"readonly": True},
        "last_modified_date_time": {"readonly": True},
        "last_scanned_date_time": {"readonly": True},
        "alert_definition": {"readonly": True},
        "alert_incidents": {"readonly": True},
        "alert_configuration": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "is_active": {"key": "properties.isActive", "type": "bool"},
        "incident_count": {"key": "properties.incidentCount", "type": "int"},
        "last_modified_date_time": {"key": "properties.lastModifiedDateTime", "type": "iso-8601"},
        "last_scanned_date_time": {"key": "properties.lastScannedDateTime", "type": "iso-8601"},
        "alert_definition": {"key": "properties.alertDefinition", "type": "AlertDefinition"},
        "alert_incidents": {"key": "properties.alertIncidents", "type": "[AlertIncident]"},
        "alert_configuration": {"key": "properties.alertConfiguration", "type": "AlertConfiguration"},
    }

    def __init__(self, *, is_active: Optional[bool] = None, **kwargs: Any) -> None:
        """
        :keyword is_active: False by default; true if the alert is active.
        :paramtype is_active: bool
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = None
        self.is_active = is_active
        self.incident_count = None
        self.last_modified_date_time = None
        self.last_scanned_date_time = None
        self.alert_definition = None
        self.alert_incidents = None
        self.alert_configuration = None


class AlertConfiguration(_serialization.Model):
    """Alert configuration.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The alert configuration ID.
    :vartype id: str
    :ivar name: The alert configuration name.
    :vartype name: str
    :ivar type: The alert configuration type.
    :vartype type: str
    :ivar alert_definition_id: The alert definition ID.
    :vartype alert_definition_id: str
    :ivar scope: The alert scope.
    :vartype scope: str
    :ivar is_enabled: True if the alert is enabled, false will disable the scanning for the
     specific alert.
    :vartype is_enabled: bool
    :ivar alert_configuration_type: The alert configuration type.
    :vartype alert_configuration_type: str
    :ivar alert_definition: The alert definition.
    :vartype alert_definition: ~azure.mgmt.authorization.v2022_08_01_preview.models.AlertDefinition
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "alert_definition_id": {"readonly": True},
        "scope": {"readonly": True},
        "alert_definition": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "alert_definition_id": {"key": "properties.alertDefinitionId", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "is_enabled": {"key": "properties.isEnabled", "type": "bool"},
        "alert_configuration_type": {"key": "properties.alertConfigurationType", "type": "str"},
        "alert_definition": {"key": "properties.alertDefinition", "type": "AlertDefinition"},
    }

    def __init__(self, *, is_enabled: Optional[bool] = None, **kwargs: Any) -> None:
        """
        :keyword is_enabled: True if the alert is enabled, false will disable the scanning for the
         specific alert.
        :paramtype is_enabled: bool
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.alert_definition_id = None
        self.scope = None
        self.is_enabled = is_enabled
        self.alert_configuration_type: Optional[str] = None
        self.alert_definition = None


class AlertConfigurationListResult(_serialization.Model):
    """Alert configuration list operation result.

    :ivar value: Alert configuration list.
    :vartype value: list[~azure.mgmt.authorization.v2022_08_01_preview.models.AlertConfiguration]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[AlertConfiguration]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.AlertConfiguration"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Alert configuration list.
        :paramtype value: list[~azure.mgmt.authorization.v2022_08_01_preview.models.AlertConfiguration]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class AlertConfigurationProperties(_serialization.Model):
    """Alert configuration properties.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    AzureRolesAssignedOutsidePimAlertConfigurationProperties,
    DuplicateRoleCreatedAlertConfigurationProperties,
    TooManyOwnersAssignedToResourceAlertConfigurationProperties,
    TooManyPermanentOwnersAssignedToResourceAlertConfigurationProperties

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar alert_definition_id: The alert definition ID.
    :vartype alert_definition_id: str
    :ivar scope: The alert scope.
    :vartype scope: str
    :ivar is_enabled: True if the alert is enabled, false will disable the scanning for the
     specific alert.
    :vartype is_enabled: bool
    :ivar alert_configuration_type: The alert configuration type. Required.
    :vartype alert_configuration_type: str
    :ivar alert_definition: The alert definition.
    :vartype alert_definition: ~azure.mgmt.authorization.v2022_08_01_preview.models.AlertDefinition
    """

    _validation = {
        "alert_definition_id": {"readonly": True},
        "scope": {"readonly": True},
        "alert_configuration_type": {"required": True},
        "alert_definition": {"readonly": True},
    }

    _attribute_map = {
        "alert_definition_id": {"key": "alertDefinitionId", "type": "str"},
        "scope": {"key": "scope", "type": "str"},
        "is_enabled": {"key": "isEnabled", "type": "bool"},
        "alert_configuration_type": {"key": "alertConfigurationType", "type": "str"},
        "alert_definition": {"key": "alertDefinition", "type": "AlertDefinition"},
    }

    _subtype_map = {
        "alert_configuration_type": {
            "AzureRolesAssignedOutsidePimAlertConfiguration": "AzureRolesAssignedOutsidePimAlertConfigurationProperties",
            "DuplicateRoleCreatedAlertConfiguration": "DuplicateRoleCreatedAlertConfigurationProperties",
            "TooManyOwnersAssignedToResourceAlertConfiguration": "TooManyOwnersAssignedToResourceAlertConfigurationProperties",
            "TooManyPermanentOwnersAssignedToResourceAlertConfiguration": "TooManyPermanentOwnersAssignedToResourceAlertConfigurationProperties",
        }
    }

    def __init__(self, *, is_enabled: Optional[bool] = None, **kwargs: Any) -> None:
        """
        :keyword is_enabled: True if the alert is enabled, false will disable the scanning for the
         specific alert.
        :paramtype is_enabled: bool
        """
        super().__init__(**kwargs)
        self.alert_definition_id = None
        self.scope = None
        self.is_enabled = is_enabled
        self.alert_configuration_type: Optional[str] = None
        self.alert_definition = None


class AlertDefinition(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Alert definition.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The alert definition ID.
    :vartype id: str
    :ivar name: The alert definition name.
    :vartype name: str
    :ivar type: The alert definition type.
    :vartype type: str
    :ivar display_name: The alert display name.
    :vartype display_name: str
    :ivar scope: The alert scope.
    :vartype scope: str
    :ivar description: The alert description.
    :vartype description: str
    :ivar severity_level: Severity level of the alert. Known values are: "Low", "Medium", and
     "High".
    :vartype severity_level: str or
     ~azure.mgmt.authorization.v2022_08_01_preview.models.SeverityLevel
    :ivar security_impact: Security impact of the alert.
    :vartype security_impact: str
    :ivar mitigation_steps: The methods to mitigate the alert.
    :vartype mitigation_steps: str
    :ivar how_to_prevent: The ways to prevent the alert.
    :vartype how_to_prevent: str
    :ivar is_remediatable: True if the alert can be remediated; false, otherwise.
    :vartype is_remediatable: bool
    :ivar is_configurable: True if the alert configuration can be configured; false, otherwise.
    :vartype is_configurable: bool
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "display_name": {"readonly": True},
        "scope": {"readonly": True},
        "description": {"readonly": True},
        "severity_level": {"readonly": True},
        "security_impact": {"readonly": True},
        "mitigation_steps": {"readonly": True},
        "how_to_prevent": {"readonly": True},
        "is_remediatable": {"readonly": True},
        "is_configurable": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "display_name": {"key": "properties.displayName", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "severity_level": {"key": "properties.severityLevel", "type": "str"},
        "security_impact": {"key": "properties.securityImpact", "type": "str"},
        "mitigation_steps": {"key": "properties.mitigationSteps", "type": "str"},
        "how_to_prevent": {"key": "properties.howToPrevent", "type": "str"},
        "is_remediatable": {"key": "properties.isRemediatable", "type": "bool"},
        "is_configurable": {"key": "properties.isConfigurable", "type": "bool"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.display_name = None
        self.scope = None
        self.description = None
        self.severity_level = None
        self.security_impact = None
        self.mitigation_steps = None
        self.how_to_prevent = None
        self.is_remediatable = None
        self.is_configurable = None


class AlertDefinitionListResult(_serialization.Model):
    """Alert definition list operation result.

    :ivar value: Alert definition list.
    :vartype value: list[~azure.mgmt.authorization.v2022_08_01_preview.models.AlertDefinition]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[AlertDefinition]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.AlertDefinition"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: Alert definition list.
        :paramtype value: list[~azure.mgmt.authorization.v2022_08_01_preview.models.AlertDefinition]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class AlertIncident(_serialization.Model):
    """Alert incident.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The alert incident ID.
    :vartype id: str
    :ivar name: The alert incident name.
    :vartype name: str
    :ivar type: The alert incident type.
    :vartype type: str
    :ivar alert_incident_type: The alert incident type.
    :vartype alert_incident_type: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "alert_incident_type": {"key": "properties.alertIncidentType", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.alert_incident_type: Optional[str] = None


class AlertIncidentListResult(_serialization.Model):
    """Alert incident list operation result.

    :ivar value: Alert incident list.
    :vartype value: list[~azure.mgmt.authorization.v2022_08_01_preview.models.AlertIncident]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[AlertIncident]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.AlertIncident"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: Alert incident list.
        :paramtype value: list[~azure.mgmt.authorization.v2022_08_01_preview.models.AlertIncident]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class AlertIncidentProperties(_serialization.Model):
    """Alert incident properties.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    AzureRolesAssignedOutsidePimAlertIncidentProperties,
    DuplicateRoleCreatedAlertIncidentProperties,
    TooManyOwnersAssignedToResourceAlertIncidentProperties,
    TooManyPermanentOwnersAssignedToResourceAlertIncidentProperties

    All required parameters must be populated in order to send to Azure.

    :ivar alert_incident_type: The alert incident type. Required.
    :vartype alert_incident_type: str
    """

    _validation = {
        "alert_incident_type": {"required": True},
    }

    _attribute_map = {
        "alert_incident_type": {"key": "alertIncidentType", "type": "str"},
    }

    _subtype_map = {
        "alert_incident_type": {
            "AzureRolesAssignedOutsidePimAlertIncident": "AzureRolesAssignedOutsidePimAlertIncidentProperties",
            "DuplicateRoleCreatedAlertIncident": "DuplicateRoleCreatedAlertIncidentProperties",
            "TooManyOwnersAssignedToResourceAlertIncident": "TooManyOwnersAssignedToResourceAlertIncidentProperties",
            "TooManyPermanentOwnersAssignedToResourceAlertIncident": "TooManyPermanentOwnersAssignedToResourceAlertIncidentProperties",
        }
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.alert_incident_type: Optional[str] = None


class AlertListResult(_serialization.Model):
    """Alert list operation result.

    :ivar value: Alert list.
    :vartype value: list[~azure.mgmt.authorization.v2022_08_01_preview.models.Alert]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[Alert]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.Alert"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: Alert list.
        :paramtype value: list[~azure.mgmt.authorization.v2022_08_01_preview.models.Alert]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class AlertOperationResult(_serialization.Model):
    """Alert operation result.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The id of the alert operation.
    :vartype id: str
    :ivar status: The status of the alert operation.
    :vartype status: str
    :ivar status_detail: The status detail of the alert operation.
    :vartype status_detail: str
    :ivar created_date_time: The created date of the alert operation.
    :vartype created_date_time: ~datetime.datetime
    :ivar last_action_date_time: The last action date of the alert operation.
    :vartype last_action_date_time: ~datetime.datetime
    :ivar resource_location: The location of the alert associated with the operation.
    :vartype resource_location: str
    """

    _validation = {
        "id": {"readonly": True},
        "status": {"readonly": True},
        "status_detail": {"readonly": True},
        "created_date_time": {"readonly": True},
        "last_action_date_time": {"readonly": True},
        "resource_location": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "status": {"key": "status", "type": "str"},
        "status_detail": {"key": "statusDetail", "type": "str"},
        "created_date_time": {"key": "createdDateTime", "type": "iso-8601"},
        "last_action_date_time": {"key": "lastActionDateTime", "type": "iso-8601"},
        "resource_location": {"key": "resourceLocation", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.id = None
        self.status = None
        self.status_detail = None
        self.created_date_time = None
        self.last_action_date_time = None
        self.resource_location = None


class AzureRolesAssignedOutsidePimAlertConfigurationProperties(AlertConfigurationProperties):
    """The Azure roles assigned outside PIM alert configuration properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar alert_definition_id: The alert definition ID.
    :vartype alert_definition_id: str
    :ivar scope: The alert scope.
    :vartype scope: str
    :ivar is_enabled: True if the alert is enabled, false will disable the scanning for the
     specific alert.
    :vartype is_enabled: bool
    :ivar alert_configuration_type: The alert configuration type. Required.
    :vartype alert_configuration_type: str
    :ivar alert_definition: The alert definition.
    :vartype alert_definition: ~azure.mgmt.authorization.v2022_08_01_preview.models.AlertDefinition
    """

    _validation = {
        "alert_definition_id": {"readonly": True},
        "scope": {"readonly": True},
        "alert_configuration_type": {"required": True},
        "alert_definition": {"readonly": True},
    }

    _attribute_map = {
        "alert_definition_id": {"key": "alertDefinitionId", "type": "str"},
        "scope": {"key": "scope", "type": "str"},
        "is_enabled": {"key": "isEnabled", "type": "bool"},
        "alert_configuration_type": {"key": "alertConfigurationType", "type": "str"},
        "alert_definition": {"key": "alertDefinition", "type": "AlertDefinition"},
    }

    def __init__(self, *, is_enabled: Optional[bool] = None, **kwargs: Any) -> None:
        """
        :keyword is_enabled: True if the alert is enabled, false will disable the scanning for the
         specific alert.
        :paramtype is_enabled: bool
        """
        super().__init__(is_enabled=is_enabled, **kwargs)
        self.alert_configuration_type: str = "AzureRolesAssignedOutsidePimAlertConfiguration"


class AzureRolesAssignedOutsidePimAlertIncidentProperties(
    AlertIncidentProperties
):  # pylint: disable=too-many-instance-attributes
    """Azure roles assigned outside PIM alert incident properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar alert_incident_type: The alert incident type. Required.
    :vartype alert_incident_type: str
    :ivar assignee_display_name: The assignee display name.
    :vartype assignee_display_name: str
    :ivar assignee_user_principal_name: The assignee user principal name.
    :vartype assignee_user_principal_name: str
    :ivar assignee_id: The assignee ID.
    :vartype assignee_id: str
    :ivar role_display_name: The role display name.
    :vartype role_display_name: str
    :ivar role_template_id: The role template ID.
    :vartype role_template_id: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar assignment_activated_date: The date the assignment was activated.
    :vartype assignment_activated_date: ~datetime.datetime
    :ivar requestor_id: The requestor ID.
    :vartype requestor_id: str
    :ivar requestor_display_name: The requestor display name.
    :vartype requestor_display_name: str
    :ivar requestor_user_principal_name: The requestor user principal name.
    :vartype requestor_user_principal_name: str
    """

    _validation = {
        "alert_incident_type": {"required": True},
        "assignee_display_name": {"readonly": True},
        "assignee_user_principal_name": {"readonly": True},
        "assignee_id": {"readonly": True},
        "role_display_name": {"readonly": True},
        "role_template_id": {"readonly": True},
        "role_definition_id": {"readonly": True},
        "assignment_activated_date": {"readonly": True},
        "requestor_id": {"readonly": True},
        "requestor_display_name": {"readonly": True},
        "requestor_user_principal_name": {"readonly": True},
    }

    _attribute_map = {
        "alert_incident_type": {"key": "alertIncidentType", "type": "str"},
        "assignee_display_name": {"key": "assigneeDisplayName", "type": "str"},
        "assignee_user_principal_name": {"key": "assigneeUserPrincipalName", "type": "str"},
        "assignee_id": {"key": "assigneeId", "type": "str"},
        "role_display_name": {"key": "roleDisplayName", "type": "str"},
        "role_template_id": {"key": "roleTemplateId", "type": "str"},
        "role_definition_id": {"key": "roleDefinitionId", "type": "str"},
        "assignment_activated_date": {"key": "assignmentActivatedDate", "type": "iso-8601"},
        "requestor_id": {"key": "requestorId", "type": "str"},
        "requestor_display_name": {"key": "requestorDisplayName", "type": "str"},
        "requestor_user_principal_name": {"key": "requestorUserPrincipalName", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.alert_incident_type: str = "AzureRolesAssignedOutsidePimAlertIncident"
        self.assignee_display_name = None
        self.assignee_user_principal_name = None
        self.assignee_id = None
        self.role_display_name = None
        self.role_template_id = None
        self.role_definition_id = None
        self.assignment_activated_date = None
        self.requestor_id = None
        self.requestor_display_name = None
        self.requestor_user_principal_name = None


class CloudErrorBody(_serialization.Model):
    """An error response from the service.

    :ivar code: An identifier for the error. Codes are invariant and are intended to be consumed
     programmatically.
    :vartype code: str
    :ivar message: A message describing the error, intended to be suitable for display in a user
     interface.
    :vartype message: str
    """

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
    }

    def __init__(self, *, code: Optional[str] = None, message: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword code: An identifier for the error. Codes are invariant and are intended to be consumed
         programmatically.
        :paramtype code: str
        :keyword message: A message describing the error, intended to be suitable for display in a user
         interface.
        :paramtype message: str
        """
        super().__init__(**kwargs)
        self.code = code
        self.message = message


class DuplicateRoleCreatedAlertConfigurationProperties(AlertConfigurationProperties):
    """The duplicate role created alert configuration.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar alert_definition_id: The alert definition ID.
    :vartype alert_definition_id: str
    :ivar scope: The alert scope.
    :vartype scope: str
    :ivar is_enabled: True if the alert is enabled, false will disable the scanning for the
     specific alert.
    :vartype is_enabled: bool
    :ivar alert_configuration_type: The alert configuration type. Required.
    :vartype alert_configuration_type: str
    :ivar alert_definition: The alert definition.
    :vartype alert_definition: ~azure.mgmt.authorization.v2022_08_01_preview.models.AlertDefinition
    """

    _validation = {
        "alert_definition_id": {"readonly": True},
        "scope": {"readonly": True},
        "alert_configuration_type": {"required": True},
        "alert_definition": {"readonly": True},
    }

    _attribute_map = {
        "alert_definition_id": {"key": "alertDefinitionId", "type": "str"},
        "scope": {"key": "scope", "type": "str"},
        "is_enabled": {"key": "isEnabled", "type": "bool"},
        "alert_configuration_type": {"key": "alertConfigurationType", "type": "str"},
        "alert_definition": {"key": "alertDefinition", "type": "AlertDefinition"},
    }

    def __init__(self, *, is_enabled: Optional[bool] = None, **kwargs: Any) -> None:
        """
        :keyword is_enabled: True if the alert is enabled, false will disable the scanning for the
         specific alert.
        :paramtype is_enabled: bool
        """
        super().__init__(is_enabled=is_enabled, **kwargs)
        self.alert_configuration_type: str = "DuplicateRoleCreatedAlertConfiguration"


class DuplicateRoleCreatedAlertIncidentProperties(AlertIncidentProperties):
    """Duplicate role created alert incident properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar alert_incident_type: The alert incident type. Required.
    :vartype alert_incident_type: str
    :ivar role_name: The role name.
    :vartype role_name: str
    :ivar duplicate_roles: The duplicate roles.
    :vartype duplicate_roles: str
    :ivar reason: The reason for the incident.
    :vartype reason: str
    """

    _validation = {
        "alert_incident_type": {"required": True},
        "role_name": {"readonly": True},
        "duplicate_roles": {"readonly": True},
        "reason": {"readonly": True},
    }

    _attribute_map = {
        "alert_incident_type": {"key": "alertIncidentType", "type": "str"},
        "role_name": {"key": "roleName", "type": "str"},
        "duplicate_roles": {"key": "duplicateRoles", "type": "str"},
        "reason": {"key": "reason", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.alert_incident_type: str = "DuplicateRoleCreatedAlertIncident"
        self.role_name = None
        self.duplicate_roles = None
        self.reason = None


class TooManyOwnersAssignedToResourceAlertConfigurationProperties(AlertConfigurationProperties):
    """Too many owners assigned to resource alert configuration properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar alert_definition_id: The alert definition ID.
    :vartype alert_definition_id: str
    :ivar scope: The alert scope.
    :vartype scope: str
    :ivar is_enabled: True if the alert is enabled, false will disable the scanning for the
     specific alert.
    :vartype is_enabled: bool
    :ivar alert_configuration_type: The alert configuration type. Required.
    :vartype alert_configuration_type: str
    :ivar alert_definition: The alert definition.
    :vartype alert_definition: ~azure.mgmt.authorization.v2022_08_01_preview.models.AlertDefinition
    :ivar threshold_number_of_owners: The threshold number of owners.
    :vartype threshold_number_of_owners: int
    :ivar threshold_percentage_of_owners_out_of_all_role_members: The threshold percentage of
     owners out of all role members.
    :vartype threshold_percentage_of_owners_out_of_all_role_members: int
    """

    _validation = {
        "alert_definition_id": {"readonly": True},
        "scope": {"readonly": True},
        "alert_configuration_type": {"required": True},
        "alert_definition": {"readonly": True},
    }

    _attribute_map = {
        "alert_definition_id": {"key": "alertDefinitionId", "type": "str"},
        "scope": {"key": "scope", "type": "str"},
        "is_enabled": {"key": "isEnabled", "type": "bool"},
        "alert_configuration_type": {"key": "alertConfigurationType", "type": "str"},
        "alert_definition": {"key": "alertDefinition", "type": "AlertDefinition"},
        "threshold_number_of_owners": {"key": "thresholdNumberOfOwners", "type": "int"},
        "threshold_percentage_of_owners_out_of_all_role_members": {
            "key": "thresholdPercentageOfOwnersOutOfAllRoleMembers",
            "type": "int",
        },
    }

    def __init__(
        self,
        *,
        is_enabled: Optional[bool] = None,
        threshold_number_of_owners: Optional[int] = None,
        threshold_percentage_of_owners_out_of_all_role_members: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword is_enabled: True if the alert is enabled, false will disable the scanning for the
         specific alert.
        :paramtype is_enabled: bool
        :keyword threshold_number_of_owners: The threshold number of owners.
        :paramtype threshold_number_of_owners: int
        :keyword threshold_percentage_of_owners_out_of_all_role_members: The threshold percentage of
         owners out of all role members.
        :paramtype threshold_percentage_of_owners_out_of_all_role_members: int
        """
        super().__init__(is_enabled=is_enabled, **kwargs)
        self.alert_configuration_type: str = "TooManyOwnersAssignedToResourceAlertConfiguration"
        self.threshold_number_of_owners = threshold_number_of_owners
        self.threshold_percentage_of_owners_out_of_all_role_members = (
            threshold_percentage_of_owners_out_of_all_role_members
        )


class TooManyOwnersAssignedToResourceAlertIncidentProperties(AlertIncidentProperties):
    """Too many owners assigned to resource alert incident properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar alert_incident_type: The alert incident type. Required.
    :vartype alert_incident_type: str
    :ivar assignee_name: The assignee name.
    :vartype assignee_name: str
    :ivar assignee_type: The assignee type.
    :vartype assignee_type: str
    """

    _validation = {
        "alert_incident_type": {"required": True},
        "assignee_name": {"readonly": True},
        "assignee_type": {"readonly": True},
    }

    _attribute_map = {
        "alert_incident_type": {"key": "alertIncidentType", "type": "str"},
        "assignee_name": {"key": "assigneeName", "type": "str"},
        "assignee_type": {"key": "assigneeType", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.alert_incident_type: str = "TooManyOwnersAssignedToResourceAlertIncident"
        self.assignee_name = None
        self.assignee_type = None


class TooManyPermanentOwnersAssignedToResourceAlertConfigurationProperties(AlertConfigurationProperties):
    """Too many permanent owners assigned to resource alert configuration properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar alert_definition_id: The alert definition ID.
    :vartype alert_definition_id: str
    :ivar scope: The alert scope.
    :vartype scope: str
    :ivar is_enabled: True if the alert is enabled, false will disable the scanning for the
     specific alert.
    :vartype is_enabled: bool
    :ivar alert_configuration_type: The alert configuration type. Required.
    :vartype alert_configuration_type: str
    :ivar alert_definition: The alert definition.
    :vartype alert_definition: ~azure.mgmt.authorization.v2022_08_01_preview.models.AlertDefinition
    :ivar threshold_number_of_permanent_owners: The threshold number of permanent owners.
    :vartype threshold_number_of_permanent_owners: int
    :ivar threshold_percentage_of_permanent_owners_out_of_all_owners: The threshold percentage of
     permanent owners out of all owners.
    :vartype threshold_percentage_of_permanent_owners_out_of_all_owners: int
    """

    _validation = {
        "alert_definition_id": {"readonly": True},
        "scope": {"readonly": True},
        "alert_configuration_type": {"required": True},
        "alert_definition": {"readonly": True},
    }

    _attribute_map = {
        "alert_definition_id": {"key": "alertDefinitionId", "type": "str"},
        "scope": {"key": "scope", "type": "str"},
        "is_enabled": {"key": "isEnabled", "type": "bool"},
        "alert_configuration_type": {"key": "alertConfigurationType", "type": "str"},
        "alert_definition": {"key": "alertDefinition", "type": "AlertDefinition"},
        "threshold_number_of_permanent_owners": {"key": "thresholdNumberOfPermanentOwners", "type": "int"},
        "threshold_percentage_of_permanent_owners_out_of_all_owners": {
            "key": "thresholdPercentageOfPermanentOwnersOutOfAllOwners",
            "type": "int",
        },
    }

    def __init__(
        self,
        *,
        is_enabled: Optional[bool] = None,
        threshold_number_of_permanent_owners: Optional[int] = None,
        threshold_percentage_of_permanent_owners_out_of_all_owners: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword is_enabled: True if the alert is enabled, false will disable the scanning for the
         specific alert.
        :paramtype is_enabled: bool
        :keyword threshold_number_of_permanent_owners: The threshold number of permanent owners.
        :paramtype threshold_number_of_permanent_owners: int
        :keyword threshold_percentage_of_permanent_owners_out_of_all_owners: The threshold percentage
         of permanent owners out of all owners.
        :paramtype threshold_percentage_of_permanent_owners_out_of_all_owners: int
        """
        super().__init__(is_enabled=is_enabled, **kwargs)
        self.alert_configuration_type: str = "TooManyPermanentOwnersAssignedToResourceAlertConfiguration"
        self.threshold_number_of_permanent_owners = threshold_number_of_permanent_owners
        self.threshold_percentage_of_permanent_owners_out_of_all_owners = (
            threshold_percentage_of_permanent_owners_out_of_all_owners
        )


class TooManyPermanentOwnersAssignedToResourceAlertIncidentProperties(AlertIncidentProperties):
    """Too many permanent owners assigned to resource alert incident properties.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar alert_incident_type: The alert incident type. Required.
    :vartype alert_incident_type: str
    :ivar assignee_name: The assignee name.
    :vartype assignee_name: str
    :ivar assignee_type: The assignee type.
    :vartype assignee_type: str
    """

    _validation = {
        "alert_incident_type": {"required": True},
        "assignee_name": {"readonly": True},
        "assignee_type": {"readonly": True},
    }

    _attribute_map = {
        "alert_incident_type": {"key": "alertIncidentType", "type": "str"},
        "assignee_name": {"key": "assigneeName", "type": "str"},
        "assignee_type": {"key": "assigneeType", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.alert_incident_type: str = "TooManyPermanentOwnersAssignedToResourceAlertIncident"
        self.assignee_name = None
        self.assignee_type = None
