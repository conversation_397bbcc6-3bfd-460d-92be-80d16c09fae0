# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._models_py3 import Alert
from ._models_py3 import AlertConfiguration
from ._models_py3 import AlertConfigurationListResult
from ._models_py3 import AlertConfigurationProperties
from ._models_py3 import AlertDefinition
from ._models_py3 import AlertDefinitionListResult
from ._models_py3 import AlertIncident
from ._models_py3 import AlertIncidentListResult
from ._models_py3 import AlertIncidentProperties
from ._models_py3 import AlertListResult
from ._models_py3 import AlertOperationResult
from ._models_py3 import AzureRolesAssignedOutsidePimAlertConfigurationProperties
from ._models_py3 import AzureRolesAssignedOutsidePimAlertIncidentProperties
from ._models_py3 import CloudErrorBody
from ._models_py3 import DuplicateRoleCreatedAlertConfigurationProperties
from ._models_py3 import DuplicateRoleCreatedAlertIncidentProperties
from ._models_py3 import TooManyOwnersAssignedToResourceAlertConfigurationProperties
from ._models_py3 import TooManyOwnersAssignedToResourceAlertIncidentProperties
from ._models_py3 import TooManyPermanentOwnersAssignedToResourceAlertConfigurationProperties
from ._models_py3 import TooManyPermanentOwnersAssignedToResourceAlertIncidentProperties

from ._authorization_management_client_enums import SeverityLevel
from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "Alert",
    "AlertConfiguration",
    "AlertConfigurationListResult",
    "AlertConfigurationProperties",
    "AlertDefinition",
    "AlertDefinitionListResult",
    "AlertIncident",
    "AlertIncidentListResult",
    "AlertIncidentProperties",
    "AlertListResult",
    "AlertOperationResult",
    "AzureRolesAssignedOutsidePimAlertConfigurationProperties",
    "AzureRolesAssignedOutsidePimAlertIncidentProperties",
    "CloudErrorBody",
    "DuplicateRoleCreatedAlertConfigurationProperties",
    "DuplicateRoleCreatedAlertIncidentProperties",
    "TooManyOwnersAssignedToResourceAlertConfigurationProperties",
    "TooManyOwnersAssignedToResourceAlertIncidentProperties",
    "TooManyPermanentOwnersAssignedToResourceAlertConfigurationProperties",
    "TooManyPermanentOwnersAssignedToResourceAlertIncidentProperties",
    "SeverityLevel",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
