# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._alerts_operations import AlertsOperations
from ._alert_configurations_operations import AlertConfigurationsOperations
from ._alert_definitions_operations import AlertDefinitionsOperations
from ._alert_incidents_operations import AlertIncidentsOperations
from ._alert_operation_operations import AlertOperationOperations

from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "AlertsOperations",
    "AlertConfigurationsOperations",
    "AlertDefinitionsOperations",
    "AlertIncidentsOperations",
    "AlertOperationOperations",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
