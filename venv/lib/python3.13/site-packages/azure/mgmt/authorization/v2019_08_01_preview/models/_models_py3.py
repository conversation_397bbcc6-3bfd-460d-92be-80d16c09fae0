# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import Any, Optional, TYPE_CHECKING

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class ErrorAdditionalInfo(_serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: JSON
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(_serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.authorization.v2019_08_01_preview.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info:
     list[~azure.mgmt.authorization.v2019_08_01_preview.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(_serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed
    operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.mgmt.authorization.v2019_08_01_preview.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["_models.ErrorDetail"] = None, **kwargs: Any) -> None:
        """
        :keyword error: The error object.
        :paramtype error: ~azure.mgmt.authorization.v2019_08_01_preview.models.ErrorDetail
        """
        super().__init__(**kwargs)
        self.error = error


class RoleAssignmentMetricsResult(_serialization.Model):
    """Role Assignment Metrics.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar subscription_id: The subscription ID.
    :vartype subscription_id: str
    :ivar role_assignments_limit: The role assignment limit.
    :vartype role_assignments_limit: int
    :ivar role_assignments_current_count: The number of current role assignments.
    :vartype role_assignments_current_count: int
    :ivar role_assignments_remaining_count: The number of remaining role assignments available.
    :vartype role_assignments_remaining_count: int
    """

    _validation = {
        "subscription_id": {"readonly": True},
        "role_assignments_limit": {"readonly": True},
        "role_assignments_current_count": {"readonly": True},
        "role_assignments_remaining_count": {"readonly": True},
    }

    _attribute_map = {
        "subscription_id": {"key": "subscriptionId", "type": "str"},
        "role_assignments_limit": {"key": "roleAssignmentsLimit", "type": "int"},
        "role_assignments_current_count": {"key": "roleAssignmentsCurrentCount", "type": "int"},
        "role_assignments_remaining_count": {"key": "roleAssignmentsRemainingCount", "type": "int"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.subscription_id = None
        self.role_assignments_limit = None
        self.role_assignments_current_count = None
        self.role_assignments_remaining_count = None
