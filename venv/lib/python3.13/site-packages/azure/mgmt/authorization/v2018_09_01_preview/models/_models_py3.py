# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import Any, List, Optional, TYPE_CHECKING, Union

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class ErrorAdditionalInfo(_serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: JSON
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(_serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.authorization.v2018_09_01_preview.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info:
     list[~azure.mgmt.authorization.v2018_09_01_preview.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(_serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed
    operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.mgmt.authorization.v2018_09_01_preview.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["_models.ErrorDetail"] = None, **kwargs: Any) -> None:
        """
        :keyword error: The error object.
        :paramtype error: ~azure.mgmt.authorization.v2018_09_01_preview.models.ErrorDetail
        """
        super().__init__(**kwargs)
        self.error = error


class RoleAssignment(_serialization.Model):
    """Role Assignments.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role assignment ID.
    :vartype id: str
    :ivar name: The role assignment name.
    :vartype name: str
    :ivar type: The role assignment type.
    :vartype type: str
    :ivar scope: The role assignment scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", and "ForeignGroup".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2018_09_01_preview.models.PrincipalType
    :ivar can_delegate: The Delegation flag for the role assignment.
    :vartype can_delegate: bool
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "can_delegate": {"key": "properties.canDelegate", "type": "bool"},
    }

    def __init__(
        self,
        *,
        scope: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        principal_type: Optional[Union[str, "_models.PrincipalType"]] = None,
        can_delegate: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: The role assignment scope.
        :paramtype scope: str
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", and "ForeignGroup".
        :paramtype principal_type: str or
         ~azure.mgmt.authorization.v2018_09_01_preview.models.PrincipalType
        :keyword can_delegate: The Delegation flag for the role assignment.
        :paramtype can_delegate: bool
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = scope
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.can_delegate = can_delegate


class RoleAssignmentCreateParameters(_serialization.Model):
    """Role assignment create parameters.

    All required parameters must be populated in order to send to Azure.

    :ivar role_definition_id: The role definition ID used in the role assignment. Required.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID assigned to the role. This maps to the ID inside the
     Active Directory. It can point to a user, service principal, or security group. Required.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", and "ForeignGroup".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2018_09_01_preview.models.PrincipalType
    :ivar can_delegate: The delegation flag used for creating a role assignment.
    :vartype can_delegate: bool
    """

    _validation = {
        "role_definition_id": {"required": True},
        "principal_id": {"required": True},
    }

    _attribute_map = {
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "can_delegate": {"key": "properties.canDelegate", "type": "bool"},
    }

    def __init__(
        self,
        *,
        role_definition_id: str,
        principal_id: str,
        principal_type: Optional[Union[str, "_models.PrincipalType"]] = None,
        can_delegate: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_definition_id: The role definition ID used in the role assignment. Required.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID assigned to the role. This maps to the ID inside the
         Active Directory. It can point to a user, service principal, or security group. Required.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", and "ForeignGroup".
        :paramtype principal_type: str or
         ~azure.mgmt.authorization.v2018_09_01_preview.models.PrincipalType
        :keyword can_delegate: The delegation flag used for creating a role assignment.
        :paramtype can_delegate: bool
        """
        super().__init__(**kwargs)
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.can_delegate = can_delegate


class RoleAssignmentFilter(_serialization.Model):
    """Role Assignments filter.

    :ivar principal_id: Returns role assignment of the specific principal.
    :vartype principal_id: str
    :ivar can_delegate: The Delegation flag for the role assignment.
    :vartype can_delegate: bool
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "can_delegate": {"key": "canDelegate", "type": "bool"},
    }

    def __init__(
        self, *, principal_id: Optional[str] = None, can_delegate: Optional[bool] = None, **kwargs: Any
    ) -> None:
        """
        :keyword principal_id: Returns role assignment of the specific principal.
        :paramtype principal_id: str
        :keyword can_delegate: The Delegation flag for the role assignment.
        :paramtype can_delegate: bool
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.can_delegate = can_delegate


class RoleAssignmentListResult(_serialization.Model):
    """Role assignment list operation result.

    :ivar value: Role assignment list.
    :vartype value: list[~azure.mgmt.authorization.v2018_09_01_preview.models.RoleAssignment]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleAssignment]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.RoleAssignment"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: Role assignment list.
        :paramtype value: list[~azure.mgmt.authorization.v2018_09_01_preview.models.RoleAssignment]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link
