# pylint: disable=too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
from io import IOBase
from typing import Any, AsyncIterable, Callable, Dict, IO, Optional, TypeVar, Union, overload
import urllib.parse

from azure.core.async_paging import AsyncItemPaged, AsyncList
from azure.core.exceptions import (
    ClientAuthenticationError,
    Htt<PERSON><PERSON><PERSON>ponse<PERSON>rror,
    ResourceExists<PERSON>rror,
    ResourceNotFoundError,
    ResourceNotModifiedError,
    map_error,
)
from azure.core.pipeline import PipelineResponse
from azure.core.pipeline.transport import AsyncHttpResponse
from azure.core.rest import HttpRequest
from azure.core.tracing.decorator import distributed_trace
from azure.core.tracing.decorator_async import distributed_trace_async
from azure.core.utils import case_insensitive_dict
from azure.mgmt.core.exceptions import ARMErrorFormat

from ... import models as _models
from ..._vendor import _convert_request
from ...operations._role_eligibility_schedule_requests_operations import (
    build_cancel_request,
    build_create_request,
    build_get_request,
    build_list_for_scope_request,
    build_validate_request,
)

T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, AsyncHttpResponse], T, Dict[str, Any]], Any]]


class RoleEligibilityScheduleRequestsOperations:
    """
    .. warning::
        **DO NOT** instantiate this class directly.

        Instead, you should access the following operations through
        :class:`~azure.mgmt.authorization.v2022_04_01_preview.aio.AuthorizationManagementClient`'s
        :attr:`role_eligibility_schedule_requests` attribute.
    """

    models = _models

    def __init__(self, *args, **kwargs) -> None:
        input_args = list(args)
        self._client = input_args.pop(0) if input_args else kwargs.pop("client")
        self._config = input_args.pop(0) if input_args else kwargs.pop("config")
        self._serialize = input_args.pop(0) if input_args else kwargs.pop("serializer")
        self._deserialize = input_args.pop(0) if input_args else kwargs.pop("deserializer")
        self._api_version = input_args.pop(0) if input_args else kwargs.pop("api_version")

    @overload
    async def create(
        self,
        scope: str,
        role_eligibility_schedule_request_name: str,
        parameters: _models.RoleEligibilityScheduleRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.RoleEligibilityScheduleRequest:
        """Creates a role eligibility schedule request.

        :param scope: The scope of the role eligibility schedule request to create. The scope can be
         any REST resource instance. For example, use
         '/providers/Microsoft.Subscription/subscriptions/{subscription-id}/' for a subscription,
         '/providers/Microsoft.Subscription/subscriptions/{subscription-id}/resourceGroups/{resource-group-name}'
         for a resource group, and
         '/providers/Microsoft.Subscription/subscriptions/{subscription-id}/resourceGroups/{resource-group-name}/providers/{resource-provider}/{resource-type}/{resource-name}'
         for a resource. Required.
        :type scope: str
        :param role_eligibility_schedule_request_name: The name of the role eligibility to create. It
         can be any valid GUID. Required.
        :type role_eligibility_schedule_request_name: str
        :param parameters: Parameters for the role eligibility schedule request. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleEligibilityScheduleRequest or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    async def create(
        self,
        scope: str,
        role_eligibility_schedule_request_name: str,
        parameters: IO,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.RoleEligibilityScheduleRequest:
        """Creates a role eligibility schedule request.

        :param scope: The scope of the role eligibility schedule request to create. The scope can be
         any REST resource instance. For example, use
         '/providers/Microsoft.Subscription/subscriptions/{subscription-id}/' for a subscription,
         '/providers/Microsoft.Subscription/subscriptions/{subscription-id}/resourceGroups/{resource-group-name}'
         for a resource group, and
         '/providers/Microsoft.Subscription/subscriptions/{subscription-id}/resourceGroups/{resource-group-name}/providers/{resource-provider}/{resource-type}/{resource-name}'
         for a resource. Required.
        :type scope: str
        :param role_eligibility_schedule_request_name: The name of the role eligibility to create. It
         can be any valid GUID. Required.
        :type role_eligibility_schedule_request_name: str
        :param parameters: Parameters for the role eligibility schedule request. Required.
        :type parameters: IO
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleEligibilityScheduleRequest or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace_async
    async def create(
        self,
        scope: str,
        role_eligibility_schedule_request_name: str,
        parameters: Union[_models.RoleEligibilityScheduleRequest, IO],
        **kwargs: Any
    ) -> _models.RoleEligibilityScheduleRequest:
        """Creates a role eligibility schedule request.

        :param scope: The scope of the role eligibility schedule request to create. The scope can be
         any REST resource instance. For example, use
         '/providers/Microsoft.Subscription/subscriptions/{subscription-id}/' for a subscription,
         '/providers/Microsoft.Subscription/subscriptions/{subscription-id}/resourceGroups/{resource-group-name}'
         for a resource group, and
         '/providers/Microsoft.Subscription/subscriptions/{subscription-id}/resourceGroups/{resource-group-name}/providers/{resource-provider}/{resource-type}/{resource-name}'
         for a resource. Required.
        :type scope: str
        :param role_eligibility_schedule_request_name: The name of the role eligibility to create. It
         can be any valid GUID. Required.
        :type role_eligibility_schedule_request_name: str
        :param parameters: Parameters for the role eligibility schedule request. Is either a
         RoleEligibilityScheduleRequest type or a IO type. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest or IO
        :keyword content_type: Body Parameter content-type. Known values are: 'application/json'.
         Default value is None.
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleEligibilityScheduleRequest or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2022-04-01-preview")
        )
        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.RoleEligibilityScheduleRequest] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(parameters, (IOBase, bytes)):
            _content = parameters
        else:
            _json = self._serialize.body(parameters, "RoleEligibilityScheduleRequest")

        request = build_create_request(
            scope=scope,
            role_eligibility_schedule_request_name=role_eligibility_schedule_request_name,
            api_version=api_version,
            content_type=content_type,
            json=_json,
            content=_content,
            template_url=self.create.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("RoleEligibilityScheduleRequest", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    create.metadata = {
        "url": "/{scope}/providers/Microsoft.Authorization/roleEligibilityScheduleRequests/{roleEligibilityScheduleRequestName}"
    }

    @distributed_trace_async
    async def get(
        self, scope: str, role_eligibility_schedule_request_name: str, **kwargs: Any
    ) -> _models.RoleEligibilityScheduleRequest:
        """Get the specified role eligibility schedule request.

        :param scope: The scope of the role eligibility schedule request. Required.
        :type scope: str
        :param role_eligibility_schedule_request_name: The name (guid) of the role eligibility schedule
         request to get. Required.
        :type role_eligibility_schedule_request_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleEligibilityScheduleRequest or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2022-04-01-preview")
        )
        cls: ClsType[_models.RoleEligibilityScheduleRequest] = kwargs.pop("cls", None)

        request = build_get_request(
            scope=scope,
            role_eligibility_schedule_request_name=role_eligibility_schedule_request_name,
            api_version=api_version,
            template_url=self.get.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("RoleEligibilityScheduleRequest", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get.metadata = {
        "url": "/{scope}/providers/Microsoft.Authorization/roleEligibilityScheduleRequests/{roleEligibilityScheduleRequestName}"
    }

    @distributed_trace
    def list_for_scope(
        self, scope: str, filter: Optional[str] = None, **kwargs: Any
    ) -> AsyncIterable["_models.RoleEligibilityScheduleRequest"]:
        """Gets role eligibility schedule requests for a scope.

        :param scope: The scope of the role eligibility schedule requests. Required.
        :type scope: str
        :param filter: The filter to apply on the operation. Use $filter=atScope() to return all role
         eligibility schedule requests at or above the scope. Use $filter=principalId eq {id} to return
         all role eligibility schedule requests at, above or below the scope for the specified
         principal. Use $filter=asRequestor() to return all role eligibility schedule requests requested
         by the current user. Use $filter=asTarget() to return all role eligibility schedule requests
         created for the current user. Use $filter=asApprover() to return all role eligibility schedule
         requests where the current user is an approver. Default value is None.
        :type filter: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either RoleEligibilityScheduleRequest or the result of
         cls(response)
        :rtype:
         ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2022-04-01-preview")
        )
        cls: ClsType[_models.RoleEligibilityScheduleRequestListResult] = kwargs.pop("cls", None)

        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        def prepare_request(next_link=None):
            if not next_link:

                request = build_list_for_scope_request(
                    scope=scope,
                    filter=filter,
                    api_version=api_version,
                    template_url=self.list_for_scope.metadata["url"],
                    headers=_headers,
                    params=_params,
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)

            else:
                # make call to next link with the client's api-version
                _parsed_next_link = urllib.parse.urlparse(next_link)
                _next_request_params = case_insensitive_dict(
                    {
                        key: [urllib.parse.quote(v) for v in value]
                        for key, value in urllib.parse.parse_qs(_parsed_next_link.query).items()
                    }
                )
                _next_request_params["api-version"] = self._config.api_version
                request = HttpRequest(
                    "GET", urllib.parse.urljoin(next_link, _parsed_next_link.path), params=_next_request_params
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)
                request.method = "GET"
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("RoleEligibilityScheduleRequestListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)  # type: ignore
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            _stream = False
            pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
                request, stream=_stream, **kwargs
            )
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                raise HttpResponseError(response=response, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_for_scope.metadata = {"url": "/{scope}/providers/Microsoft.Authorization/roleEligibilityScheduleRequests"}

    @distributed_trace_async
    async def cancel(  # pylint: disable=inconsistent-return-statements
        self, scope: str, role_eligibility_schedule_request_name: str, **kwargs: Any
    ) -> None:
        """Cancels a pending role eligibility schedule request.

        :param scope: The scope of the role eligibility request to cancel. Required.
        :type scope: str
        :param role_eligibility_schedule_request_name: The name of the role eligibility request to
         cancel. Required.
        :type role_eligibility_schedule_request_name: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2022-04-01-preview")
        )
        cls: ClsType[None] = kwargs.pop("cls", None)

        request = build_cancel_request(
            scope=scope,
            role_eligibility_schedule_request_name=role_eligibility_schedule_request_name,
            api_version=api_version,
            template_url=self.cancel.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        if cls:
            return cls(pipeline_response, None, {})

    cancel.metadata = {
        "url": "/{scope}/providers/Microsoft.Authorization/roleEligibilityScheduleRequests/{roleEligibilityScheduleRequestName}/cancel"
    }

    @overload
    async def validate(
        self,
        scope: str,
        role_eligibility_schedule_request_name: str,
        parameters: _models.RoleEligibilityScheduleRequest,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.RoleEligibilityScheduleRequest:
        """Validates a new role eligibility schedule request.

        :param scope: The scope of the role eligibility request to validate. Required.
        :type scope: str
        :param role_eligibility_schedule_request_name: The name of the role eligibility request to
         validate. Required.
        :type role_eligibility_schedule_request_name: str
        :param parameters: Parameters for the role eligibility schedule request. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleEligibilityScheduleRequest or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    async def validate(
        self,
        scope: str,
        role_eligibility_schedule_request_name: str,
        parameters: IO,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.RoleEligibilityScheduleRequest:
        """Validates a new role eligibility schedule request.

        :param scope: The scope of the role eligibility request to validate. Required.
        :type scope: str
        :param role_eligibility_schedule_request_name: The name of the role eligibility request to
         validate. Required.
        :type role_eligibility_schedule_request_name: str
        :param parameters: Parameters for the role eligibility schedule request. Required.
        :type parameters: IO
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleEligibilityScheduleRequest or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace_async
    async def validate(
        self,
        scope: str,
        role_eligibility_schedule_request_name: str,
        parameters: Union[_models.RoleEligibilityScheduleRequest, IO],
        **kwargs: Any
    ) -> _models.RoleEligibilityScheduleRequest:
        """Validates a new role eligibility schedule request.

        :param scope: The scope of the role eligibility request to validate. Required.
        :type scope: str
        :param role_eligibility_schedule_request_name: The name of the role eligibility request to
         validate. Required.
        :type role_eligibility_schedule_request_name: str
        :param parameters: Parameters for the role eligibility schedule request. Is either a
         RoleEligibilityScheduleRequest type or a IO type. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest or IO
        :keyword content_type: Body Parameter content-type. Known values are: 'application/json'.
         Default value is None.
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleEligibilityScheduleRequest or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2022-04-01-preview")
        )
        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.RoleEligibilityScheduleRequest] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(parameters, (IOBase, bytes)):
            _content = parameters
        else:
            _json = self._serialize.body(parameters, "RoleEligibilityScheduleRequest")

        request = build_validate_request(
            scope=scope,
            role_eligibility_schedule_request_name=role_eligibility_schedule_request_name,
            api_version=api_version,
            content_type=content_type,
            json=_json,
            content=_content,
            template_url=self.validate.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            raise HttpResponseError(response=response, error_format=ARMErrorFormat)

        deserialized = self._deserialize("RoleEligibilityScheduleRequest", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    validate.metadata = {
        "url": "/{scope}/providers/Microsoft.Authorization/roleEligibilityScheduleRequests/{roleEligibilityScheduleRequestName}/validate"
    }
