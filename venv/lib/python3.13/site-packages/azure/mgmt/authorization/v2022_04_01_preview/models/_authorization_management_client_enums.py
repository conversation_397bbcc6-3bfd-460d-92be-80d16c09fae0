# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from enum import Enum
from azure.core import CaseInsensitiveEnumMeta


class PrincipalType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The principal type of the assigned principal ID."""

    USER = "User"
    GROUP = "Group"
    SERVICE_PRINCIPAL = "ServicePrincipal"
    FOREIGN_GROUP = "ForeignGroup"
    DEVICE = "Device"


class RequestType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of the role assignment schedule request. Eg: SelfActivate, AdminAssign etc."""

    ADMIN_ASSIGN = "AdminAssign"
    ADMIN_REMOVE = "AdminRemove"
    ADMIN_UPDATE = "AdminUpdate"
    ADMIN_EXTEND = "AdminExtend"
    ADMIN_RENEW = "AdminRenew"
    SELF_ACTIVATE = "SelfActivate"
    SELF_DEACTIVATE = "SelfDeactivate"
    SELF_EXTEND = "SelfExtend"
    SELF_RENEW = "SelfRenew"


class Status(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The status of the role assignment schedule request."""

    ACCEPTED = "Accepted"
    PENDING_EVALUATION = "PendingEvaluation"
    GRANTED = "Granted"
    DENIED = "Denied"
    PENDING_PROVISIONING = "PendingProvisioning"
    PROVISIONED = "Provisioned"
    PENDING_REVOCATION = "PendingRevocation"
    REVOKED = "Revoked"
    CANCELED = "Canceled"
    FAILED = "Failed"
    PENDING_APPROVAL_PROVISIONING = "PendingApprovalProvisioning"
    PENDING_APPROVAL = "PendingApproval"
    FAILED_AS_RESOURCE_IS_LOCKED = "FailedAsResourceIsLocked"
    PENDING_ADMIN_DECISION = "PendingAdminDecision"
    ADMIN_APPROVED = "AdminApproved"
    ADMIN_DENIED = "AdminDenied"
    TIMED_OUT = "TimedOut"
    PROVISIONING_STARTED = "ProvisioningStarted"
    INVALID = "Invalid"
    PENDING_SCHEDULE_CREATION = "PendingScheduleCreation"
    SCHEDULE_CREATED = "ScheduleCreated"
    PENDING_EXTERNAL_PROVISIONING = "PendingExternalProvisioning"


class Type(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """Type of the role assignment schedule expiration."""

    AFTER_DURATION = "AfterDuration"
    AFTER_DATE_TIME = "AfterDateTime"
    NO_EXPIRATION = "NoExpiration"
