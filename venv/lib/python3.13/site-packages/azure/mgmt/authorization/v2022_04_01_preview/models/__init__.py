# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._models_py3 import CloudErrorBody
from ._models_py3 import ExpandedProperties
from ._models_py3 import ExpandedPropertiesPrincipal
from ._models_py3 import ExpandedPropertiesRoleDefinition
from ._models_py3 import ExpandedPropertiesScope
from ._models_py3 import RoleAssignmentScheduleRequest
from ._models_py3 import RoleAssignmentScheduleRequestFilter
from ._models_py3 import RoleAssignmentScheduleRequestListResult
from ._models_py3 import RoleAssignmentScheduleRequestPropertiesScheduleInfo
from ._models_py3 import RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration
from ._models_py3 import RoleAssignmentScheduleRequestPropertiesTicketInfo
from ._models_py3 import RoleEligibilityScheduleRequest
from ._models_py3 import RoleEligibilityScheduleRequestFilter
from ._models_py3 import RoleEligibilityScheduleRequestListResult
from ._models_py3 import RoleEligibilityScheduleRequestPropertiesScheduleInfo
from ._models_py3 import RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration
from ._models_py3 import RoleEligibilityScheduleRequestPropertiesTicketInfo

from ._authorization_management_client_enums import PrincipalType
from ._authorization_management_client_enums import RequestType
from ._authorization_management_client_enums import Status
from ._authorization_management_client_enums import Type
from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "CloudErrorBody",
    "ExpandedProperties",
    "ExpandedPropertiesPrincipal",
    "ExpandedPropertiesRoleDefinition",
    "ExpandedPropertiesScope",
    "RoleAssignmentScheduleRequest",
    "RoleAssignmentScheduleRequestFilter",
    "RoleAssignmentScheduleRequestListResult",
    "RoleAssignmentScheduleRequestPropertiesScheduleInfo",
    "RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration",
    "RoleAssignmentScheduleRequestPropertiesTicketInfo",
    "RoleEligibilityScheduleRequest",
    "RoleEligibilityScheduleRequestFilter",
    "RoleEligibilityScheduleRequestListResult",
    "RoleEligibilityScheduleRequestPropertiesScheduleInfo",
    "RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration",
    "RoleEligibilityScheduleRequestPropertiesTicketInfo",
    "PrincipalType",
    "RequestType",
    "Status",
    "Type",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
