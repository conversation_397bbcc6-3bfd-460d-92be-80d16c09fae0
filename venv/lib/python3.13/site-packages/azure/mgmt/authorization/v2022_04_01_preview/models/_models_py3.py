# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

import datetime
from typing import Any, List, Optional, TYPE_CHECKING, Union

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class CloudErrorBody(_serialization.Model):
    """An error response from the service.

    :ivar code: An identifier for the error. Codes are invariant and are intended to be consumed
     programmatically.
    :vartype code: str
    :ivar message: A message describing the error, intended to be suitable for display in a user
     interface.
    :vartype message: str
    """

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
    }

    def __init__(self, *, code: Optional[str] = None, message: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword code: An identifier for the error. Codes are invariant and are intended to be consumed
         programmatically.
        :paramtype code: str
        :keyword message: A message describing the error, intended to be suitable for display in a user
         interface.
        :paramtype message: str
        """
        super().__init__(**kwargs)
        self.code = code
        self.message = message


class ExpandedProperties(_serialization.Model):
    """Expanded info of resource, role and principal.

    :ivar scope: Details of the resource scope.
    :vartype scope: ~azure.mgmt.authorization.v2022_04_01_preview.models.ExpandedPropertiesScope
    :ivar role_definition: Details of role definition.
    :vartype role_definition:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.ExpandedPropertiesRoleDefinition
    :ivar principal: Details of the principal.
    :vartype principal:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.ExpandedPropertiesPrincipal
    """

    _attribute_map = {
        "scope": {"key": "scope", "type": "ExpandedPropertiesScope"},
        "role_definition": {"key": "roleDefinition", "type": "ExpandedPropertiesRoleDefinition"},
        "principal": {"key": "principal", "type": "ExpandedPropertiesPrincipal"},
    }

    def __init__(
        self,
        *,
        scope: Optional["_models.ExpandedPropertiesScope"] = None,
        role_definition: Optional["_models.ExpandedPropertiesRoleDefinition"] = None,
        principal: Optional["_models.ExpandedPropertiesPrincipal"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: Details of the resource scope.
        :paramtype scope: ~azure.mgmt.authorization.v2022_04_01_preview.models.ExpandedPropertiesScope
        :keyword role_definition: Details of role definition.
        :paramtype role_definition:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.ExpandedPropertiesRoleDefinition
        :keyword principal: Details of the principal.
        :paramtype principal:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.ExpandedPropertiesPrincipal
        """
        super().__init__(**kwargs)
        self.scope = scope
        self.role_definition = role_definition
        self.principal = principal


class ExpandedPropertiesPrincipal(_serialization.Model):
    """Details of the principal.

    :ivar id: Id of the principal.
    :vartype id: str
    :ivar display_name: Display name of the principal.
    :vartype display_name: str
    :ivar email: Email id of the principal.
    :vartype email: str
    :ivar type: Type of the principal.
    :vartype type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "email": {"key": "email", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        email: Optional[str] = None,
        type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Id of the principal.
        :paramtype id: str
        :keyword display_name: Display name of the principal.
        :paramtype display_name: str
        :keyword email: Email id of the principal.
        :paramtype email: str
        :keyword type: Type of the principal.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.email = email
        self.type = type


class ExpandedPropertiesRoleDefinition(_serialization.Model):
    """Details of role definition.

    :ivar id: Id of the role definition.
    :vartype id: str
    :ivar display_name: Display name of the role definition.
    :vartype display_name: str
    :ivar type: Type of the role definition.
    :vartype type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Id of the role definition.
        :paramtype id: str
        :keyword display_name: Display name of the role definition.
        :paramtype display_name: str
        :keyword type: Type of the role definition.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type


class ExpandedPropertiesScope(_serialization.Model):
    """Details of the resource scope.

    :ivar id: Scope id of the resource.
    :vartype id: str
    :ivar display_name: Display name of the resource.
    :vartype display_name: str
    :ivar type: Type of the resource.
    :vartype type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Scope id of the resource.
        :paramtype id: str
        :keyword display_name: Display name of the resource.
        :paramtype display_name: str
        :keyword type: Type of the resource.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type


class RoleAssignmentScheduleRequest(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role Assignment schedule request.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role assignment schedule request ID.
    :vartype id: str
    :ivar name: The role assignment schedule request name.
    :vartype name: str
    :ivar type: The role assignment schedule request type.
    :vartype type: str
    :ivar scope: The role assignment schedule request scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2022_04_01_preview.models.PrincipalType
    :ivar request_type: The type of the role assignment schedule request. Eg: SelfActivate,
     AdminAssign etc. Known values are: "AdminAssign", "AdminRemove", "AdminUpdate", "AdminExtend",
     "AdminRenew", "SelfActivate", "SelfDeactivate", "SelfExtend", and "SelfRenew".
    :vartype request_type: str or ~azure.mgmt.authorization.v2022_04_01_preview.models.RequestType
    :ivar status: The status of the role assignment schedule request. Known values are: "Accepted",
     "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
     "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
     "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
     "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
     "ScheduleCreated", and "PendingExternalProvisioning".
    :vartype status: str or ~azure.mgmt.authorization.v2022_04_01_preview.models.Status
    :ivar approval_id: The approvalId of the role assignment schedule request.
    :vartype approval_id: str
    :ivar target_role_assignment_schedule_id: The resultant role assignment schedule id or the role
     assignment schedule id being updated.
    :vartype target_role_assignment_schedule_id: str
    :ivar target_role_assignment_schedule_instance_id: The role assignment schedule instance id
     being updated.
    :vartype target_role_assignment_schedule_instance_id: str
    :ivar schedule_info: Schedule info of the role assignment schedule.
    :vartype schedule_info:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleAssignmentScheduleRequestPropertiesScheduleInfo
    :ivar linked_role_eligibility_schedule_id: The linked role eligibility schedule id - to
     activate an eligibility.
    :vartype linked_role_eligibility_schedule_id: str
    :ivar justification: Justification for the role assignment.
    :vartype justification: str
    :ivar ticket_info: Ticket Info of the role assignment.
    :vartype ticket_info:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleAssignmentScheduleRequestPropertiesTicketInfo
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: DateTime when role assignment schedule request was created.
    :vartype created_on: ~datetime.datetime
    :ivar requestor_id: Id of the user who created this request.
    :vartype requestor_id: str
    :ivar expanded_properties: Additional properties of principal, scope and role definition.
    :vartype expanded_properties:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.ExpandedProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "scope": {"readonly": True},
        "principal_type": {"readonly": True},
        "status": {"readonly": True},
        "approval_id": {"readonly": True},
        "created_on": {"readonly": True},
        "requestor_id": {"readonly": True},
        "expanded_properties": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "request_type": {"key": "properties.requestType", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "approval_id": {"key": "properties.approvalId", "type": "str"},
        "target_role_assignment_schedule_id": {"key": "properties.targetRoleAssignmentScheduleId", "type": "str"},
        "target_role_assignment_schedule_instance_id": {
            "key": "properties.targetRoleAssignmentScheduleInstanceId",
            "type": "str",
        },
        "schedule_info": {
            "key": "properties.scheduleInfo",
            "type": "RoleAssignmentScheduleRequestPropertiesScheduleInfo",
        },
        "linked_role_eligibility_schedule_id": {"key": "properties.linkedRoleEligibilityScheduleId", "type": "str"},
        "justification": {"key": "properties.justification", "type": "str"},
        "ticket_info": {"key": "properties.ticketInfo", "type": "RoleAssignmentScheduleRequestPropertiesTicketInfo"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "requestor_id": {"key": "properties.requestorId", "type": "str"},
        "expanded_properties": {"key": "properties.expandedProperties", "type": "ExpandedProperties"},
    }

    def __init__(
        self,
        *,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        request_type: Optional[Union[str, "_models.RequestType"]] = None,
        target_role_assignment_schedule_id: Optional[str] = None,
        target_role_assignment_schedule_instance_id: Optional[str] = None,
        schedule_info: Optional["_models.RoleAssignmentScheduleRequestPropertiesScheduleInfo"] = None,
        linked_role_eligibility_schedule_id: Optional[str] = None,
        justification: Optional[str] = None,
        ticket_info: Optional["_models.RoleAssignmentScheduleRequestPropertiesTicketInfo"] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword request_type: The type of the role assignment schedule request. Eg: SelfActivate,
         AdminAssign etc. Known values are: "AdminAssign", "AdminRemove", "AdminUpdate", "AdminExtend",
         "AdminRenew", "SelfActivate", "SelfDeactivate", "SelfExtend", and "SelfRenew".
        :paramtype request_type: str or
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RequestType
        :keyword target_role_assignment_schedule_id: The resultant role assignment schedule id or the
         role assignment schedule id being updated.
        :paramtype target_role_assignment_schedule_id: str
        :keyword target_role_assignment_schedule_instance_id: The role assignment schedule instance id
         being updated.
        :paramtype target_role_assignment_schedule_instance_id: str
        :keyword schedule_info: Schedule info of the role assignment schedule.
        :paramtype schedule_info:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleAssignmentScheduleRequestPropertiesScheduleInfo
        :keyword linked_role_eligibility_schedule_id: The linked role eligibility schedule id - to
         activate an eligibility.
        :paramtype linked_role_eligibility_schedule_id: str
        :keyword justification: Justification for the role assignment.
        :paramtype justification: str
        :keyword ticket_info: Ticket Info of the role assignment.
        :paramtype ticket_info:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleAssignmentScheduleRequestPropertiesTicketInfo
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = None
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = None
        self.request_type = request_type
        self.status = None
        self.approval_id = None
        self.target_role_assignment_schedule_id = target_role_assignment_schedule_id
        self.target_role_assignment_schedule_instance_id = target_role_assignment_schedule_instance_id
        self.schedule_info = schedule_info
        self.linked_role_eligibility_schedule_id = linked_role_eligibility_schedule_id
        self.justification = justification
        self.ticket_info = ticket_info
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = None
        self.requestor_id = None
        self.expanded_properties = None


class RoleAssignmentScheduleRequestFilter(_serialization.Model):
    """Role assignment schedule request filter.

    :ivar principal_id: Returns role assignment requests of the specific principal.
    :vartype principal_id: str
    :ivar role_definition_id: Returns role assignment requests of the specific role definition.
    :vartype role_definition_id: str
    :ivar requestor_id: Returns role assignment requests created by specific principal.
    :vartype requestor_id: str
    :ivar status: Returns role assignment requests of specific status.
    :vartype status: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "role_definition_id": {"key": "roleDefinitionId", "type": "str"},
        "requestor_id": {"key": "requestorId", "type": "str"},
        "status": {"key": "status", "type": "str"},
    }

    def __init__(
        self,
        *,
        principal_id: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        requestor_id: Optional[str] = None,
        status: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword principal_id: Returns role assignment requests of the specific principal.
        :paramtype principal_id: str
        :keyword role_definition_id: Returns role assignment requests of the specific role definition.
        :paramtype role_definition_id: str
        :keyword requestor_id: Returns role assignment requests created by specific principal.
        :paramtype requestor_id: str
        :keyword status: Returns role assignment requests of specific status.
        :paramtype status: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.role_definition_id = role_definition_id
        self.requestor_id = requestor_id
        self.status = status


class RoleAssignmentScheduleRequestListResult(_serialization.Model):
    """Role assignment schedule request list operation result.

    :ivar value: Role assignment schedule request list.
    :vartype value:
     list[~azure.mgmt.authorization.v2022_04_01_preview.models.RoleAssignmentScheduleRequest]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleAssignmentScheduleRequest]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleAssignmentScheduleRequest"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Role assignment schedule request list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2022_04_01_preview.models.RoleAssignmentScheduleRequest]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleAssignmentScheduleRequestPropertiesScheduleInfo(_serialization.Model):
    """Schedule info of the role assignment schedule.

    :ivar start_date_time: Start DateTime of the role assignment schedule.
    :vartype start_date_time: ~datetime.datetime
    :ivar expiration: Expiration of the role assignment schedule.
    :vartype expiration:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration
    """

    _attribute_map = {
        "start_date_time": {"key": "startDateTime", "type": "iso-8601"},
        "expiration": {"key": "expiration", "type": "RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration"},
    }

    def __init__(
        self,
        *,
        start_date_time: Optional[datetime.datetime] = None,
        expiration: Optional["_models.RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword start_date_time: Start DateTime of the role assignment schedule.
        :paramtype start_date_time: ~datetime.datetime
        :keyword expiration: Expiration of the role assignment schedule.
        :paramtype expiration:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration
        """
        super().__init__(**kwargs)
        self.start_date_time = start_date_time
        self.expiration = expiration


class RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration(_serialization.Model):
    """Expiration of the role assignment schedule.

    :ivar type: Type of the role assignment schedule expiration. Known values are: "AfterDuration",
     "AfterDateTime", and "NoExpiration".
    :vartype type: str or ~azure.mgmt.authorization.v2022_04_01_preview.models.Type
    :ivar end_date_time: End DateTime of the role assignment schedule.
    :vartype end_date_time: ~datetime.datetime
    :ivar duration: Duration of the role assignment schedule in TimeSpan.
    :vartype duration: str
    """

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "end_date_time": {"key": "endDateTime", "type": "iso-8601"},
        "duration": {"key": "duration", "type": "str"},
    }

    def __init__(
        self,
        *,
        type: Optional[Union[str, "_models.Type"]] = None,
        end_date_time: Optional[datetime.datetime] = None,
        duration: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: Type of the role assignment schedule expiration. Known values are:
         "AfterDuration", "AfterDateTime", and "NoExpiration".
        :paramtype type: str or ~azure.mgmt.authorization.v2022_04_01_preview.models.Type
        :keyword end_date_time: End DateTime of the role assignment schedule.
        :paramtype end_date_time: ~datetime.datetime
        :keyword duration: Duration of the role assignment schedule in TimeSpan.
        :paramtype duration: str
        """
        super().__init__(**kwargs)
        self.type = type
        self.end_date_time = end_date_time
        self.duration = duration


class RoleAssignmentScheduleRequestPropertiesTicketInfo(_serialization.Model):
    """Ticket Info of the role assignment.

    :ivar ticket_number: Ticket number for the role assignment.
    :vartype ticket_number: str
    :ivar ticket_system: Ticket system name for the role assignment.
    :vartype ticket_system: str
    """

    _attribute_map = {
        "ticket_number": {"key": "ticketNumber", "type": "str"},
        "ticket_system": {"key": "ticketSystem", "type": "str"},
    }

    def __init__(
        self, *, ticket_number: Optional[str] = None, ticket_system: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword ticket_number: Ticket number for the role assignment.
        :paramtype ticket_number: str
        :keyword ticket_system: Ticket system name for the role assignment.
        :paramtype ticket_system: str
        """
        super().__init__(**kwargs)
        self.ticket_number = ticket_number
        self.ticket_system = ticket_system


class RoleEligibilityScheduleRequest(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role Eligibility schedule request.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role eligibility schedule request ID.
    :vartype id: str
    :ivar name: The role eligibility schedule request name.
    :vartype name: str
    :ivar type: The role eligibility schedule request type.
    :vartype type: str
    :ivar scope: The role eligibility schedule request scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2022_04_01_preview.models.PrincipalType
    :ivar request_type: The type of the role assignment schedule request. Eg: SelfActivate,
     AdminAssign etc. Known values are: "AdminAssign", "AdminRemove", "AdminUpdate", "AdminExtend",
     "AdminRenew", "SelfActivate", "SelfDeactivate", "SelfExtend", and "SelfRenew".
    :vartype request_type: str or ~azure.mgmt.authorization.v2022_04_01_preview.models.RequestType
    :ivar status: The status of the role eligibility schedule request. Known values are:
     "Accepted", "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
     "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
     "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
     "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
     "ScheduleCreated", and "PendingExternalProvisioning".
    :vartype status: str or ~azure.mgmt.authorization.v2022_04_01_preview.models.Status
    :ivar approval_id: The approvalId of the role eligibility schedule request.
    :vartype approval_id: str
    :ivar schedule_info: Schedule info of the role eligibility schedule.
    :vartype schedule_info:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequestPropertiesScheduleInfo
    :ivar target_role_eligibility_schedule_id: The resultant role eligibility schedule id or the
     role eligibility schedule id being updated.
    :vartype target_role_eligibility_schedule_id: str
    :ivar target_role_eligibility_schedule_instance_id: The role eligibility schedule instance id
     being updated.
    :vartype target_role_eligibility_schedule_instance_id: str
    :ivar justification: Justification for the role eligibility.
    :vartype justification: str
    :ivar ticket_info: Ticket Info of the role eligibility.
    :vartype ticket_info:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequestPropertiesTicketInfo
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: DateTime when role eligibility schedule request was created.
    :vartype created_on: ~datetime.datetime
    :ivar requestor_id: Id of the user who created this request.
    :vartype requestor_id: str
    :ivar expanded_properties: Additional properties of principal, scope and role definition.
    :vartype expanded_properties:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.ExpandedProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "scope": {"readonly": True},
        "principal_type": {"readonly": True},
        "status": {"readonly": True},
        "approval_id": {"readonly": True},
        "created_on": {"readonly": True},
        "requestor_id": {"readonly": True},
        "expanded_properties": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "request_type": {"key": "properties.requestType", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "approval_id": {"key": "properties.approvalId", "type": "str"},
        "schedule_info": {
            "key": "properties.scheduleInfo",
            "type": "RoleEligibilityScheduleRequestPropertiesScheduleInfo",
        },
        "target_role_eligibility_schedule_id": {"key": "properties.targetRoleEligibilityScheduleId", "type": "str"},
        "target_role_eligibility_schedule_instance_id": {
            "key": "properties.targetRoleEligibilityScheduleInstanceId",
            "type": "str",
        },
        "justification": {"key": "properties.justification", "type": "str"},
        "ticket_info": {"key": "properties.ticketInfo", "type": "RoleEligibilityScheduleRequestPropertiesTicketInfo"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "requestor_id": {"key": "properties.requestorId", "type": "str"},
        "expanded_properties": {"key": "properties.expandedProperties", "type": "ExpandedProperties"},
    }

    def __init__(
        self,
        *,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        request_type: Optional[Union[str, "_models.RequestType"]] = None,
        schedule_info: Optional["_models.RoleEligibilityScheduleRequestPropertiesScheduleInfo"] = None,
        target_role_eligibility_schedule_id: Optional[str] = None,
        target_role_eligibility_schedule_instance_id: Optional[str] = None,
        justification: Optional[str] = None,
        ticket_info: Optional["_models.RoleEligibilityScheduleRequestPropertiesTicketInfo"] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword request_type: The type of the role assignment schedule request. Eg: SelfActivate,
         AdminAssign etc. Known values are: "AdminAssign", "AdminRemove", "AdminUpdate", "AdminExtend",
         "AdminRenew", "SelfActivate", "SelfDeactivate", "SelfExtend", and "SelfRenew".
        :paramtype request_type: str or
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RequestType
        :keyword schedule_info: Schedule info of the role eligibility schedule.
        :paramtype schedule_info:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequestPropertiesScheduleInfo
        :keyword target_role_eligibility_schedule_id: The resultant role eligibility schedule id or the
         role eligibility schedule id being updated.
        :paramtype target_role_eligibility_schedule_id: str
        :keyword target_role_eligibility_schedule_instance_id: The role eligibility schedule instance
         id being updated.
        :paramtype target_role_eligibility_schedule_instance_id: str
        :keyword justification: Justification for the role eligibility.
        :paramtype justification: str
        :keyword ticket_info: Ticket Info of the role eligibility.
        :paramtype ticket_info:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequestPropertiesTicketInfo
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = None
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = None
        self.request_type = request_type
        self.status = None
        self.approval_id = None
        self.schedule_info = schedule_info
        self.target_role_eligibility_schedule_id = target_role_eligibility_schedule_id
        self.target_role_eligibility_schedule_instance_id = target_role_eligibility_schedule_instance_id
        self.justification = justification
        self.ticket_info = ticket_info
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = None
        self.requestor_id = None
        self.expanded_properties = None


class RoleEligibilityScheduleRequestFilter(_serialization.Model):
    """Role eligibility schedule request filter.

    :ivar principal_id: Returns role eligibility requests of the specific principal.
    :vartype principal_id: str
    :ivar role_definition_id: Returns role eligibility requests of the specific role definition.
    :vartype role_definition_id: str
    :ivar requestor_id: Returns role eligibility requests created by specific principal.
    :vartype requestor_id: str
    :ivar status: Returns role eligibility requests of specific status.
    :vartype status: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "role_definition_id": {"key": "roleDefinitionId", "type": "str"},
        "requestor_id": {"key": "requestorId", "type": "str"},
        "status": {"key": "status", "type": "str"},
    }

    def __init__(
        self,
        *,
        principal_id: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        requestor_id: Optional[str] = None,
        status: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword principal_id: Returns role eligibility requests of the specific principal.
        :paramtype principal_id: str
        :keyword role_definition_id: Returns role eligibility requests of the specific role definition.
        :paramtype role_definition_id: str
        :keyword requestor_id: Returns role eligibility requests created by specific principal.
        :paramtype requestor_id: str
        :keyword status: Returns role eligibility requests of specific status.
        :paramtype status: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.role_definition_id = role_definition_id
        self.requestor_id = requestor_id
        self.status = status


class RoleEligibilityScheduleRequestListResult(_serialization.Model):
    """Role eligibility schedule request list operation result.

    :ivar value: Role eligibility schedule request list.
    :vartype value:
     list[~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleEligibilityScheduleRequest]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleEligibilityScheduleRequest"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Role eligibility schedule request list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequest]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleEligibilityScheduleRequestPropertiesScheduleInfo(_serialization.Model):
    """Schedule info of the role eligibility schedule.

    :ivar start_date_time: Start DateTime of the role eligibility schedule.
    :vartype start_date_time: ~datetime.datetime
    :ivar expiration: Expiration of the role eligibility schedule.
    :vartype expiration:
     ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration
    """

    _attribute_map = {
        "start_date_time": {"key": "startDateTime", "type": "iso-8601"},
        "expiration": {"key": "expiration", "type": "RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration"},
    }

    def __init__(
        self,
        *,
        start_date_time: Optional[datetime.datetime] = None,
        expiration: Optional["_models.RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword start_date_time: Start DateTime of the role eligibility schedule.
        :paramtype start_date_time: ~datetime.datetime
        :keyword expiration: Expiration of the role eligibility schedule.
        :paramtype expiration:
         ~azure.mgmt.authorization.v2022_04_01_preview.models.RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration
        """
        super().__init__(**kwargs)
        self.start_date_time = start_date_time
        self.expiration = expiration


class RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration(_serialization.Model):
    """Expiration of the role eligibility schedule.

    :ivar type: Type of the role eligibility schedule expiration. Known values are:
     "AfterDuration", "AfterDateTime", and "NoExpiration".
    :vartype type: str or ~azure.mgmt.authorization.v2022_04_01_preview.models.Type
    :ivar end_date_time: End DateTime of the role eligibility schedule.
    :vartype end_date_time: ~datetime.datetime
    :ivar duration: Duration of the role eligibility schedule in TimeSpan.
    :vartype duration: str
    """

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "end_date_time": {"key": "endDateTime", "type": "iso-8601"},
        "duration": {"key": "duration", "type": "str"},
    }

    def __init__(
        self,
        *,
        type: Optional[Union[str, "_models.Type"]] = None,
        end_date_time: Optional[datetime.datetime] = None,
        duration: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: Type of the role eligibility schedule expiration. Known values are:
         "AfterDuration", "AfterDateTime", and "NoExpiration".
        :paramtype type: str or ~azure.mgmt.authorization.v2022_04_01_preview.models.Type
        :keyword end_date_time: End DateTime of the role eligibility schedule.
        :paramtype end_date_time: ~datetime.datetime
        :keyword duration: Duration of the role eligibility schedule in TimeSpan.
        :paramtype duration: str
        """
        super().__init__(**kwargs)
        self.type = type
        self.end_date_time = end_date_time
        self.duration = duration


class RoleEligibilityScheduleRequestPropertiesTicketInfo(_serialization.Model):
    """Ticket Info of the role eligibility.

    :ivar ticket_number: Ticket number for the role eligibility.
    :vartype ticket_number: str
    :ivar ticket_system: Ticket system name for the role eligibility.
    :vartype ticket_system: str
    """

    _attribute_map = {
        "ticket_number": {"key": "ticketNumber", "type": "str"},
        "ticket_system": {"key": "ticketSystem", "type": "str"},
    }

    def __init__(
        self, *, ticket_number: Optional[str] = None, ticket_system: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword ticket_number: Ticket number for the role eligibility.
        :paramtype ticket_number: str
        :keyword ticket_system: Ticket system name for the role eligibility.
        :paramtype ticket_system: str
        """
        super().__init__(**kwargs)
        self.ticket_number = ticket_number
        self.ticket_system = ticket_system
