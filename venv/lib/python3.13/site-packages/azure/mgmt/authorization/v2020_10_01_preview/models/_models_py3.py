# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

import datetime
from typing import Any, List, Optional, TYPE_CHECKING, Union

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class ApprovalSettings(_serialization.Model):
    """The approval settings.

    :ivar is_approval_required: Determines whether approval is required or not.
    :vartype is_approval_required: bool
    :ivar is_approval_required_for_extension: Determines whether approval is required for
     assignment extension.
    :vartype is_approval_required_for_extension: bool
    :ivar is_requestor_justification_required: Determine whether requestor justification is
     required.
    :vartype is_requestor_justification_required: bool
    :ivar approval_mode: The type of rule. Known values are: "SingleStage", "Serial", "Parallel",
     and "NoApproval".
    :vartype approval_mode: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ApprovalMode
    :ivar approval_stages: The approval stages of the request.
    :vartype approval_stages:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.ApprovalStage]
    """

    _attribute_map = {
        "is_approval_required": {"key": "isApprovalRequired", "type": "bool"},
        "is_approval_required_for_extension": {"key": "isApprovalRequiredForExtension", "type": "bool"},
        "is_requestor_justification_required": {"key": "isRequestorJustificationRequired", "type": "bool"},
        "approval_mode": {"key": "approvalMode", "type": "str"},
        "approval_stages": {"key": "approvalStages", "type": "[ApprovalStage]"},
    }

    def __init__(
        self,
        *,
        is_approval_required: Optional[bool] = None,
        is_approval_required_for_extension: Optional[bool] = None,
        is_requestor_justification_required: Optional[bool] = None,
        approval_mode: Optional[Union[str, "_models.ApprovalMode"]] = None,
        approval_stages: Optional[List["_models.ApprovalStage"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword is_approval_required: Determines whether approval is required or not.
        :paramtype is_approval_required: bool
        :keyword is_approval_required_for_extension: Determines whether approval is required for
         assignment extension.
        :paramtype is_approval_required_for_extension: bool
        :keyword is_requestor_justification_required: Determine whether requestor justification is
         required.
        :paramtype is_requestor_justification_required: bool
        :keyword approval_mode: The type of rule. Known values are: "SingleStage", "Serial",
         "Parallel", and "NoApproval".
        :paramtype approval_mode: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.ApprovalMode
        :keyword approval_stages: The approval stages of the request.
        :paramtype approval_stages:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.ApprovalStage]
        """
        super().__init__(**kwargs)
        self.is_approval_required = is_approval_required
        self.is_approval_required_for_extension = is_approval_required_for_extension
        self.is_requestor_justification_required = is_requestor_justification_required
        self.approval_mode = approval_mode
        self.approval_stages = approval_stages


class ApprovalStage(_serialization.Model):
    """The approval stage.

    :ivar approval_stage_time_out_in_days: The time in days when approval request would be timed
     out.
    :vartype approval_stage_time_out_in_days: int
    :ivar is_approver_justification_required: Determines whether approver need to provide
     justification for his decision.
    :vartype is_approver_justification_required: bool
    :ivar escalation_time_in_minutes: The time in minutes when the approval request would be
     escalated if the primary approver does not approve.
    :vartype escalation_time_in_minutes: int
    :ivar primary_approvers: The primary approver of the request.
    :vartype primary_approvers: list[~azure.mgmt.authorization.v2020_10_01_preview.models.UserSet]
    :ivar is_escalation_enabled: The value determine whether escalation feature is enabled.
    :vartype is_escalation_enabled: bool
    :ivar escalation_approvers: The escalation approver of the request.
    :vartype escalation_approvers:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.UserSet]
    """

    _attribute_map = {
        "approval_stage_time_out_in_days": {"key": "approvalStageTimeOutInDays", "type": "int"},
        "is_approver_justification_required": {"key": "isApproverJustificationRequired", "type": "bool"},
        "escalation_time_in_minutes": {"key": "escalationTimeInMinutes", "type": "int"},
        "primary_approvers": {"key": "primaryApprovers", "type": "[UserSet]"},
        "is_escalation_enabled": {"key": "isEscalationEnabled", "type": "bool"},
        "escalation_approvers": {"key": "escalationApprovers", "type": "[UserSet]"},
    }

    def __init__(
        self,
        *,
        approval_stage_time_out_in_days: Optional[int] = None,
        is_approver_justification_required: Optional[bool] = None,
        escalation_time_in_minutes: Optional[int] = None,
        primary_approvers: Optional[List["_models.UserSet"]] = None,
        is_escalation_enabled: Optional[bool] = None,
        escalation_approvers: Optional[List["_models.UserSet"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword approval_stage_time_out_in_days: The time in days when approval request would be timed
         out.
        :paramtype approval_stage_time_out_in_days: int
        :keyword is_approver_justification_required: Determines whether approver need to provide
         justification for his decision.
        :paramtype is_approver_justification_required: bool
        :keyword escalation_time_in_minutes: The time in minutes when the approval request would be
         escalated if the primary approver does not approve.
        :paramtype escalation_time_in_minutes: int
        :keyword primary_approvers: The primary approver of the request.
        :paramtype primary_approvers:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.UserSet]
        :keyword is_escalation_enabled: The value determine whether escalation feature is enabled.
        :paramtype is_escalation_enabled: bool
        :keyword escalation_approvers: The escalation approver of the request.
        :paramtype escalation_approvers:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.UserSet]
        """
        super().__init__(**kwargs)
        self.approval_stage_time_out_in_days = approval_stage_time_out_in_days
        self.is_approver_justification_required = is_approver_justification_required
        self.escalation_time_in_minutes = escalation_time_in_minutes
        self.primary_approvers = primary_approvers
        self.is_escalation_enabled = is_escalation_enabled
        self.escalation_approvers = escalation_approvers


class CloudErrorBody(_serialization.Model):
    """An error response from the service.

    :ivar code: An identifier for the error. Codes are invariant and are intended to be consumed
     programmatically.
    :vartype code: str
    :ivar message: A message describing the error, intended to be suitable for display in a user
     interface.
    :vartype message: str
    """

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
    }

    def __init__(self, *, code: Optional[str] = None, message: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword code: An identifier for the error. Codes are invariant and are intended to be consumed
         programmatically.
        :paramtype code: str
        :keyword message: A message describing the error, intended to be suitable for display in a user
         interface.
        :paramtype message: str
        """
        super().__init__(**kwargs)
        self.code = code
        self.message = message


class EligibleChildResource(_serialization.Model):
    """Eligible child resource.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The resource scope Id.
    :vartype id: str
    :ivar name: The resource name.
    :vartype name: str
    :ivar type: The resource type.
    :vartype type: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None


class EligibleChildResourcesListResult(_serialization.Model):
    """Eligible child resources list operation result.

    :ivar value: Eligible child resource list.
    :vartype value:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.EligibleChildResource]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[EligibleChildResource]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.EligibleChildResource"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Eligible child resource list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.EligibleChildResource]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class ErrorAdditionalInfo(_serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: JSON
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(_serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.authorization.v2020_10_01_preview.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(_serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed
    operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.mgmt.authorization.v2020_10_01_preview.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["_models.ErrorDetail"] = None, **kwargs: Any) -> None:
        """
        :keyword error: The error object.
        :paramtype error: ~azure.mgmt.authorization.v2020_10_01_preview.models.ErrorDetail
        """
        super().__init__(**kwargs)
        self.error = error


class ExpandedProperties(_serialization.Model):
    """ExpandedProperties.

    :ivar scope: Details of the resource scope.
    :vartype scope: ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedPropertiesScope
    :ivar role_definition: Details of role definition.
    :vartype role_definition:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedPropertiesRoleDefinition
    :ivar principal: Details of the principal.
    :vartype principal:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedPropertiesPrincipal
    """

    _attribute_map = {
        "scope": {"key": "scope", "type": "ExpandedPropertiesScope"},
        "role_definition": {"key": "roleDefinition", "type": "ExpandedPropertiesRoleDefinition"},
        "principal": {"key": "principal", "type": "ExpandedPropertiesPrincipal"},
    }

    def __init__(
        self,
        *,
        scope: Optional["_models.ExpandedPropertiesScope"] = None,
        role_definition: Optional["_models.ExpandedPropertiesRoleDefinition"] = None,
        principal: Optional["_models.ExpandedPropertiesPrincipal"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: Details of the resource scope.
        :paramtype scope: ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedPropertiesScope
        :keyword role_definition: Details of role definition.
        :paramtype role_definition:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedPropertiesRoleDefinition
        :keyword principal: Details of the principal.
        :paramtype principal:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedPropertiesPrincipal
        """
        super().__init__(**kwargs)
        self.scope = scope
        self.role_definition = role_definition
        self.principal = principal


class ExpandedPropertiesPrincipal(_serialization.Model):
    """Details of the principal.

    :ivar id: Id of the principal.
    :vartype id: str
    :ivar display_name: Display name of the principal.
    :vartype display_name: str
    :ivar email: Email id of the principal.
    :vartype email: str
    :ivar type: Type of the principal.
    :vartype type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "email": {"key": "email", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        email: Optional[str] = None,
        type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Id of the principal.
        :paramtype id: str
        :keyword display_name: Display name of the principal.
        :paramtype display_name: str
        :keyword email: Email id of the principal.
        :paramtype email: str
        :keyword type: Type of the principal.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.email = email
        self.type = type


class ExpandedPropertiesRoleDefinition(_serialization.Model):
    """Details of role definition.

    :ivar id: Id of the role definition.
    :vartype id: str
    :ivar display_name: Display name of the role definition.
    :vartype display_name: str
    :ivar type: Type of the role definition.
    :vartype type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Id of the role definition.
        :paramtype id: str
        :keyword display_name: Display name of the role definition.
        :paramtype display_name: str
        :keyword type: Type of the role definition.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type


class ExpandedPropertiesScope(_serialization.Model):
    """Details of the resource scope.

    :ivar id: Scope id of the resource.
    :vartype id: str
    :ivar display_name: Display name of the resource.
    :vartype display_name: str
    :ivar type: Type of the resource.
    :vartype type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Scope id of the resource.
        :paramtype id: str
        :keyword display_name: Display name of the resource.
        :paramtype display_name: str
        :keyword type: Type of the resource.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type


class Permission(_serialization.Model):
    """Role definition permissions.

    :ivar actions: Allowed actions.
    :vartype actions: list[str]
    :ivar not_actions: Denied actions.
    :vartype not_actions: list[str]
    :ivar data_actions: Allowed Data actions.
    :vartype data_actions: list[str]
    :ivar not_data_actions: Denied Data actions.
    :vartype not_data_actions: list[str]
    """

    _attribute_map = {
        "actions": {"key": "actions", "type": "[str]"},
        "not_actions": {"key": "notActions", "type": "[str]"},
        "data_actions": {"key": "dataActions", "type": "[str]"},
        "not_data_actions": {"key": "notDataActions", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        actions: Optional[List[str]] = None,
        not_actions: Optional[List[str]] = None,
        data_actions: Optional[List[str]] = None,
        not_data_actions: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword actions: Allowed actions.
        :paramtype actions: list[str]
        :keyword not_actions: Denied actions.
        :paramtype not_actions: list[str]
        :keyword data_actions: Allowed Data actions.
        :paramtype data_actions: list[str]
        :keyword not_data_actions: Denied Data actions.
        :paramtype not_data_actions: list[str]
        """
        super().__init__(**kwargs)
        self.actions = actions
        self.not_actions = not_actions
        self.data_actions = data_actions
        self.not_data_actions = not_data_actions


class PolicyAssignmentProperties(_serialization.Model):
    """PolicyAssignmentProperties.

    :ivar scope: Details of the resource scope.
    :vartype scope:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PolicyAssignmentPropertiesScope
    :ivar role_definition: Details of role definition.
    :vartype role_definition:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PolicyAssignmentPropertiesRoleDefinition
    :ivar policy: Details of the policy.
    :vartype policy:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PolicyAssignmentPropertiesPolicy
    """

    _attribute_map = {
        "scope": {"key": "scope", "type": "PolicyAssignmentPropertiesScope"},
        "role_definition": {"key": "roleDefinition", "type": "PolicyAssignmentPropertiesRoleDefinition"},
        "policy": {"key": "policy", "type": "PolicyAssignmentPropertiesPolicy"},
    }

    def __init__(
        self,
        *,
        scope: Optional["_models.PolicyAssignmentPropertiesScope"] = None,
        role_definition: Optional["_models.PolicyAssignmentPropertiesRoleDefinition"] = None,
        policy: Optional["_models.PolicyAssignmentPropertiesPolicy"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: Details of the resource scope.
        :paramtype scope:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.PolicyAssignmentPropertiesScope
        :keyword role_definition: Details of role definition.
        :paramtype role_definition:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.PolicyAssignmentPropertiesRoleDefinition
        :keyword policy: Details of the policy.
        :paramtype policy:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.PolicyAssignmentPropertiesPolicy
        """
        super().__init__(**kwargs)
        self.scope = scope
        self.role_definition = role_definition
        self.policy = policy


class PolicyAssignmentPropertiesPolicy(_serialization.Model):
    """Details of the policy.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: Id of the policy.
    :vartype id: str
    :ivar last_modified_by: The name of the entity last modified it.
    :vartype last_modified_by: ~azure.mgmt.authorization.v2020_10_01_preview.models.Principal
    :ivar last_modified_date_time: The last modified date time.
    :vartype last_modified_date_time: ~datetime.datetime
    """

    _validation = {
        "last_modified_by": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "last_modified_by": {"key": "lastModifiedBy", "type": "Principal"},
        "last_modified_date_time": {"key": "lastModifiedDateTime", "type": "iso-8601"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        last_modified_date_time: Optional[datetime.datetime] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Id of the policy.
        :paramtype id: str
        :keyword last_modified_date_time: The last modified date time.
        :paramtype last_modified_date_time: ~datetime.datetime
        """
        super().__init__(**kwargs)
        self.id = id
        self.last_modified_by = None
        self.last_modified_date_time = last_modified_date_time


class PolicyAssignmentPropertiesRoleDefinition(_serialization.Model):
    """Details of role definition.

    :ivar id: Id of the role definition.
    :vartype id: str
    :ivar display_name: Display name of the role definition.
    :vartype display_name: str
    :ivar type: Type of the role definition.
    :vartype type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Id of the role definition.
        :paramtype id: str
        :keyword display_name: Display name of the role definition.
        :paramtype display_name: str
        :keyword type: Type of the role definition.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type


class PolicyAssignmentPropertiesScope(_serialization.Model):
    """Details of the resource scope.

    :ivar id: Scope id of the resource.
    :vartype id: str
    :ivar display_name: Display name of the resource.
    :vartype display_name: str
    :ivar type: Type of the resource.
    :vartype type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Scope id of the resource.
        :paramtype id: str
        :keyword display_name: Display name of the resource.
        :paramtype display_name: str
        :keyword type: Type of the resource.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type


class PolicyProperties(_serialization.Model):
    """PolicyProperties.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar scope: Details of the resource scope.
    :vartype scope: ~azure.mgmt.authorization.v2020_10_01_preview.models.PolicyPropertiesScope
    """

    _validation = {
        "scope": {"readonly": True},
    }

    _attribute_map = {
        "scope": {"key": "scope", "type": "PolicyPropertiesScope"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.scope = None


class PolicyPropertiesScope(_serialization.Model):
    """Details of the resource scope.

    :ivar id: Scope id of the resource.
    :vartype id: str
    :ivar display_name: Display name of the resource.
    :vartype display_name: str
    :ivar type: Type of the resource.
    :vartype type: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: Scope id of the resource.
        :paramtype id: str
        :keyword display_name: Display name of the resource.
        :paramtype display_name: str
        :keyword type: Type of the resource.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type


class Principal(_serialization.Model):
    """The name of the entity last modified it.

    :ivar id: The id of the principal made changes.
    :vartype id: str
    :ivar display_name: The name of the principal made changes.
    :vartype display_name: str
    :ivar type: Type of principal such as user , group etc.
    :vartype type: str
    :ivar email: Email of principal.
    :vartype email: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "email": {"key": "email", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        email: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the principal made changes.
        :paramtype id: str
        :keyword display_name: The name of the principal made changes.
        :paramtype display_name: str
        :keyword type: Type of principal such as user , group etc.
        :paramtype type: str
        :keyword email: Email of principal.
        :paramtype email: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type
        self.email = email


class RoleAssignment(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role Assignments.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role assignment ID.
    :vartype id: str
    :ivar name: The role assignment name.
    :vartype name: str
    :ivar type: The role assignment type.
    :vartype type: str
    :ivar scope: The role assignment scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
    :ivar description: Description of role assignment.
    :vartype description: str
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: Time it was created.
    :vartype created_on: ~datetime.datetime
    :ivar updated_on: Time it was updated.
    :vartype updated_on: ~datetime.datetime
    :ivar created_by: Id of the user who created the assignment.
    :vartype created_by: str
    :ivar updated_by: Id of the user who updated the assignment.
    :vartype updated_by: str
    :ivar delegated_managed_identity_resource_id: Id of the delegated managed identity resource.
    :vartype delegated_managed_identity_resource_id: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "scope": {"readonly": True},
        "created_on": {"readonly": True},
        "updated_on": {"readonly": True},
        "created_by": {"readonly": True},
        "updated_by": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "updated_on": {"key": "properties.updatedOn", "type": "iso-8601"},
        "created_by": {"key": "properties.createdBy", "type": "str"},
        "updated_by": {"key": "properties.updatedBy", "type": "str"},
        "delegated_managed_identity_resource_id": {
            "key": "properties.delegatedManagedIdentityResourceId",
            "type": "str",
        },
    }

    def __init__(
        self,
        *,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        principal_type: Optional[Union[str, "_models.PrincipalType"]] = None,
        description: Optional[str] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        delegated_managed_identity_resource_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
        :paramtype principal_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
        :keyword description: Description of role assignment.
        :paramtype description: str
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        :keyword delegated_managed_identity_resource_id: Id of the delegated managed identity resource.
        :paramtype delegated_managed_identity_resource_id: str
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = None
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.description = description
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = None
        self.updated_on = None
        self.created_by = None
        self.updated_by = None
        self.delegated_managed_identity_resource_id = delegated_managed_identity_resource_id


class RoleAssignmentCreateParameters(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role assignment create parameters.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar scope: The role assignment scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID. Required.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID. Required.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
    :ivar description: Description of role assignment.
    :vartype description: str
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: Time it was created.
    :vartype created_on: ~datetime.datetime
    :ivar updated_on: Time it was updated.
    :vartype updated_on: ~datetime.datetime
    :ivar created_by: Id of the user who created the assignment.
    :vartype created_by: str
    :ivar updated_by: Id of the user who updated the assignment.
    :vartype updated_by: str
    :ivar delegated_managed_identity_resource_id: Id of the delegated managed identity resource.
    :vartype delegated_managed_identity_resource_id: str
    """

    _validation = {
        "scope": {"readonly": True},
        "role_definition_id": {"required": True},
        "principal_id": {"required": True},
        "created_on": {"readonly": True},
        "updated_on": {"readonly": True},
        "created_by": {"readonly": True},
        "updated_by": {"readonly": True},
    }

    _attribute_map = {
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "updated_on": {"key": "properties.updatedOn", "type": "iso-8601"},
        "created_by": {"key": "properties.createdBy", "type": "str"},
        "updated_by": {"key": "properties.updatedBy", "type": "str"},
        "delegated_managed_identity_resource_id": {
            "key": "properties.delegatedManagedIdentityResourceId",
            "type": "str",
        },
    }

    def __init__(
        self,
        *,
        role_definition_id: str,
        principal_id: str,
        principal_type: Optional[Union[str, "_models.PrincipalType"]] = None,
        description: Optional[str] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        delegated_managed_identity_resource_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_definition_id: The role definition ID. Required.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID. Required.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
        :paramtype principal_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
        :keyword description: Description of role assignment.
        :paramtype description: str
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        :keyword delegated_managed_identity_resource_id: Id of the delegated managed identity resource.
        :paramtype delegated_managed_identity_resource_id: str
        """
        super().__init__(**kwargs)
        self.scope = None
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.description = description
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = None
        self.updated_on = None
        self.created_by = None
        self.updated_by = None
        self.delegated_managed_identity_resource_id = delegated_managed_identity_resource_id


class RoleAssignmentFilter(_serialization.Model):
    """Role Assignments filter.

    :ivar principal_id: Returns role assignment of the specific principal.
    :vartype principal_id: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
    }

    def __init__(self, *, principal_id: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword principal_id: Returns role assignment of the specific principal.
        :paramtype principal_id: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id


class RoleAssignmentListResult(_serialization.Model):
    """Role assignment list operation result.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar value: Role assignment list.
    :vartype value: list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _validation = {
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleAssignment]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: Optional[List["_models.RoleAssignment"]] = None, **kwargs: Any) -> None:
        """
        :keyword value: Role assignment list.
        :paramtype value: list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class RoleAssignmentSchedule(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role Assignment schedule.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role assignment schedule Id.
    :vartype id: str
    :ivar name: The role assignment schedule name.
    :vartype name: str
    :ivar type: The role assignment schedule type.
    :vartype type: str
    :ivar scope: The role assignment schedule scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
    :ivar role_assignment_schedule_request_id: The id of roleAssignmentScheduleRequest used to
     create this roleAssignmentSchedule.
    :vartype role_assignment_schedule_request_id: str
    :ivar linked_role_eligibility_schedule_id: The id of roleEligibilitySchedule used to activated
     this roleAssignmentSchedule.
    :vartype linked_role_eligibility_schedule_id: str
    :ivar assignment_type: Assignment type of the role assignment schedule. Known values are:
     "Activated" and "Assigned".
    :vartype assignment_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.AssignmentType
    :ivar member_type: Membership type of the role assignment schedule. Known values are:
     "Inherited", "Direct", and "Group".
    :vartype member_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.MemberType
    :ivar status: The status of the role assignment schedule. Known values are: "Accepted",
     "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
     "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
     "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
     "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
     "ScheduleCreated", and "PendingExternalProvisioning".
    :vartype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
    :ivar start_date_time: Start DateTime when role assignment schedule.
    :vartype start_date_time: ~datetime.datetime
    :ivar end_date_time: End DateTime when role assignment schedule.
    :vartype end_date_time: ~datetime.datetime
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: DateTime when role assignment schedule was created.
    :vartype created_on: ~datetime.datetime
    :ivar updated_on: DateTime when role assignment schedule was modified.
    :vartype updated_on: ~datetime.datetime
    :ivar expanded_properties: Additional properties of principal, scope and role definition.
    :vartype expanded_properties:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "role_assignment_schedule_request_id": {"key": "properties.roleAssignmentScheduleRequestId", "type": "str"},
        "linked_role_eligibility_schedule_id": {"key": "properties.linkedRoleEligibilityScheduleId", "type": "str"},
        "assignment_type": {"key": "properties.assignmentType", "type": "str"},
        "member_type": {"key": "properties.memberType", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "start_date_time": {"key": "properties.startDateTime", "type": "iso-8601"},
        "end_date_time": {"key": "properties.endDateTime", "type": "iso-8601"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "updated_on": {"key": "properties.updatedOn", "type": "iso-8601"},
        "expanded_properties": {"key": "properties.expandedProperties", "type": "ExpandedProperties"},
    }

    def __init__(
        self,
        *,
        scope: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        principal_type: Optional[Union[str, "_models.PrincipalType"]] = None,
        role_assignment_schedule_request_id: Optional[str] = None,
        linked_role_eligibility_schedule_id: Optional[str] = None,
        assignment_type: Optional[Union[str, "_models.AssignmentType"]] = None,
        member_type: Optional[Union[str, "_models.MemberType"]] = None,
        status: Optional[Union[str, "_models.Status"]] = None,
        start_date_time: Optional[datetime.datetime] = None,
        end_date_time: Optional[datetime.datetime] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        created_on: Optional[datetime.datetime] = None,
        updated_on: Optional[datetime.datetime] = None,
        expanded_properties: Optional["_models.ExpandedProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: The role assignment schedule scope.
        :paramtype scope: str
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
        :paramtype principal_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
        :keyword role_assignment_schedule_request_id: The id of roleAssignmentScheduleRequest used to
         create this roleAssignmentSchedule.
        :paramtype role_assignment_schedule_request_id: str
        :keyword linked_role_eligibility_schedule_id: The id of roleEligibilitySchedule used to
         activated this roleAssignmentSchedule.
        :paramtype linked_role_eligibility_schedule_id: str
        :keyword assignment_type: Assignment type of the role assignment schedule. Known values are:
         "Activated" and "Assigned".
        :paramtype assignment_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.AssignmentType
        :keyword member_type: Membership type of the role assignment schedule. Known values are:
         "Inherited", "Direct", and "Group".
        :paramtype member_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.MemberType
        :keyword status: The status of the role assignment schedule. Known values are: "Accepted",
         "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
         "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
         "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
         "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
         "ScheduleCreated", and "PendingExternalProvisioning".
        :paramtype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
        :keyword start_date_time: Start DateTime when role assignment schedule.
        :paramtype start_date_time: ~datetime.datetime
        :keyword end_date_time: End DateTime when role assignment schedule.
        :paramtype end_date_time: ~datetime.datetime
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        :keyword created_on: DateTime when role assignment schedule was created.
        :paramtype created_on: ~datetime.datetime
        :keyword updated_on: DateTime when role assignment schedule was modified.
        :paramtype updated_on: ~datetime.datetime
        :keyword expanded_properties: Additional properties of principal, scope and role definition.
        :paramtype expanded_properties:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = scope
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.role_assignment_schedule_request_id = role_assignment_schedule_request_id
        self.linked_role_eligibility_schedule_id = linked_role_eligibility_schedule_id
        self.assignment_type = assignment_type
        self.member_type = member_type
        self.status = status
        self.start_date_time = start_date_time
        self.end_date_time = end_date_time
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = created_on
        self.updated_on = updated_on
        self.expanded_properties = expanded_properties


class RoleAssignmentScheduleFilter(_serialization.Model):
    """Role assignment schedule filter.

    :ivar principal_id: Returns role assignment schedule of the specific principal.
    :vartype principal_id: str
    :ivar role_definition_id: Returns role assignment schedule of the specific role definition.
    :vartype role_definition_id: str
    :ivar status: Returns role assignment schedule instances of the specific status.
    :vartype status: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "role_definition_id": {"key": "roleDefinitionId", "type": "str"},
        "status": {"key": "status", "type": "str"},
    }

    def __init__(
        self,
        *,
        principal_id: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        status: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword principal_id: Returns role assignment schedule of the specific principal.
        :paramtype principal_id: str
        :keyword role_definition_id: Returns role assignment schedule of the specific role definition.
        :paramtype role_definition_id: str
        :keyword status: Returns role assignment schedule instances of the specific status.
        :paramtype status: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.role_definition_id = role_definition_id
        self.status = status


class RoleAssignmentScheduleInstance(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Information about current or upcoming role assignment schedule instance.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role assignment schedule instance ID.
    :vartype id: str
    :ivar name: The role assignment schedule instance name.
    :vartype name: str
    :ivar type: The role assignment schedule instance type.
    :vartype type: str
    :ivar scope: The role assignment schedule scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
    :ivar role_assignment_schedule_id: Id of the master role assignment schedule.
    :vartype role_assignment_schedule_id: str
    :ivar origin_role_assignment_id: Role Assignment Id in external system.
    :vartype origin_role_assignment_id: str
    :ivar status: The status of the role assignment schedule instance. Known values are:
     "Accepted", "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
     "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
     "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
     "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
     "ScheduleCreated", and "PendingExternalProvisioning".
    :vartype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
    :ivar start_date_time: The startDateTime of the role assignment schedule instance.
    :vartype start_date_time: ~datetime.datetime
    :ivar end_date_time: The endDateTime of the role assignment schedule instance.
    :vartype end_date_time: ~datetime.datetime
    :ivar linked_role_eligibility_schedule_id: roleEligibilityScheduleId used to activate.
    :vartype linked_role_eligibility_schedule_id: str
    :ivar linked_role_eligibility_schedule_instance_id: roleEligibilityScheduleInstanceId linked to
     this roleAssignmentScheduleInstance.
    :vartype linked_role_eligibility_schedule_instance_id: str
    :ivar assignment_type: Assignment type of the role assignment schedule. Known values are:
     "Activated" and "Assigned".
    :vartype assignment_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.AssignmentType
    :ivar member_type: Membership type of the role assignment schedule. Known values are:
     "Inherited", "Direct", and "Group".
    :vartype member_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.MemberType
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: DateTime when role assignment schedule was created.
    :vartype created_on: ~datetime.datetime
    :ivar expanded_properties: Additional properties of principal, scope and role definition.
    :vartype expanded_properties:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "role_assignment_schedule_id": {"key": "properties.roleAssignmentScheduleId", "type": "str"},
        "origin_role_assignment_id": {"key": "properties.originRoleAssignmentId", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "start_date_time": {"key": "properties.startDateTime", "type": "iso-8601"},
        "end_date_time": {"key": "properties.endDateTime", "type": "iso-8601"},
        "linked_role_eligibility_schedule_id": {"key": "properties.linkedRoleEligibilityScheduleId", "type": "str"},
        "linked_role_eligibility_schedule_instance_id": {
            "key": "properties.linkedRoleEligibilityScheduleInstanceId",
            "type": "str",
        },
        "assignment_type": {"key": "properties.assignmentType", "type": "str"},
        "member_type": {"key": "properties.memberType", "type": "str"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "expanded_properties": {"key": "properties.expandedProperties", "type": "ExpandedProperties"},
    }

    def __init__(
        self,
        *,
        scope: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        principal_type: Optional[Union[str, "_models.PrincipalType"]] = None,
        role_assignment_schedule_id: Optional[str] = None,
        origin_role_assignment_id: Optional[str] = None,
        status: Optional[Union[str, "_models.Status"]] = None,
        start_date_time: Optional[datetime.datetime] = None,
        end_date_time: Optional[datetime.datetime] = None,
        linked_role_eligibility_schedule_id: Optional[str] = None,
        linked_role_eligibility_schedule_instance_id: Optional[str] = None,
        assignment_type: Optional[Union[str, "_models.AssignmentType"]] = None,
        member_type: Optional[Union[str, "_models.MemberType"]] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        created_on: Optional[datetime.datetime] = None,
        expanded_properties: Optional["_models.ExpandedProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: The role assignment schedule scope.
        :paramtype scope: str
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
        :paramtype principal_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
        :keyword role_assignment_schedule_id: Id of the master role assignment schedule.
        :paramtype role_assignment_schedule_id: str
        :keyword origin_role_assignment_id: Role Assignment Id in external system.
        :paramtype origin_role_assignment_id: str
        :keyword status: The status of the role assignment schedule instance. Known values are:
         "Accepted", "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
         "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
         "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
         "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
         "ScheduleCreated", and "PendingExternalProvisioning".
        :paramtype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
        :keyword start_date_time: The startDateTime of the role assignment schedule instance.
        :paramtype start_date_time: ~datetime.datetime
        :keyword end_date_time: The endDateTime of the role assignment schedule instance.
        :paramtype end_date_time: ~datetime.datetime
        :keyword linked_role_eligibility_schedule_id: roleEligibilityScheduleId used to activate.
        :paramtype linked_role_eligibility_schedule_id: str
        :keyword linked_role_eligibility_schedule_instance_id: roleEligibilityScheduleInstanceId linked
         to this roleAssignmentScheduleInstance.
        :paramtype linked_role_eligibility_schedule_instance_id: str
        :keyword assignment_type: Assignment type of the role assignment schedule. Known values are:
         "Activated" and "Assigned".
        :paramtype assignment_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.AssignmentType
        :keyword member_type: Membership type of the role assignment schedule. Known values are:
         "Inherited", "Direct", and "Group".
        :paramtype member_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.MemberType
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        :keyword created_on: DateTime when role assignment schedule was created.
        :paramtype created_on: ~datetime.datetime
        :keyword expanded_properties: Additional properties of principal, scope and role definition.
        :paramtype expanded_properties:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = scope
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.role_assignment_schedule_id = role_assignment_schedule_id
        self.origin_role_assignment_id = origin_role_assignment_id
        self.status = status
        self.start_date_time = start_date_time
        self.end_date_time = end_date_time
        self.linked_role_eligibility_schedule_id = linked_role_eligibility_schedule_id
        self.linked_role_eligibility_schedule_instance_id = linked_role_eligibility_schedule_instance_id
        self.assignment_type = assignment_type
        self.member_type = member_type
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = created_on
        self.expanded_properties = expanded_properties


class RoleAssignmentScheduleInstanceFilter(_serialization.Model):
    """Role assignment schedule instance filter.

    :ivar principal_id: Returns role assignment schedule instances of the specific principal.
    :vartype principal_id: str
    :ivar role_definition_id: Returns role assignment schedule instances of the specific role
     definition.
    :vartype role_definition_id: str
    :ivar status: Returns role assignment schedule instances of the specific status.
    :vartype status: str
    :ivar role_assignment_schedule_id: Returns role assignment schedule instances belonging to a
     specific role assignment schedule.
    :vartype role_assignment_schedule_id: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "role_definition_id": {"key": "roleDefinitionId", "type": "str"},
        "status": {"key": "status", "type": "str"},
        "role_assignment_schedule_id": {"key": "roleAssignmentScheduleId", "type": "str"},
    }

    def __init__(
        self,
        *,
        principal_id: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        status: Optional[str] = None,
        role_assignment_schedule_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword principal_id: Returns role assignment schedule instances of the specific principal.
        :paramtype principal_id: str
        :keyword role_definition_id: Returns role assignment schedule instances of the specific role
         definition.
        :paramtype role_definition_id: str
        :keyword status: Returns role assignment schedule instances of the specific status.
        :paramtype status: str
        :keyword role_assignment_schedule_id: Returns role assignment schedule instances belonging to a
         specific role assignment schedule.
        :paramtype role_assignment_schedule_id: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.role_definition_id = role_definition_id
        self.status = status
        self.role_assignment_schedule_id = role_assignment_schedule_id


class RoleAssignmentScheduleInstanceListResult(_serialization.Model):
    """Role assignment schedule instance list operation result.

    :ivar value: Role assignment schedule instance list.
    :vartype value:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleInstance]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleAssignmentScheduleInstance]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleAssignmentScheduleInstance"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Role assignment schedule instance list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleInstance]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleAssignmentScheduleListResult(_serialization.Model):
    """Role assignment schedule list operation result.

    :ivar value: Role assignment schedule list.
    :vartype value:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentSchedule]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleAssignmentSchedule]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleAssignmentSchedule"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Role assignment schedule list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentSchedule]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleAssignmentScheduleRequest(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role Assignment schedule request.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role assignment schedule request ID.
    :vartype id: str
    :ivar name: The role assignment schedule request name.
    :vartype name: str
    :ivar type: The role assignment schedule request type.
    :vartype type: str
    :ivar scope: The role assignment schedule request scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
    :ivar request_type: The type of the role assignment schedule request. Eg: SelfActivate,
     AdminAssign etc. Known values are: "AdminAssign", "AdminRemove", "AdminUpdate", "AdminExtend",
     "AdminRenew", "SelfActivate", "SelfDeactivate", "SelfExtend", and "SelfRenew".
    :vartype request_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.RequestType
    :ivar status: The status of the role assignment schedule request. Known values are: "Accepted",
     "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
     "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
     "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
     "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
     "ScheduleCreated", and "PendingExternalProvisioning".
    :vartype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
    :ivar approval_id: The approvalId of the role assignment schedule request.
    :vartype approval_id: str
    :ivar target_role_assignment_schedule_id: The resultant role assignment schedule id or the role
     assignment schedule id being updated.
    :vartype target_role_assignment_schedule_id: str
    :ivar target_role_assignment_schedule_instance_id: The role assignment schedule instance id
     being updated.
    :vartype target_role_assignment_schedule_instance_id: str
    :ivar schedule_info: Schedule info of the role assignment schedule.
    :vartype schedule_info:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleRequestPropertiesScheduleInfo
    :ivar linked_role_eligibility_schedule_id: The linked role eligibility schedule id - to
     activate an eligibility.
    :vartype linked_role_eligibility_schedule_id: str
    :ivar justification: Justification for the role assignment.
    :vartype justification: str
    :ivar ticket_info: Ticket Info of the role assignment.
    :vartype ticket_info:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleRequestPropertiesTicketInfo
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: DateTime when role assignment schedule request was created.
    :vartype created_on: ~datetime.datetime
    :ivar requestor_id: Id of the user who created this request.
    :vartype requestor_id: str
    :ivar expanded_properties: Additional properties of principal, scope and role definition.
    :vartype expanded_properties:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "scope": {"readonly": True},
        "principal_type": {"readonly": True},
        "status": {"readonly": True},
        "approval_id": {"readonly": True},
        "created_on": {"readonly": True},
        "requestor_id": {"readonly": True},
        "expanded_properties": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "request_type": {"key": "properties.requestType", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "approval_id": {"key": "properties.approvalId", "type": "str"},
        "target_role_assignment_schedule_id": {"key": "properties.targetRoleAssignmentScheduleId", "type": "str"},
        "target_role_assignment_schedule_instance_id": {
            "key": "properties.targetRoleAssignmentScheduleInstanceId",
            "type": "str",
        },
        "schedule_info": {
            "key": "properties.scheduleInfo",
            "type": "RoleAssignmentScheduleRequestPropertiesScheduleInfo",
        },
        "linked_role_eligibility_schedule_id": {"key": "properties.linkedRoleEligibilityScheduleId", "type": "str"},
        "justification": {"key": "properties.justification", "type": "str"},
        "ticket_info": {"key": "properties.ticketInfo", "type": "RoleAssignmentScheduleRequestPropertiesTicketInfo"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "requestor_id": {"key": "properties.requestorId", "type": "str"},
        "expanded_properties": {"key": "properties.expandedProperties", "type": "ExpandedProperties"},
    }

    def __init__(
        self,
        *,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        request_type: Optional[Union[str, "_models.RequestType"]] = None,
        target_role_assignment_schedule_id: Optional[str] = None,
        target_role_assignment_schedule_instance_id: Optional[str] = None,
        schedule_info: Optional["_models.RoleAssignmentScheduleRequestPropertiesScheduleInfo"] = None,
        linked_role_eligibility_schedule_id: Optional[str] = None,
        justification: Optional[str] = None,
        ticket_info: Optional["_models.RoleAssignmentScheduleRequestPropertiesTicketInfo"] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword request_type: The type of the role assignment schedule request. Eg: SelfActivate,
         AdminAssign etc. Known values are: "AdminAssign", "AdminRemove", "AdminUpdate", "AdminExtend",
         "AdminRenew", "SelfActivate", "SelfDeactivate", "SelfExtend", and "SelfRenew".
        :paramtype request_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RequestType
        :keyword target_role_assignment_schedule_id: The resultant role assignment schedule id or the
         role assignment schedule id being updated.
        :paramtype target_role_assignment_schedule_id: str
        :keyword target_role_assignment_schedule_instance_id: The role assignment schedule instance id
         being updated.
        :paramtype target_role_assignment_schedule_instance_id: str
        :keyword schedule_info: Schedule info of the role assignment schedule.
        :paramtype schedule_info:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleRequestPropertiesScheduleInfo
        :keyword linked_role_eligibility_schedule_id: The linked role eligibility schedule id - to
         activate an eligibility.
        :paramtype linked_role_eligibility_schedule_id: str
        :keyword justification: Justification for the role assignment.
        :paramtype justification: str
        :keyword ticket_info: Ticket Info of the role assignment.
        :paramtype ticket_info:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleRequestPropertiesTicketInfo
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = None
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = None
        self.request_type = request_type
        self.status = None
        self.approval_id = None
        self.target_role_assignment_schedule_id = target_role_assignment_schedule_id
        self.target_role_assignment_schedule_instance_id = target_role_assignment_schedule_instance_id
        self.schedule_info = schedule_info
        self.linked_role_eligibility_schedule_id = linked_role_eligibility_schedule_id
        self.justification = justification
        self.ticket_info = ticket_info
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = None
        self.requestor_id = None
        self.expanded_properties = None


class RoleAssignmentScheduleRequestFilter(_serialization.Model):
    """Role assignment schedule request filter.

    :ivar principal_id: Returns role assignment requests of the specific principal.
    :vartype principal_id: str
    :ivar role_definition_id: Returns role assignment requests of the specific role definition.
    :vartype role_definition_id: str
    :ivar requestor_id: Returns role assignment requests created by specific principal.
    :vartype requestor_id: str
    :ivar status: Returns role assignment requests of specific status.
    :vartype status: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "role_definition_id": {"key": "roleDefinitionId", "type": "str"},
        "requestor_id": {"key": "requestorId", "type": "str"},
        "status": {"key": "status", "type": "str"},
    }

    def __init__(
        self,
        *,
        principal_id: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        requestor_id: Optional[str] = None,
        status: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword principal_id: Returns role assignment requests of the specific principal.
        :paramtype principal_id: str
        :keyword role_definition_id: Returns role assignment requests of the specific role definition.
        :paramtype role_definition_id: str
        :keyword requestor_id: Returns role assignment requests created by specific principal.
        :paramtype requestor_id: str
        :keyword status: Returns role assignment requests of specific status.
        :paramtype status: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.role_definition_id = role_definition_id
        self.requestor_id = requestor_id
        self.status = status


class RoleAssignmentScheduleRequestListResult(_serialization.Model):
    """Role assignment schedule request list operation result.

    :ivar value: Role assignment schedule request list.
    :vartype value:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleRequest]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleAssignmentScheduleRequest]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleAssignmentScheduleRequest"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Role assignment schedule request list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleRequest]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleAssignmentScheduleRequestPropertiesScheduleInfo(_serialization.Model):
    """Schedule info of the role assignment schedule.

    :ivar start_date_time: Start DateTime of the role assignment schedule.
    :vartype start_date_time: ~datetime.datetime
    :ivar expiration: Expiration of the role assignment schedule.
    :vartype expiration:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration
    """

    _attribute_map = {
        "start_date_time": {"key": "startDateTime", "type": "iso-8601"},
        "expiration": {"key": "expiration", "type": "RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration"},
    }

    def __init__(
        self,
        *,
        start_date_time: Optional[datetime.datetime] = None,
        expiration: Optional["_models.RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword start_date_time: Start DateTime of the role assignment schedule.
        :paramtype start_date_time: ~datetime.datetime
        :keyword expiration: Expiration of the role assignment schedule.
        :paramtype expiration:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration
        """
        super().__init__(**kwargs)
        self.start_date_time = start_date_time
        self.expiration = expiration


class RoleAssignmentScheduleRequestPropertiesScheduleInfoExpiration(_serialization.Model):
    """Expiration of the role assignment schedule.

    :ivar type: Type of the role assignment schedule expiration. Known values are: "AfterDuration",
     "AfterDateTime", and "NoExpiration".
    :vartype type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Type
    :ivar end_date_time: End DateTime of the role assignment schedule.
    :vartype end_date_time: ~datetime.datetime
    :ivar duration: Duration of the role assignment schedule in TimeSpan.
    :vartype duration: str
    """

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "end_date_time": {"key": "endDateTime", "type": "iso-8601"},
        "duration": {"key": "duration", "type": "str"},
    }

    def __init__(
        self,
        *,
        type: Optional[Union[str, "_models.Type"]] = None,
        end_date_time: Optional[datetime.datetime] = None,
        duration: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: Type of the role assignment schedule expiration. Known values are:
         "AfterDuration", "AfterDateTime", and "NoExpiration".
        :paramtype type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Type
        :keyword end_date_time: End DateTime of the role assignment schedule.
        :paramtype end_date_time: ~datetime.datetime
        :keyword duration: Duration of the role assignment schedule in TimeSpan.
        :paramtype duration: str
        """
        super().__init__(**kwargs)
        self.type = type
        self.end_date_time = end_date_time
        self.duration = duration


class RoleAssignmentScheduleRequestPropertiesTicketInfo(_serialization.Model):
    """Ticket Info of the role assignment.

    :ivar ticket_number: Ticket number for the role assignment.
    :vartype ticket_number: str
    :ivar ticket_system: Ticket system name for the role assignment.
    :vartype ticket_system: str
    """

    _attribute_map = {
        "ticket_number": {"key": "ticketNumber", "type": "str"},
        "ticket_system": {"key": "ticketSystem", "type": "str"},
    }

    def __init__(
        self, *, ticket_number: Optional[str] = None, ticket_system: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword ticket_number: Ticket number for the role assignment.
        :paramtype ticket_number: str
        :keyword ticket_system: Ticket system name for the role assignment.
        :paramtype ticket_system: str
        """
        super().__init__(**kwargs)
        self.ticket_number = ticket_number
        self.ticket_system = ticket_system


class RoleEligibilitySchedule(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role eligibility schedule.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role eligibility schedule Id.
    :vartype id: str
    :ivar name: The role eligibility schedule name.
    :vartype name: str
    :ivar type: The role eligibility schedule type.
    :vartype type: str
    :ivar scope: The role eligibility schedule scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
    :ivar role_eligibility_schedule_request_id: The id of roleEligibilityScheduleRequest used to
     create this roleAssignmentSchedule.
    :vartype role_eligibility_schedule_request_id: str
    :ivar member_type: Membership type of the role eligibility schedule. Known values are:
     "Inherited", "Direct", and "Group".
    :vartype member_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.MemberType
    :ivar status: The status of the role eligibility schedule. Known values are: "Accepted",
     "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
     "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
     "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
     "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
     "ScheduleCreated", and "PendingExternalProvisioning".
    :vartype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
    :ivar start_date_time: Start DateTime when role eligibility schedule.
    :vartype start_date_time: ~datetime.datetime
    :ivar end_date_time: End DateTime when role eligibility schedule.
    :vartype end_date_time: ~datetime.datetime
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: DateTime when role eligibility schedule was created.
    :vartype created_on: ~datetime.datetime
    :ivar updated_on: DateTime when role eligibility schedule was modified.
    :vartype updated_on: ~datetime.datetime
    :ivar expanded_properties: Additional properties of principal, scope and role definition.
    :vartype expanded_properties:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "role_eligibility_schedule_request_id": {"key": "properties.roleEligibilityScheduleRequestId", "type": "str"},
        "member_type": {"key": "properties.memberType", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "start_date_time": {"key": "properties.startDateTime", "type": "iso-8601"},
        "end_date_time": {"key": "properties.endDateTime", "type": "iso-8601"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "updated_on": {"key": "properties.updatedOn", "type": "iso-8601"},
        "expanded_properties": {"key": "properties.expandedProperties", "type": "ExpandedProperties"},
    }

    def __init__(
        self,
        *,
        scope: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        principal_type: Optional[Union[str, "_models.PrincipalType"]] = None,
        role_eligibility_schedule_request_id: Optional[str] = None,
        member_type: Optional[Union[str, "_models.MemberType"]] = None,
        status: Optional[Union[str, "_models.Status"]] = None,
        start_date_time: Optional[datetime.datetime] = None,
        end_date_time: Optional[datetime.datetime] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        created_on: Optional[datetime.datetime] = None,
        updated_on: Optional[datetime.datetime] = None,
        expanded_properties: Optional["_models.ExpandedProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: The role eligibility schedule scope.
        :paramtype scope: str
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
        :paramtype principal_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
        :keyword role_eligibility_schedule_request_id: The id of roleEligibilityScheduleRequest used to
         create this roleAssignmentSchedule.
        :paramtype role_eligibility_schedule_request_id: str
        :keyword member_type: Membership type of the role eligibility schedule. Known values are:
         "Inherited", "Direct", and "Group".
        :paramtype member_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.MemberType
        :keyword status: The status of the role eligibility schedule. Known values are: "Accepted",
         "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
         "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
         "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
         "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
         "ScheduleCreated", and "PendingExternalProvisioning".
        :paramtype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
        :keyword start_date_time: Start DateTime when role eligibility schedule.
        :paramtype start_date_time: ~datetime.datetime
        :keyword end_date_time: End DateTime when role eligibility schedule.
        :paramtype end_date_time: ~datetime.datetime
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        :keyword created_on: DateTime when role eligibility schedule was created.
        :paramtype created_on: ~datetime.datetime
        :keyword updated_on: DateTime when role eligibility schedule was modified.
        :paramtype updated_on: ~datetime.datetime
        :keyword expanded_properties: Additional properties of principal, scope and role definition.
        :paramtype expanded_properties:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = scope
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.role_eligibility_schedule_request_id = role_eligibility_schedule_request_id
        self.member_type = member_type
        self.status = status
        self.start_date_time = start_date_time
        self.end_date_time = end_date_time
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = created_on
        self.updated_on = updated_on
        self.expanded_properties = expanded_properties


class RoleEligibilityScheduleFilter(_serialization.Model):
    """Role eligibility schedule filter.

    :ivar principal_id: Returns role eligibility schedule of the specific principal.
    :vartype principal_id: str
    :ivar role_definition_id: Returns role eligibility schedule of the specific role definition.
    :vartype role_definition_id: str
    :ivar status: Returns role eligibility schedule of the specific status.
    :vartype status: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "role_definition_id": {"key": "roleDefinitionId", "type": "str"},
        "status": {"key": "status", "type": "str"},
    }

    def __init__(
        self,
        *,
        principal_id: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        status: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword principal_id: Returns role eligibility schedule of the specific principal.
        :paramtype principal_id: str
        :keyword role_definition_id: Returns role eligibility schedule of the specific role definition.
        :paramtype role_definition_id: str
        :keyword status: Returns role eligibility schedule of the specific status.
        :paramtype status: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.role_definition_id = role_definition_id
        self.status = status


class RoleEligibilityScheduleInstance(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Information about current or upcoming role eligibility schedule instance.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role eligibility schedule instance ID.
    :vartype id: str
    :ivar name: The role eligibility schedule instance name.
    :vartype name: str
    :ivar type: The role eligibility schedule instance type.
    :vartype type: str
    :ivar scope: The role eligibility schedule scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
    :ivar role_eligibility_schedule_id: Id of the master role eligibility schedule.
    :vartype role_eligibility_schedule_id: str
    :ivar status: The status of the role eligibility schedule instance. Known values are:
     "Accepted", "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
     "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
     "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
     "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
     "ScheduleCreated", and "PendingExternalProvisioning".
    :vartype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
    :ivar start_date_time: The startDateTime of the role eligibility schedule instance.
    :vartype start_date_time: ~datetime.datetime
    :ivar end_date_time: The endDateTime of the role eligibility schedule instance.
    :vartype end_date_time: ~datetime.datetime
    :ivar member_type: Membership type of the role eligibility schedule. Known values are:
     "Inherited", "Direct", and "Group".
    :vartype member_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.MemberType
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: DateTime when role eligibility schedule was created.
    :vartype created_on: ~datetime.datetime
    :ivar expanded_properties: Additional properties of principal, scope and role definition.
    :vartype expanded_properties:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "role_eligibility_schedule_id": {"key": "properties.roleEligibilityScheduleId", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "start_date_time": {"key": "properties.startDateTime", "type": "iso-8601"},
        "end_date_time": {"key": "properties.endDateTime", "type": "iso-8601"},
        "member_type": {"key": "properties.memberType", "type": "str"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "expanded_properties": {"key": "properties.expandedProperties", "type": "ExpandedProperties"},
    }

    def __init__(
        self,
        *,
        scope: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        principal_type: Optional[Union[str, "_models.PrincipalType"]] = None,
        role_eligibility_schedule_id: Optional[str] = None,
        status: Optional[Union[str, "_models.Status"]] = None,
        start_date_time: Optional[datetime.datetime] = None,
        end_date_time: Optional[datetime.datetime] = None,
        member_type: Optional[Union[str, "_models.MemberType"]] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        created_on: Optional[datetime.datetime] = None,
        expanded_properties: Optional["_models.ExpandedProperties"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: The role eligibility schedule scope.
        :paramtype scope: str
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
        :paramtype principal_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
        :keyword role_eligibility_schedule_id: Id of the master role eligibility schedule.
        :paramtype role_eligibility_schedule_id: str
        :keyword status: The status of the role eligibility schedule instance. Known values are:
         "Accepted", "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
         "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
         "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
         "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
         "ScheduleCreated", and "PendingExternalProvisioning".
        :paramtype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
        :keyword start_date_time: The startDateTime of the role eligibility schedule instance.
        :paramtype start_date_time: ~datetime.datetime
        :keyword end_date_time: The endDateTime of the role eligibility schedule instance.
        :paramtype end_date_time: ~datetime.datetime
        :keyword member_type: Membership type of the role eligibility schedule. Known values are:
         "Inherited", "Direct", and "Group".
        :paramtype member_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.MemberType
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        :keyword created_on: DateTime when role eligibility schedule was created.
        :paramtype created_on: ~datetime.datetime
        :keyword expanded_properties: Additional properties of principal, scope and role definition.
        :paramtype expanded_properties:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = scope
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.role_eligibility_schedule_id = role_eligibility_schedule_id
        self.status = status
        self.start_date_time = start_date_time
        self.end_date_time = end_date_time
        self.member_type = member_type
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = created_on
        self.expanded_properties = expanded_properties


class RoleEligibilityScheduleInstanceFilter(_serialization.Model):
    """Role eligibility schedule instance filter.

    :ivar principal_id: Returns role eligibility schedule instances of the specific principal.
    :vartype principal_id: str
    :ivar role_definition_id: Returns role eligibility schedule instances of the specific role
     definition.
    :vartype role_definition_id: str
    :ivar status: Returns role eligibility schedule instances of the specific status.
    :vartype status: str
    :ivar role_eligibility_schedule_id: Returns role eligibility schedule instances belonging to a
     specific role eligibility schedule.
    :vartype role_eligibility_schedule_id: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "role_definition_id": {"key": "roleDefinitionId", "type": "str"},
        "status": {"key": "status", "type": "str"},
        "role_eligibility_schedule_id": {"key": "roleEligibilityScheduleId", "type": "str"},
    }

    def __init__(
        self,
        *,
        principal_id: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        status: Optional[str] = None,
        role_eligibility_schedule_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword principal_id: Returns role eligibility schedule instances of the specific principal.
        :paramtype principal_id: str
        :keyword role_definition_id: Returns role eligibility schedule instances of the specific role
         definition.
        :paramtype role_definition_id: str
        :keyword status: Returns role eligibility schedule instances of the specific status.
        :paramtype status: str
        :keyword role_eligibility_schedule_id: Returns role eligibility schedule instances belonging to
         a specific role eligibility schedule.
        :paramtype role_eligibility_schedule_id: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.role_definition_id = role_definition_id
        self.status = status
        self.role_eligibility_schedule_id = role_eligibility_schedule_id


class RoleEligibilityScheduleInstanceListResult(_serialization.Model):
    """Role eligibility schedule instance list operation result.

    :ivar value: Role eligibility schedule instance list.
    :vartype value:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleInstance]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleEligibilityScheduleInstance]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleEligibilityScheduleInstance"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Role eligibility schedule instance list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleInstance]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleEligibilityScheduleListResult(_serialization.Model):
    """role eligibility schedule list operation result.

    :ivar value: role eligibility schedule list.
    :vartype value:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilitySchedule]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleEligibilitySchedule]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleEligibilitySchedule"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: role eligibility schedule list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilitySchedule]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleEligibilityScheduleRequest(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role Eligibility schedule request.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role eligibility schedule request ID.
    :vartype id: str
    :ivar name: The role eligibility schedule request name.
    :vartype name: str
    :ivar type: The role eligibility schedule request type.
    :vartype type: str
    :ivar scope: The role eligibility schedule request scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PrincipalType
    :ivar request_type: The type of the role assignment schedule request. Eg: SelfActivate,
     AdminAssign etc. Known values are: "AdminAssign", "AdminRemove", "AdminUpdate", "AdminExtend",
     "AdminRenew", "SelfActivate", "SelfDeactivate", "SelfExtend", and "SelfRenew".
    :vartype request_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.RequestType
    :ivar status: The status of the role eligibility schedule request. Known values are:
     "Accepted", "PendingEvaluation", "Granted", "Denied", "PendingProvisioning", "Provisioned",
     "PendingRevocation", "Revoked", "Canceled", "Failed", "PendingApprovalProvisioning",
     "PendingApproval", "FailedAsResourceIsLocked", "PendingAdminDecision", "AdminApproved",
     "AdminDenied", "TimedOut", "ProvisioningStarted", "Invalid", "PendingScheduleCreation",
     "ScheduleCreated", and "PendingExternalProvisioning".
    :vartype status: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Status
    :ivar approval_id: The approvalId of the role eligibility schedule request.
    :vartype approval_id: str
    :ivar schedule_info: Schedule info of the role eligibility schedule.
    :vartype schedule_info:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleRequestPropertiesScheduleInfo
    :ivar target_role_eligibility_schedule_id: The resultant role eligibility schedule id or the
     role eligibility schedule id being updated.
    :vartype target_role_eligibility_schedule_id: str
    :ivar target_role_eligibility_schedule_instance_id: The role eligibility schedule instance id
     being updated.
    :vartype target_role_eligibility_schedule_instance_id: str
    :ivar justification: Justification for the role eligibility.
    :vartype justification: str
    :ivar ticket_info: Ticket Info of the role eligibility.
    :vartype ticket_info:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleRequestPropertiesTicketInfo
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: DateTime when role eligibility schedule request was created.
    :vartype created_on: ~datetime.datetime
    :ivar requestor_id: Id of the user who created this request.
    :vartype requestor_id: str
    :ivar expanded_properties: Additional properties of principal, scope and role definition.
    :vartype expanded_properties:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ExpandedProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "scope": {"readonly": True},
        "principal_type": {"readonly": True},
        "status": {"readonly": True},
        "approval_id": {"readonly": True},
        "created_on": {"readonly": True},
        "requestor_id": {"readonly": True},
        "expanded_properties": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "request_type": {"key": "properties.requestType", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "approval_id": {"key": "properties.approvalId", "type": "str"},
        "schedule_info": {
            "key": "properties.scheduleInfo",
            "type": "RoleEligibilityScheduleRequestPropertiesScheduleInfo",
        },
        "target_role_eligibility_schedule_id": {"key": "properties.targetRoleEligibilityScheduleId", "type": "str"},
        "target_role_eligibility_schedule_instance_id": {
            "key": "properties.targetRoleEligibilityScheduleInstanceId",
            "type": "str",
        },
        "justification": {"key": "properties.justification", "type": "str"},
        "ticket_info": {"key": "properties.ticketInfo", "type": "RoleEligibilityScheduleRequestPropertiesTicketInfo"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "requestor_id": {"key": "properties.requestorId", "type": "str"},
        "expanded_properties": {"key": "properties.expandedProperties", "type": "ExpandedProperties"},
    }

    def __init__(
        self,
        *,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        request_type: Optional[Union[str, "_models.RequestType"]] = None,
        schedule_info: Optional["_models.RoleEligibilityScheduleRequestPropertiesScheduleInfo"] = None,
        target_role_eligibility_schedule_id: Optional[str] = None,
        target_role_eligibility_schedule_instance_id: Optional[str] = None,
        justification: Optional[str] = None,
        ticket_info: Optional["_models.RoleEligibilityScheduleRequestPropertiesTicketInfo"] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword request_type: The type of the role assignment schedule request. Eg: SelfActivate,
         AdminAssign etc. Known values are: "AdminAssign", "AdminRemove", "AdminUpdate", "AdminExtend",
         "AdminRenew", "SelfActivate", "SelfDeactivate", "SelfExtend", and "SelfRenew".
        :paramtype request_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RequestType
        :keyword schedule_info: Schedule info of the role eligibility schedule.
        :paramtype schedule_info:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleRequestPropertiesScheduleInfo
        :keyword target_role_eligibility_schedule_id: The resultant role eligibility schedule id or the
         role eligibility schedule id being updated.
        :paramtype target_role_eligibility_schedule_id: str
        :keyword target_role_eligibility_schedule_instance_id: The role eligibility schedule instance
         id being updated.
        :paramtype target_role_eligibility_schedule_instance_id: str
        :keyword justification: Justification for the role eligibility.
        :paramtype justification: str
        :keyword ticket_info: Ticket Info of the role eligibility.
        :paramtype ticket_info:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleRequestPropertiesTicketInfo
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently accepted value is '2.0'.
        :paramtype condition_version: str
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = None
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = None
        self.request_type = request_type
        self.status = None
        self.approval_id = None
        self.schedule_info = schedule_info
        self.target_role_eligibility_schedule_id = target_role_eligibility_schedule_id
        self.target_role_eligibility_schedule_instance_id = target_role_eligibility_schedule_instance_id
        self.justification = justification
        self.ticket_info = ticket_info
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = None
        self.requestor_id = None
        self.expanded_properties = None


class RoleEligibilityScheduleRequestFilter(_serialization.Model):
    """Role eligibility schedule request filter.

    :ivar principal_id: Returns role eligibility requests of the specific principal.
    :vartype principal_id: str
    :ivar role_definition_id: Returns role eligibility requests of the specific role definition.
    :vartype role_definition_id: str
    :ivar requestor_id: Returns role eligibility requests created by specific principal.
    :vartype requestor_id: str
    :ivar status: Returns role eligibility requests of specific status.
    :vartype status: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "role_definition_id": {"key": "roleDefinitionId", "type": "str"},
        "requestor_id": {"key": "requestorId", "type": "str"},
        "status": {"key": "status", "type": "str"},
    }

    def __init__(
        self,
        *,
        principal_id: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        requestor_id: Optional[str] = None,
        status: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword principal_id: Returns role eligibility requests of the specific principal.
        :paramtype principal_id: str
        :keyword role_definition_id: Returns role eligibility requests of the specific role definition.
        :paramtype role_definition_id: str
        :keyword requestor_id: Returns role eligibility requests created by specific principal.
        :paramtype requestor_id: str
        :keyword status: Returns role eligibility requests of specific status.
        :paramtype status: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.role_definition_id = role_definition_id
        self.requestor_id = requestor_id
        self.status = status


class RoleEligibilityScheduleRequestListResult(_serialization.Model):
    """Role eligibility schedule request list operation result.

    :ivar value: Role eligibility schedule request list.
    :vartype value:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleRequest]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleEligibilityScheduleRequest]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleEligibilityScheduleRequest"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Role eligibility schedule request list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleRequest]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleEligibilityScheduleRequestPropertiesScheduleInfo(_serialization.Model):
    """Schedule info of the role eligibility schedule.

    :ivar start_date_time: Start DateTime of the role eligibility schedule.
    :vartype start_date_time: ~datetime.datetime
    :ivar expiration: Expiration of the role eligibility schedule.
    :vartype expiration:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration
    """

    _attribute_map = {
        "start_date_time": {"key": "startDateTime", "type": "iso-8601"},
        "expiration": {"key": "expiration", "type": "RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration"},
    }

    def __init__(
        self,
        *,
        start_date_time: Optional[datetime.datetime] = None,
        expiration: Optional["_models.RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword start_date_time: Start DateTime of the role eligibility schedule.
        :paramtype start_date_time: ~datetime.datetime
        :keyword expiration: Expiration of the role eligibility schedule.
        :paramtype expiration:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration
        """
        super().__init__(**kwargs)
        self.start_date_time = start_date_time
        self.expiration = expiration


class RoleEligibilityScheduleRequestPropertiesScheduleInfoExpiration(_serialization.Model):
    """Expiration of the role eligibility schedule.

    :ivar type: Type of the role eligibility schedule expiration. Known values are:
     "AfterDuration", "AfterDateTime", and "NoExpiration".
    :vartype type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Type
    :ivar end_date_time: End DateTime of the role eligibility schedule.
    :vartype end_date_time: ~datetime.datetime
    :ivar duration: Duration of the role eligibility schedule in TimeSpan.
    :vartype duration: str
    """

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "end_date_time": {"key": "endDateTime", "type": "iso-8601"},
        "duration": {"key": "duration", "type": "str"},
    }

    def __init__(
        self,
        *,
        type: Optional[Union[str, "_models.Type"]] = None,
        end_date_time: Optional[datetime.datetime] = None,
        duration: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: Type of the role eligibility schedule expiration. Known values are:
         "AfterDuration", "AfterDateTime", and "NoExpiration".
        :paramtype type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.Type
        :keyword end_date_time: End DateTime of the role eligibility schedule.
        :paramtype end_date_time: ~datetime.datetime
        :keyword duration: Duration of the role eligibility schedule in TimeSpan.
        :paramtype duration: str
        """
        super().__init__(**kwargs)
        self.type = type
        self.end_date_time = end_date_time
        self.duration = duration


class RoleEligibilityScheduleRequestPropertiesTicketInfo(_serialization.Model):
    """Ticket Info of the role eligibility.

    :ivar ticket_number: Ticket number for the role eligibility.
    :vartype ticket_number: str
    :ivar ticket_system: Ticket system name for the role eligibility.
    :vartype ticket_system: str
    """

    _attribute_map = {
        "ticket_number": {"key": "ticketNumber", "type": "str"},
        "ticket_system": {"key": "ticketSystem", "type": "str"},
    }

    def __init__(
        self, *, ticket_number: Optional[str] = None, ticket_system: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword ticket_number: Ticket number for the role eligibility.
        :paramtype ticket_number: str
        :keyword ticket_system: Ticket system name for the role eligibility.
        :paramtype ticket_system: str
        """
        super().__init__(**kwargs)
        self.ticket_number = ticket_number
        self.ticket_system = ticket_system


class RoleManagementPolicy(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role management policy.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role management policy Id.
    :vartype id: str
    :ivar name: The role management policy name.
    :vartype name: str
    :ivar type: The role management policy type.
    :vartype type: str
    :ivar scope: The role management policy scope.
    :vartype scope: str
    :ivar display_name: The role management policy display name.
    :vartype display_name: str
    :ivar description: The role management policy description.
    :vartype description: str
    :ivar is_organization_default: The role management policy is default policy.
    :vartype is_organization_default: bool
    :ivar last_modified_by: The name of the entity last modified it.
    :vartype last_modified_by: ~azure.mgmt.authorization.v2020_10_01_preview.models.Principal
    :ivar last_modified_date_time: The last modified date time.
    :vartype last_modified_date_time: ~datetime.datetime
    :ivar rules: The rule applied to the policy.
    :vartype rules:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRule]
    :ivar effective_rules: The readonly computed rule applied to the policy.
    :vartype effective_rules:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRule]
    :ivar policy_properties: Additional properties of scope.
    :vartype policy_properties:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PolicyProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "last_modified_by": {"readonly": True},
        "last_modified_date_time": {"readonly": True},
        "effective_rules": {"readonly": True},
        "policy_properties": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "display_name": {"key": "properties.displayName", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "is_organization_default": {"key": "properties.isOrganizationDefault", "type": "bool"},
        "last_modified_by": {"key": "properties.lastModifiedBy", "type": "Principal"},
        "last_modified_date_time": {"key": "properties.lastModifiedDateTime", "type": "iso-8601"},
        "rules": {"key": "properties.rules", "type": "[RoleManagementPolicyRule]"},
        "effective_rules": {"key": "properties.effectiveRules", "type": "[RoleManagementPolicyRule]"},
        "policy_properties": {"key": "properties.policyProperties", "type": "PolicyProperties"},
    }

    def __init__(
        self,
        *,
        scope: Optional[str] = None,
        display_name: Optional[str] = None,
        description: Optional[str] = None,
        is_organization_default: Optional[bool] = None,
        rules: Optional[List["_models.RoleManagementPolicyRule"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: The role management policy scope.
        :paramtype scope: str
        :keyword display_name: The role management policy display name.
        :paramtype display_name: str
        :keyword description: The role management policy description.
        :paramtype description: str
        :keyword is_organization_default: The role management policy is default policy.
        :paramtype is_organization_default: bool
        :keyword rules: The rule applied to the policy.
        :paramtype rules:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRule]
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = scope
        self.display_name = display_name
        self.description = description
        self.is_organization_default = is_organization_default
        self.last_modified_by = None
        self.last_modified_date_time = None
        self.rules = rules
        self.effective_rules = None
        self.policy_properties = None


class RoleManagementPolicyRule(_serialization.Model):
    """The role management policy rule.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RoleManagementPolicyApprovalRule, RoleManagementPolicyAuthenticationContextRule,
    RoleManagementPolicyEnablementRule, RoleManagementPolicyExpirationRule,
    RoleManagementPolicyNotificationRule

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
    }

    _subtype_map = {
        "rule_type": {
            "RoleManagementPolicyApprovalRule": "RoleManagementPolicyApprovalRule",
            "RoleManagementPolicyAuthenticationContextRule": "RoleManagementPolicyAuthenticationContextRule",
            "RoleManagementPolicyEnablementRule": "RoleManagementPolicyEnablementRule",
            "RoleManagementPolicyExpirationRule": "RoleManagementPolicyExpirationRule",
            "RoleManagementPolicyNotificationRule": "RoleManagementPolicyNotificationRule",
        }
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
        """
        super().__init__(**kwargs)
        self.id = id
        self.rule_type: Optional[str] = None
        self.target = target


class RoleManagementPolicyApprovalRule(RoleManagementPolicyRule):
    """The role management policy approval rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar setting: The approval setting.
    :vartype setting: ~azure.mgmt.authorization.v2020_10_01_preview.models.ApprovalSettings
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "setting": {"key": "setting", "type": "ApprovalSettings"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        setting: Optional["_models.ApprovalSettings"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword setting: The approval setting.
        :paramtype setting: ~azure.mgmt.authorization.v2020_10_01_preview.models.ApprovalSettings
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyApprovalRule"
        self.setting = setting


class RoleManagementPolicyAssignment(_serialization.Model):
    """Role management policy.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role management policy Id.
    :vartype id: str
    :ivar name: The role management policy name.
    :vartype name: str
    :ivar type: The role management policy type.
    :vartype type: str
    :ivar scope: The role management policy scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition of management policy assignment.
    :vartype role_definition_id: str
    :ivar policy_id: The policy id role management policy assignment.
    :vartype policy_id: str
    :ivar policy_assignment_properties: Additional properties of scope, role definition and policy.
    :vartype policy_assignment_properties:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.PolicyAssignmentProperties
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "policy_assignment_properties": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "policy_id": {"key": "properties.policyId", "type": "str"},
        "policy_assignment_properties": {
            "key": "properties.policyAssignmentProperties",
            "type": "PolicyAssignmentProperties",
        },
    }

    def __init__(
        self,
        *,
        scope: Optional[str] = None,
        role_definition_id: Optional[str] = None,
        policy_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword scope: The role management policy scope.
        :paramtype scope: str
        :keyword role_definition_id: The role definition of management policy assignment.
        :paramtype role_definition_id: str
        :keyword policy_id: The policy id role management policy assignment.
        :paramtype policy_id: str
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = scope
        self.role_definition_id = role_definition_id
        self.policy_id = policy_id
        self.policy_assignment_properties = None


class RoleManagementPolicyAssignmentListResult(_serialization.Model):
    """Role management policy assignment list operation result.

    :ivar value: Role management policy assignment list.
    :vartype value:
     list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyAssignment]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleManagementPolicyAssignment]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleManagementPolicyAssignment"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Role management policy assignment list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyAssignment]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleManagementPolicyAuthenticationContextRule(RoleManagementPolicyRule):
    """The role management policy authentication context rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar is_enabled: The value indicating if rule is enabled.
    :vartype is_enabled: bool
    :ivar claim_value: The claim value.
    :vartype claim_value: str
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "is_enabled": {"key": "isEnabled", "type": "bool"},
        "claim_value": {"key": "claimValue", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        is_enabled: Optional[bool] = None,
        claim_value: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword is_enabled: The value indicating if rule is enabled.
        :paramtype is_enabled: bool
        :keyword claim_value: The claim value.
        :paramtype claim_value: str
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyAuthenticationContextRule"
        self.is_enabled = is_enabled
        self.claim_value = claim_value


class RoleManagementPolicyEnablementRule(RoleManagementPolicyRule):
    """The role management policy rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar enabled_rules: The list of enabled rules.
    :vartype enabled_rules: list[str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.EnablementRules]
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "enabled_rules": {"key": "enabledRules", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        enabled_rules: Optional[List[Union[str, "_models.EnablementRules"]]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword enabled_rules: The list of enabled rules.
        :paramtype enabled_rules: list[str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.EnablementRules]
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyEnablementRule"
        self.enabled_rules = enabled_rules


class RoleManagementPolicyExpirationRule(RoleManagementPolicyRule):
    """The role management policy expiration rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar is_expiration_required: The value indicating whether expiration is required.
    :vartype is_expiration_required: bool
    :ivar maximum_duration: The maximum duration of expiration in timespan.
    :vartype maximum_duration: str
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "is_expiration_required": {"key": "isExpirationRequired", "type": "bool"},
        "maximum_duration": {"key": "maximumDuration", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        is_expiration_required: Optional[bool] = None,
        maximum_duration: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword is_expiration_required: The value indicating whether expiration is required.
        :paramtype is_expiration_required: bool
        :keyword maximum_duration: The maximum duration of expiration in timespan.
        :paramtype maximum_duration: str
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyExpirationRule"
        self.is_expiration_required = is_expiration_required
        self.maximum_duration = maximum_duration


class RoleManagementPolicyListResult(_serialization.Model):
    """Role management policy list operation result.

    :ivar value: Role management policy list.
    :vartype value: list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicy]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleManagementPolicy]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.RoleManagementPolicy"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Role management policy list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicy]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleManagementPolicyNotificationRule(RoleManagementPolicyRule):
    """The role management policy notification rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
    :ivar notification_type: The type of notification. "Email"
    :vartype notification_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.NotificationDeliveryMechanism
    :ivar notification_level: The notification level. Known values are: "None", "Critical", and
     "All".
    :vartype notification_level: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.NotificationLevel
    :ivar recipient_type: The recipient type. Known values are: "Requestor", "Approver", and
     "Admin".
    :vartype recipient_type: str or
     ~azure.mgmt.authorization.v2020_10_01_preview.models.RecipientType
    :ivar notification_recipients: The list of notification recipients.
    :vartype notification_recipients: list[str]
    :ivar is_default_recipients_enabled: Determines if the notification will be sent to the
     recipient type specified in the policy rule.
    :vartype is_default_recipients_enabled: bool
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "notification_type": {"key": "notificationType", "type": "str"},
        "notification_level": {"key": "notificationLevel", "type": "str"},
        "recipient_type": {"key": "recipientType", "type": "str"},
        "notification_recipients": {"key": "notificationRecipients", "type": "[str]"},
        "is_default_recipients_enabled": {"key": "isDefaultRecipientsEnabled", "type": "bool"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        notification_type: Optional[Union[str, "_models.NotificationDeliveryMechanism"]] = None,
        notification_level: Optional[Union[str, "_models.NotificationLevel"]] = None,
        recipient_type: Optional[Union[str, "_models.RecipientType"]] = None,
        notification_recipients: Optional[List[str]] = None,
        is_default_recipients_enabled: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleManagementPolicyRuleTarget
        :keyword notification_type: The type of notification. "Email"
        :paramtype notification_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.NotificationDeliveryMechanism
        :keyword notification_level: The notification level. Known values are: "None", "Critical", and
         "All".
        :paramtype notification_level: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.NotificationLevel
        :keyword recipient_type: The recipient type. Known values are: "Requestor", "Approver", and
         "Admin".
        :paramtype recipient_type: str or
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RecipientType
        :keyword notification_recipients: The list of notification recipients.
        :paramtype notification_recipients: list[str]
        :keyword is_default_recipients_enabled: Determines if the notification will be sent to the
         recipient type specified in the policy rule.
        :paramtype is_default_recipients_enabled: bool
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyNotificationRule"
        self.notification_type = notification_type
        self.notification_level = notification_level
        self.recipient_type = recipient_type
        self.notification_recipients = notification_recipients
        self.is_default_recipients_enabled = is_default_recipients_enabled


class RoleManagementPolicyRuleTarget(_serialization.Model):
    """The role management policy rule target.

    :ivar caller: The caller of the setting.
    :vartype caller: str
    :ivar operations: The type of operation.
    :vartype operations: list[str]
    :ivar level: The assignment level to which rule is applied.
    :vartype level: str
    :ivar target_objects: The list of target objects.
    :vartype target_objects: list[str]
    :ivar inheritable_settings: The list of inheritable settings.
    :vartype inheritable_settings: list[str]
    :ivar enforced_settings: The list of enforced settings.
    :vartype enforced_settings: list[str]
    """

    _attribute_map = {
        "caller": {"key": "caller", "type": "str"},
        "operations": {"key": "operations", "type": "[str]"},
        "level": {"key": "level", "type": "str"},
        "target_objects": {"key": "targetObjects", "type": "[str]"},
        "inheritable_settings": {"key": "inheritableSettings", "type": "[str]"},
        "enforced_settings": {"key": "enforcedSettings", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        caller: Optional[str] = None,
        operations: Optional[List[str]] = None,
        level: Optional[str] = None,
        target_objects: Optional[List[str]] = None,
        inheritable_settings: Optional[List[str]] = None,
        enforced_settings: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword caller: The caller of the setting.
        :paramtype caller: str
        :keyword operations: The type of operation.
        :paramtype operations: list[str]
        :keyword level: The assignment level to which rule is applied.
        :paramtype level: str
        :keyword target_objects: The list of target objects.
        :paramtype target_objects: list[str]
        :keyword inheritable_settings: The list of inheritable settings.
        :paramtype inheritable_settings: list[str]
        :keyword enforced_settings: The list of enforced settings.
        :paramtype enforced_settings: list[str]
        """
        super().__init__(**kwargs)
        self.caller = caller
        self.operations = operations
        self.level = level
        self.target_objects = target_objects
        self.inheritable_settings = inheritable_settings
        self.enforced_settings = enforced_settings


class UserSet(_serialization.Model):
    """The detail of a user.

    :ivar user_type: The type of user. Known values are: "User" and "Group".
    :vartype user_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.UserType
    :ivar is_backup: The value indicating whether the user is a backup fallback approver.
    :vartype is_backup: bool
    :ivar id: The object id of the user.
    :vartype id: str
    :ivar description: The description of the user.
    :vartype description: str
    """

    _attribute_map = {
        "user_type": {"key": "userType", "type": "str"},
        "is_backup": {"key": "isBackup", "type": "bool"},
        "id": {"key": "id", "type": "str"},
        "description": {"key": "description", "type": "str"},
    }

    def __init__(
        self,
        *,
        user_type: Optional[Union[str, "_models.UserType"]] = None,
        is_backup: Optional[bool] = None,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        description: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword user_type: The type of user. Known values are: "User" and "Group".
        :paramtype user_type: str or ~azure.mgmt.authorization.v2020_10_01_preview.models.UserType
        :keyword is_backup: The value indicating whether the user is a backup fallback approver.
        :paramtype is_backup: bool
        :keyword id: The object id of the user.
        :paramtype id: str
        :keyword description: The description of the user.
        :paramtype description: str
        """
        super().__init__(**kwargs)
        self.user_type = user_type
        self.is_backup = is_backup
        self.id = id
        self.description = description


class ValidationResponse(_serialization.Model):
    """Validation response.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar is_valid: Whether or not validation succeeded.
    :vartype is_valid: bool
    :ivar error_info: Failed validation result details.
    :vartype error_info:
     ~azure.mgmt.authorization.v2020_10_01_preview.models.ValidationResponseErrorInfo
    """

    _validation = {
        "is_valid": {"readonly": True},
    }

    _attribute_map = {
        "is_valid": {"key": "isValid", "type": "bool"},
        "error_info": {"key": "errorInfo", "type": "ValidationResponseErrorInfo"},
    }

    def __init__(self, *, error_info: Optional["_models.ValidationResponseErrorInfo"] = None, **kwargs: Any) -> None:
        """
        :keyword error_info: Failed validation result details.
        :paramtype error_info:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.ValidationResponseErrorInfo
        """
        super().__init__(**kwargs)
        self.is_valid = None
        self.error_info = error_info


class ValidationResponseErrorInfo(_serialization.Model):
    """Failed validation result details.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: Error code indicating why validation failed.
    :vartype code: str
    :ivar message: Message indicating why validation failed.
    :vartype message: str
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
