# pylint: disable=too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
from io import IOBase
from typing import Any, AsyncIterable, Callable, Dict, IO, Optional, TypeVar, Union, overload
import urllib.parse

from azure.core.async_paging import AsyncItemPaged, AsyncList
from azure.core.exceptions import (
    ClientAuthenticationError,
    Htt<PERSON><PERSON><PERSON>ponse<PERSON>rror,
    ResourceExists<PERSON>rror,
    ResourceNotFoundError,
    ResourceNotModifiedError,
    map_error,
)
from azure.core.pipeline import PipelineResponse
from azure.core.pipeline.transport import AsyncHttpResponse
from azure.core.rest import HttpRequest
from azure.core.tracing.decorator import distributed_trace
from azure.core.tracing.decorator_async import distributed_trace_async
from azure.core.utils import case_insensitive_dict
from azure.mgmt.core.exceptions import ARMErrorFormat

from ... import models as _models
from ..._vendor import _convert_request
from ...operations._role_assignments_operations import (
    build_create_by_id_request,
    build_create_request,
    build_delete_by_id_request,
    build_delete_request,
    build_get_by_id_request,
    build_get_request,
    build_list_for_resource_group_request,
    build_list_for_resource_request,
    build_list_for_scope_request,
    build_list_for_subscription_request,
    build_validate_by_id_request,
    build_validate_request,
)

T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, AsyncHttpResponse], T, Dict[str, Any]], Any]]


class RoleAssignmentsOperations:
    """
    .. warning::
        **DO NOT** instantiate this class directly.

        Instead, you should access the following operations through
        :class:`~azure.mgmt.authorization.v2020_10_01_preview.aio.AuthorizationManagementClient`'s
        :attr:`role_assignments` attribute.
    """

    models = _models

    def __init__(self, *args, **kwargs) -> None:
        input_args = list(args)
        self._client = input_args.pop(0) if input_args else kwargs.pop("client")
        self._config = input_args.pop(0) if input_args else kwargs.pop("config")
        self._serialize = input_args.pop(0) if input_args else kwargs.pop("serializer")
        self._deserialize = input_args.pop(0) if input_args else kwargs.pop("deserializer")
        self._api_version = input_args.pop(0) if input_args else kwargs.pop("api_version")

    @distributed_trace
    def list_for_subscription(
        self, filter: Optional[str] = None, tenant_id: Optional[str] = None, **kwargs: Any
    ) -> AsyncIterable["_models.RoleAssignment"]:
        """List all role assignments that apply to a subscription.

        :param filter: The filter to apply on the operation. Use $filter=atScope() to return all role
         assignments at or above the scope. Use $filter=principalId eq {id} to return all role
         assignments at, above or below the scope for the specified principal. Default value is None.
        :type filter: str
        :param tenant_id: Tenant ID for cross-tenant request. Default value is None.
        :type tenant_id: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either RoleAssignment or the result of cls(response)
        :rtype:
         ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        cls: ClsType[_models.RoleAssignmentListResult] = kwargs.pop("cls", None)

        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        def prepare_request(next_link=None):
            if not next_link:

                request = build_list_for_subscription_request(
                    subscription_id=self._config.subscription_id,
                    filter=filter,
                    tenant_id=tenant_id,
                    api_version=api_version,
                    template_url=self.list_for_subscription.metadata["url"],
                    headers=_headers,
                    params=_params,
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)

            else:
                # make call to next link with the client's api-version
                _parsed_next_link = urllib.parse.urlparse(next_link)
                _next_request_params = case_insensitive_dict(
                    {
                        key: [urllib.parse.quote(v) for v in value]
                        for key, value in urllib.parse.parse_qs(_parsed_next_link.query).items()
                    }
                )
                _next_request_params["api-version"] = self._config.api_version
                request = HttpRequest(
                    "GET", urllib.parse.urljoin(next_link, _parsed_next_link.path), params=_next_request_params
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)
                request.method = "GET"
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("RoleAssignmentListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)  # type: ignore
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            _stream = False
            pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
                request, stream=_stream, **kwargs
            )
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
                raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_for_subscription.metadata = {
        "url": "/subscriptions/{subscriptionId}/providers/Microsoft.Authorization/roleAssignments"
    }

    @distributed_trace
    def list_for_resource_group(
        self, resource_group_name: str, filter: Optional[str] = None, tenant_id: Optional[str] = None, **kwargs: Any
    ) -> AsyncIterable["_models.RoleAssignment"]:
        """List all role assignments that apply to a resource group.

        :param resource_group_name: The name of the resource group. The name is case insensitive.
         Required.
        :type resource_group_name: str
        :param filter: The filter to apply on the operation. Use $filter=atScope() to return all role
         assignments at or above the scope. Use $filter=principalId eq {id} to return all role
         assignments at, above or below the scope for the specified principal. Default value is None.
        :type filter: str
        :param tenant_id: Tenant ID for cross-tenant request. Default value is None.
        :type tenant_id: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either RoleAssignment or the result of cls(response)
        :rtype:
         ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        cls: ClsType[_models.RoleAssignmentListResult] = kwargs.pop("cls", None)

        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        def prepare_request(next_link=None):
            if not next_link:

                request = build_list_for_resource_group_request(
                    resource_group_name=resource_group_name,
                    subscription_id=self._config.subscription_id,
                    filter=filter,
                    tenant_id=tenant_id,
                    api_version=api_version,
                    template_url=self.list_for_resource_group.metadata["url"],
                    headers=_headers,
                    params=_params,
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)

            else:
                # make call to next link with the client's api-version
                _parsed_next_link = urllib.parse.urlparse(next_link)
                _next_request_params = case_insensitive_dict(
                    {
                        key: [urllib.parse.quote(v) for v in value]
                        for key, value in urllib.parse.parse_qs(_parsed_next_link.query).items()
                    }
                )
                _next_request_params["api-version"] = self._config.api_version
                request = HttpRequest(
                    "GET", urllib.parse.urljoin(next_link, _parsed_next_link.path), params=_next_request_params
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)
                request.method = "GET"
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("RoleAssignmentListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)  # type: ignore
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            _stream = False
            pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
                request, stream=_stream, **kwargs
            )
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
                raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_for_resource_group.metadata = {
        "url": "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Authorization/roleAssignments"
    }

    @distributed_trace
    def list_for_resource(
        self,
        resource_group_name: str,
        resource_provider_namespace: str,
        resource_type: str,
        resource_name: str,
        filter: Optional[str] = None,
        tenant_id: Optional[str] = None,
        **kwargs: Any
    ) -> AsyncIterable["_models.RoleAssignment"]:
        """List all role assignments that apply to a resource.

        :param resource_group_name: The name of the resource group. The name is case insensitive.
         Required.
        :type resource_group_name: str
        :param resource_provider_namespace: The namespace of the resource provider. Required.
        :type resource_provider_namespace: str
        :param resource_type: The resource type name. For example the type name of a web app is 'sites'
         (from Microsoft.Web/sites). Required.
        :type resource_type: str
        :param resource_name: The resource name. Required.
        :type resource_name: str
        :param filter: The filter to apply on the operation. Use $filter=atScope() to return all role
         assignments at or above the scope. Use $filter=principalId eq {id} to return all role
         assignments at, above or below the scope for the specified principal. Default value is None.
        :type filter: str
        :param tenant_id: Tenant ID for cross-tenant request. Default value is None.
        :type tenant_id: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either RoleAssignment or the result of cls(response)
        :rtype:
         ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        cls: ClsType[_models.RoleAssignmentListResult] = kwargs.pop("cls", None)

        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        def prepare_request(next_link=None):
            if not next_link:

                request = build_list_for_resource_request(
                    resource_group_name=resource_group_name,
                    resource_provider_namespace=resource_provider_namespace,
                    resource_type=resource_type,
                    resource_name=resource_name,
                    subscription_id=self._config.subscription_id,
                    filter=filter,
                    tenant_id=tenant_id,
                    api_version=api_version,
                    template_url=self.list_for_resource.metadata["url"],
                    headers=_headers,
                    params=_params,
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)

            else:
                # make call to next link with the client's api-version
                _parsed_next_link = urllib.parse.urlparse(next_link)
                _next_request_params = case_insensitive_dict(
                    {
                        key: [urllib.parse.quote(v) for v in value]
                        for key, value in urllib.parse.parse_qs(_parsed_next_link.query).items()
                    }
                )
                _next_request_params["api-version"] = self._config.api_version
                request = HttpRequest(
                    "GET", urllib.parse.urljoin(next_link, _parsed_next_link.path), params=_next_request_params
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)
                request.method = "GET"
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("RoleAssignmentListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)  # type: ignore
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            _stream = False
            pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
                request, stream=_stream, **kwargs
            )
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
                raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_for_resource.metadata = {
        "url": "/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/{resourceType}/{resourceName}/providers/Microsoft.Authorization/roleAssignments"
    }

    @distributed_trace_async
    async def get(
        self, scope: str, role_assignment_name: str, tenant_id: Optional[str] = None, **kwargs: Any
    ) -> _models.RoleAssignment:
        """Get a role assignment by scope and name.

        :param scope: The scope of the operation or resource. Valid scopes are: subscription (format:
         '/subscriptions/{subscriptionId}'), resource group (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'.
         Required.
        :type scope: str
        :param role_assignment_name: The name of the role assignment. It can be any valid GUID.
         Required.
        :type role_assignment_name: str
        :param tenant_id: Tenant ID for cross-tenant request. Default value is None.
        :type tenant_id: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        cls: ClsType[_models.RoleAssignment] = kwargs.pop("cls", None)

        request = build_get_request(
            scope=scope,
            role_assignment_name=role_assignment_name,
            tenant_id=tenant_id,
            api_version=api_version,
            template_url=self.get.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

        deserialized = self._deserialize("RoleAssignment", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get.metadata = {"url": "/{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}"}

    @overload
    async def create(
        self,
        scope: str,
        role_assignment_name: str,
        parameters: _models.RoleAssignmentCreateParameters,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.RoleAssignment:
        """Create or update a role assignment by scope and name.

        :param scope: The scope of the operation or resource. Valid scopes are: subscription (format:
         '/subscriptions/{subscriptionId}'), resource group (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'.
         Required.
        :type scope: str
        :param role_assignment_name: The name of the role assignment. It can be any valid GUID.
         Required.
        :type role_assignment_name: str
        :param parameters: Parameters for the role assignment. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentCreateParameters
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    async def create(
        self,
        scope: str,
        role_assignment_name: str,
        parameters: IO,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.RoleAssignment:
        """Create or update a role assignment by scope and name.

        :param scope: The scope of the operation or resource. Valid scopes are: subscription (format:
         '/subscriptions/{subscriptionId}'), resource group (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'.
         Required.
        :type scope: str
        :param role_assignment_name: The name of the role assignment. It can be any valid GUID.
         Required.
        :type role_assignment_name: str
        :param parameters: Parameters for the role assignment. Required.
        :type parameters: IO
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace_async
    async def create(
        self,
        scope: str,
        role_assignment_name: str,
        parameters: Union[_models.RoleAssignmentCreateParameters, IO],
        **kwargs: Any
    ) -> _models.RoleAssignment:
        """Create or update a role assignment by scope and name.

        :param scope: The scope of the operation or resource. Valid scopes are: subscription (format:
         '/subscriptions/{subscriptionId}'), resource group (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'.
         Required.
        :type scope: str
        :param role_assignment_name: The name of the role assignment. It can be any valid GUID.
         Required.
        :type role_assignment_name: str
        :param parameters: Parameters for the role assignment. Is either a
         RoleAssignmentCreateParameters type or a IO type. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentCreateParameters or IO
        :keyword content_type: Body Parameter content-type. Known values are: 'application/json'.
         Default value is None.
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.RoleAssignment] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(parameters, (IOBase, bytes)):
            _content = parameters
        else:
            _json = self._serialize.body(parameters, "RoleAssignmentCreateParameters")

        request = build_create_request(
            scope=scope,
            role_assignment_name=role_assignment_name,
            api_version=api_version,
            content_type=content_type,
            json=_json,
            content=_content,
            template_url=self.create.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200, 201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

        if response.status_code == 200:
            deserialized = self._deserialize("RoleAssignment", pipeline_response)

        if response.status_code == 201:
            deserialized = self._deserialize("RoleAssignment", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    create.metadata = {"url": "/{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}"}

    @distributed_trace_async
    async def delete(
        self, scope: str, role_assignment_name: str, tenant_id: Optional[str] = None, **kwargs: Any
    ) -> Optional[_models.RoleAssignment]:
        """Delete a role assignment by scope and name.

        :param scope: The scope of the operation or resource. Valid scopes are: subscription (format:
         '/subscriptions/{subscriptionId}'), resource group (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'.
         Required.
        :type scope: str
        :param role_assignment_name: The name of the role assignment. It can be any valid GUID.
         Required.
        :type role_assignment_name: str
        :param tenant_id: Tenant ID for cross-tenant request. Default value is None.
        :type tenant_id: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or None or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment or None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        cls: ClsType[Optional[_models.RoleAssignment]] = kwargs.pop("cls", None)

        request = build_delete_request(
            scope=scope,
            role_assignment_name=role_assignment_name,
            tenant_id=tenant_id,
            api_version=api_version,
            template_url=self.delete.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("RoleAssignment", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    delete.metadata = {"url": "/{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}"}

    @overload
    async def validate(
        self,
        scope: str,
        role_assignment_name: str,
        parameters: _models.RoleAssignmentCreateParameters,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.ValidationResponse:
        """Validate a role assignment create or update operation by scope and name.

        :param scope: The scope of the operation or resource. Valid scopes are: subscription (format:
         '/subscriptions/{subscriptionId}'), resource group (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'.
         Required.
        :type scope: str
        :param role_assignment_name: The name of the role assignment. It can be any valid GUID.
         Required.
        :type role_assignment_name: str
        :param parameters: Parameters for the role assignment. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentCreateParameters
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: ValidationResponse or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.ValidationResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    async def validate(
        self,
        scope: str,
        role_assignment_name: str,
        parameters: IO,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.ValidationResponse:
        """Validate a role assignment create or update operation by scope and name.

        :param scope: The scope of the operation or resource. Valid scopes are: subscription (format:
         '/subscriptions/{subscriptionId}'), resource group (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'.
         Required.
        :type scope: str
        :param role_assignment_name: The name of the role assignment. It can be any valid GUID.
         Required.
        :type role_assignment_name: str
        :param parameters: Parameters for the role assignment. Required.
        :type parameters: IO
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: ValidationResponse or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.ValidationResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace_async
    async def validate(
        self,
        scope: str,
        role_assignment_name: str,
        parameters: Union[_models.RoleAssignmentCreateParameters, IO],
        **kwargs: Any
    ) -> _models.ValidationResponse:
        """Validate a role assignment create or update operation by scope and name.

        :param scope: The scope of the operation or resource. Valid scopes are: subscription (format:
         '/subscriptions/{subscriptionId}'), resource group (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'.
         Required.
        :type scope: str
        :param role_assignment_name: The name of the role assignment. It can be any valid GUID.
         Required.
        :type role_assignment_name: str
        :param parameters: Parameters for the role assignment. Is either a
         RoleAssignmentCreateParameters type or a IO type. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentCreateParameters or IO
        :keyword content_type: Body Parameter content-type. Known values are: 'application/json'.
         Default value is None.
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: ValidationResponse or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.ValidationResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.ValidationResponse] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(parameters, (IOBase, bytes)):
            _content = parameters
        else:
            _json = self._serialize.body(parameters, "RoleAssignmentCreateParameters")

        request = build_validate_request(
            scope=scope,
            role_assignment_name=role_assignment_name,
            api_version=api_version,
            content_type=content_type,
            json=_json,
            content=_content,
            template_url=self.validate.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

        deserialized = self._deserialize("ValidationResponse", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    validate.metadata = {
        "url": "/{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}/validate"
    }

    @distributed_trace
    def list_for_scope(
        self, scope: str, filter: Optional[str] = None, tenant_id: Optional[str] = None, **kwargs: Any
    ) -> AsyncIterable["_models.RoleAssignment"]:
        """List all role assignments that apply to a scope.

        :param scope: The scope of the operation or resource. Valid scopes are: subscription (format:
         '/subscriptions/{subscriptionId}'), resource group (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}', or resource (format:
         '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/{resourceProviderNamespace}/[{parentResourcePath}/]{resourceType}/{resourceName}'.
         Required.
        :type scope: str
        :param filter: The filter to apply on the operation. Use $filter=atScope() to return all role
         assignments at or above the scope. Use $filter=principalId eq {id} to return all role
         assignments at, above or below the scope for the specified principal. Default value is None.
        :type filter: str
        :param tenant_id: Tenant ID for cross-tenant request. Default value is None.
        :type tenant_id: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: An iterator like instance of either RoleAssignment or the result of cls(response)
        :rtype:
         ~azure.core.async_paging.AsyncItemPaged[~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment]
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        cls: ClsType[_models.RoleAssignmentListResult] = kwargs.pop("cls", None)

        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        def prepare_request(next_link=None):
            if not next_link:

                request = build_list_for_scope_request(
                    scope=scope,
                    filter=filter,
                    tenant_id=tenant_id,
                    api_version=api_version,
                    template_url=self.list_for_scope.metadata["url"],
                    headers=_headers,
                    params=_params,
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)

            else:
                # make call to next link with the client's api-version
                _parsed_next_link = urllib.parse.urlparse(next_link)
                _next_request_params = case_insensitive_dict(
                    {
                        key: [urllib.parse.quote(v) for v in value]
                        for key, value in urllib.parse.parse_qs(_parsed_next_link.query).items()
                    }
                )
                _next_request_params["api-version"] = self._config.api_version
                request = HttpRequest(
                    "GET", urllib.parse.urljoin(next_link, _parsed_next_link.path), params=_next_request_params
                )
                request = _convert_request(request)
                request.url = self._client.format_url(request.url)
                request.method = "GET"
            return request

        async def extract_data(pipeline_response):
            deserialized = self._deserialize("RoleAssignmentListResult", pipeline_response)
            list_of_elem = deserialized.value
            if cls:
                list_of_elem = cls(list_of_elem)  # type: ignore
            return deserialized.next_link or None, AsyncList(list_of_elem)

        async def get_next(next_link=None):
            request = prepare_request(next_link)

            _stream = False
            pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
                request, stream=_stream, **kwargs
            )
            response = pipeline_response.http_response

            if response.status_code not in [200]:
                map_error(status_code=response.status_code, response=response, error_map=error_map)
                error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
                raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

            return pipeline_response

        return AsyncItemPaged(get_next, extract_data)

    list_for_scope.metadata = {"url": "/{scope}/providers/Microsoft.Authorization/roleAssignments"}

    @distributed_trace_async
    async def get_by_id(
        self, role_assignment_id: str, tenant_id: Optional[str] = None, **kwargs: Any
    ) -> _models.RoleAssignment:
        """Get a role assignment by ID.

        :param role_assignment_id: The fully qualified ID of the role assignment including scope,
         resource name, and resource type. Format:
         /{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}. Example:
         /subscriptions/:code:`<SUB_ID>`/resourcegroups/:code:`<RESOURCE_GROUP>`/providers/Microsoft.Authorization/roleAssignments/:code:`<ROLE_ASSIGNMENT_NAME>`.
         Required.
        :type role_assignment_id: str
        :param tenant_id: Tenant ID for cross-tenant request. Default value is None.
        :type tenant_id: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        cls: ClsType[_models.RoleAssignment] = kwargs.pop("cls", None)

        request = build_get_by_id_request(
            role_assignment_id=role_assignment_id,
            tenant_id=tenant_id,
            api_version=api_version,
            template_url=self.get_by_id.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

        deserialized = self._deserialize("RoleAssignment", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    get_by_id.metadata = {"url": "/{roleAssignmentId}"}

    @overload
    async def create_by_id(
        self,
        role_assignment_id: str,
        parameters: _models.RoleAssignmentCreateParameters,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.RoleAssignment:
        """Create or update a role assignment by ID.

        :param role_assignment_id: The fully qualified ID of the role assignment including scope,
         resource name, and resource type. Format:
         /{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}. Example:
         /subscriptions/:code:`<SUB_ID>`/resourcegroups/:code:`<RESOURCE_GROUP>`/providers/Microsoft.Authorization/roleAssignments/:code:`<ROLE_ASSIGNMENT_NAME>`.
         Required.
        :type role_assignment_id: str
        :param parameters: Parameters for the role assignment. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentCreateParameters
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    async def create_by_id(
        self, role_assignment_id: str, parameters: IO, *, content_type: str = "application/json", **kwargs: Any
    ) -> _models.RoleAssignment:
        """Create or update a role assignment by ID.

        :param role_assignment_id: The fully qualified ID of the role assignment including scope,
         resource name, and resource type. Format:
         /{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}. Example:
         /subscriptions/:code:`<SUB_ID>`/resourcegroups/:code:`<RESOURCE_GROUP>`/providers/Microsoft.Authorization/roleAssignments/:code:`<ROLE_ASSIGNMENT_NAME>`.
         Required.
        :type role_assignment_id: str
        :param parameters: Parameters for the role assignment. Required.
        :type parameters: IO
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace_async
    async def create_by_id(
        self, role_assignment_id: str, parameters: Union[_models.RoleAssignmentCreateParameters, IO], **kwargs: Any
    ) -> _models.RoleAssignment:
        """Create or update a role assignment by ID.

        :param role_assignment_id: The fully qualified ID of the role assignment including scope,
         resource name, and resource type. Format:
         /{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}. Example:
         /subscriptions/:code:`<SUB_ID>`/resourcegroups/:code:`<RESOURCE_GROUP>`/providers/Microsoft.Authorization/roleAssignments/:code:`<ROLE_ASSIGNMENT_NAME>`.
         Required.
        :type role_assignment_id: str
        :param parameters: Parameters for the role assignment. Is either a
         RoleAssignmentCreateParameters type or a IO type. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentCreateParameters or IO
        :keyword content_type: Body Parameter content-type. Known values are: 'application/json'.
         Default value is None.
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.RoleAssignment] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(parameters, (IOBase, bytes)):
            _content = parameters
        else:
            _json = self._serialize.body(parameters, "RoleAssignmentCreateParameters")

        request = build_create_by_id_request(
            role_assignment_id=role_assignment_id,
            api_version=api_version,
            content_type=content_type,
            json=_json,
            content=_content,
            template_url=self.create_by_id.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200, 201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

        if response.status_code == 200:
            deserialized = self._deserialize("RoleAssignment", pipeline_response)

        if response.status_code == 201:
            deserialized = self._deserialize("RoleAssignment", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})  # type: ignore

        return deserialized  # type: ignore

    create_by_id.metadata = {"url": "/{roleAssignmentId}"}

    @distributed_trace_async
    async def delete_by_id(
        self, role_assignment_id: str, tenant_id: Optional[str] = None, **kwargs: Any
    ) -> Optional[_models.RoleAssignment]:
        """Delete a role assignment by ID.

        :param role_assignment_id: The fully qualified ID of the role assignment including scope,
         resource name, and resource type. Format:
         /{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}. Example:
         /subscriptions/:code:`<SUB_ID>`/resourcegroups/:code:`<RESOURCE_GROUP>`/providers/Microsoft.Authorization/roleAssignments/:code:`<ROLE_ASSIGNMENT_NAME>`.
         Required.
        :type role_assignment_id: str
        :param tenant_id: Tenant ID for cross-tenant request. Default value is None.
        :type tenant_id: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: RoleAssignment or None or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignment or None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        cls: ClsType[Optional[_models.RoleAssignment]] = kwargs.pop("cls", None)

        request = build_delete_by_id_request(
            role_assignment_id=role_assignment_id,
            tenant_id=tenant_id,
            api_version=api_version,
            template_url=self.delete_by_id.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200, 204]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

        deserialized = None
        if response.status_code == 200:
            deserialized = self._deserialize("RoleAssignment", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    delete_by_id.metadata = {"url": "/{roleAssignmentId}"}

    @overload
    async def validate_by_id(
        self,
        role_assignment_id: str,
        parameters: _models.RoleAssignmentCreateParameters,
        *,
        content_type: str = "application/json",
        **kwargs: Any
    ) -> _models.ValidationResponse:
        """Validate a role assignment create or update operation by ID.

        :param role_assignment_id: The fully qualified ID of the role assignment including scope,
         resource name, and resource type. Format:
         /{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}. Example:
         /subscriptions/:code:`<SUB_ID>`/resourcegroups/:code:`<RESOURCE_GROUP>`/providers/Microsoft.Authorization/roleAssignments/:code:`<ROLE_ASSIGNMENT_NAME>`.
         Required.
        :type role_assignment_id: str
        :param parameters: Parameters for the role assignment. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentCreateParameters
        :keyword content_type: Body Parameter content-type. Content type parameter for JSON body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: ValidationResponse or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.ValidationResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @overload
    async def validate_by_id(
        self, role_assignment_id: str, parameters: IO, *, content_type: str = "application/json", **kwargs: Any
    ) -> _models.ValidationResponse:
        """Validate a role assignment create or update operation by ID.

        :param role_assignment_id: The fully qualified ID of the role assignment including scope,
         resource name, and resource type. Format:
         /{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}. Example:
         /subscriptions/:code:`<SUB_ID>`/resourcegroups/:code:`<RESOURCE_GROUP>`/providers/Microsoft.Authorization/roleAssignments/:code:`<ROLE_ASSIGNMENT_NAME>`.
         Required.
        :type role_assignment_id: str
        :param parameters: Parameters for the role assignment. Required.
        :type parameters: IO
        :keyword content_type: Body Parameter content-type. Content type parameter for binary body.
         Default value is "application/json".
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: ValidationResponse or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.ValidationResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """

    @distributed_trace_async
    async def validate_by_id(
        self, role_assignment_id: str, parameters: Union[_models.RoleAssignmentCreateParameters, IO], **kwargs: Any
    ) -> _models.ValidationResponse:
        """Validate a role assignment create or update operation by ID.

        :param role_assignment_id: The fully qualified ID of the role assignment including scope,
         resource name, and resource type. Format:
         /{scope}/providers/Microsoft.Authorization/roleAssignments/{roleAssignmentName}. Example:
         /subscriptions/:code:`<SUB_ID>`/resourcegroups/:code:`<RESOURCE_GROUP>`/providers/Microsoft.Authorization/roleAssignments/:code:`<ROLE_ASSIGNMENT_NAME>`.
         Required.
        :type role_assignment_id: str
        :param parameters: Parameters for the role assignment. Is either a
         RoleAssignmentCreateParameters type or a IO type. Required.
        :type parameters:
         ~azure.mgmt.authorization.v2020_10_01_preview.models.RoleAssignmentCreateParameters or IO
        :keyword content_type: Body Parameter content-type. Known values are: 'application/json'.
         Default value is None.
        :paramtype content_type: str
        :keyword callable cls: A custom type or function that will be passed the direct response
        :return: ValidationResponse or the result of cls(response)
        :rtype: ~azure.mgmt.authorization.v2020_10_01_preview.models.ValidationResponse
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        api_version: str = kwargs.pop(
            "api_version", _params.pop("api-version", self._api_version or "2020-10-01-preview")
        )
        content_type: Optional[str] = kwargs.pop("content_type", _headers.pop("Content-Type", None))
        cls: ClsType[_models.ValidationResponse] = kwargs.pop("cls", None)

        content_type = content_type or "application/json"
        _json = None
        _content = None
        if isinstance(parameters, (IOBase, bytes)):
            _content = parameters
        else:
            _json = self._serialize.body(parameters, "RoleAssignmentCreateParameters")

        request = build_validate_by_id_request(
            role_assignment_id=role_assignment_id,
            api_version=api_version,
            content_type=content_type,
            json=_json,
            content=_content,
            template_url=self.validate_by_id.metadata["url"],
            headers=_headers,
            params=_params,
        )
        request = _convert_request(request)
        request.url = self._client.format_url(request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.ErrorResponse, pipeline_response)
            raise HttpResponseError(response=response, model=error, error_format=ARMErrorFormat)

        deserialized = self._deserialize("ValidationResponse", pipeline_response)

        if cls:
            return cls(pipeline_response, deserialized, {})

        return deserialized

    validate_by_id.metadata = {"url": "/{roleAssignmentId}/validate"}
