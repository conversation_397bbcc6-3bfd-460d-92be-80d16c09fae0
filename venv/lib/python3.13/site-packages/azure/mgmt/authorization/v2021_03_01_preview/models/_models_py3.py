# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

import datetime
from typing import Any, List, Optional, TYPE_CHECKING, Union

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class AccessReviewDecision(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Access Review.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The access review decision id.
    :vartype id: str
    :ivar name: The access review decision name.
    :vartype name: str
    :ivar type: The resource type.
    :vartype type: str
    :ivar recommendation: The feature- generated recommendation shown to the reviewer. Known values
     are: "Approve", "Deny", and "NoInfoAvailable".
    :vartype recommendation: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessRecommendationType
    :ivar decision: The decision on the approval step. This value is initially set to NotReviewed.
     Approvers can take action of Approve/Deny. Known values are: "Approve", "Deny", "NotReviewed",
     "DontKnow", and "NotNotified".
    :vartype decision: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewResult
    :ivar justification: Justification provided by approvers for their action.
    :vartype justification: str
    :ivar reviewed_date_time: Date Time when a decision was taken.
    :vartype reviewed_date_time: ~datetime.datetime
    :ivar apply_result: The outcome of applying the decision. Known values are: "New", "Applying",
     "AppliedSuccessfully", "AppliedWithUnknownFailure", "AppliedSuccessfullyButObjectNotFound", and
     "ApplyNotSupported".
    :vartype apply_result: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewApplyResult
    :ivar applied_date_time: The date and time when the review decision was applied.
    :vartype applied_date_time: ~datetime.datetime
    :ivar principal_id_properties_applied_by_principal_id: The identity id.
    :vartype principal_id_properties_applied_by_principal_id: str
    :ivar principal_type_properties_applied_by_principal_type: The identity type :
     user/servicePrincipal. Known values are: "user" and "servicePrincipal".
    :vartype principal_type_properties_applied_by_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewActorIdentityType
    :ivar principal_name_properties_applied_by_principal_name: The identity display name.
    :vartype principal_name_properties_applied_by_principal_name: str
    :ivar user_principal_name_properties_applied_by_user_principal_name: The user principal name(if
     valid).
    :vartype user_principal_name_properties_applied_by_user_principal_name: str
    :ivar principal_id_properties_reviewed_by_principal_id: The identity id.
    :vartype principal_id_properties_reviewed_by_principal_id: str
    :ivar principal_type_properties_reviewed_by_principal_type: The identity type :
     user/servicePrincipal. Known values are: "user" and "servicePrincipal".
    :vartype principal_type_properties_reviewed_by_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewActorIdentityType
    :ivar principal_name_properties_reviewed_by_principal_name: The identity display name.
    :vartype principal_name_properties_reviewed_by_principal_name: str
    :ivar user_principal_name_properties_reviewed_by_user_principal_name: The user principal
     name(if valid).
    :vartype user_principal_name_properties_reviewed_by_user_principal_name: str
    :ivar type_properties_resource_type: The type of resource. "azureRole"
    :vartype type_properties_resource_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.DecisionResourceType
    :ivar id_properties_resource_id: The id of resource associated with a decision record.
    :vartype id_properties_resource_id: str
    :ivar display_name_properties_resource_display_name: The display name of resource associated
     with a decision record.
    :vartype display_name_properties_resource_display_name: str
    :ivar type_properties_principal_type: The type of decision target : User/ServicePrincipal.
     Known values are: "user" and "servicePrincipal".
    :vartype type_properties_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.DecisionTargetType
    :ivar id_properties_principal_id: The id of principal whose access was reviewed.
    :vartype id_properties_principal_id: str
    :ivar display_name_properties_principal_display_name: The display name of the user whose access
     was reviewed.
    :vartype display_name_properties_principal_display_name: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "recommendation": {"readonly": True},
        "reviewed_date_time": {"readonly": True},
        "apply_result": {"readonly": True},
        "applied_date_time": {"readonly": True},
        "principal_id_properties_applied_by_principal_id": {"readonly": True},
        "principal_type_properties_applied_by_principal_type": {"readonly": True},
        "principal_name_properties_applied_by_principal_name": {"readonly": True},
        "user_principal_name_properties_applied_by_user_principal_name": {"readonly": True},
        "principal_id_properties_reviewed_by_principal_id": {"readonly": True},
        "principal_type_properties_reviewed_by_principal_type": {"readonly": True},
        "principal_name_properties_reviewed_by_principal_name": {"readonly": True},
        "user_principal_name_properties_reviewed_by_user_principal_name": {"readonly": True},
        "id_properties_resource_id": {"readonly": True},
        "display_name_properties_resource_display_name": {"readonly": True},
        "id_properties_principal_id": {"readonly": True},
        "display_name_properties_principal_display_name": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "recommendation": {"key": "properties.recommendation", "type": "str"},
        "decision": {"key": "properties.decision", "type": "str"},
        "justification": {"key": "properties.justification", "type": "str"},
        "reviewed_date_time": {"key": "properties.reviewedDateTime", "type": "iso-8601"},
        "apply_result": {"key": "properties.applyResult", "type": "str"},
        "applied_date_time": {"key": "properties.appliedDateTime", "type": "iso-8601"},
        "principal_id_properties_applied_by_principal_id": {"key": "properties.appliedBy.principalId", "type": "str"},
        "principal_type_properties_applied_by_principal_type": {
            "key": "properties.appliedBy.principalType",
            "type": "str",
        },
        "principal_name_properties_applied_by_principal_name": {
            "key": "properties.appliedBy.principalName",
            "type": "str",
        },
        "user_principal_name_properties_applied_by_user_principal_name": {
            "key": "properties.appliedBy.userPrincipalName",
            "type": "str",
        },
        "principal_id_properties_reviewed_by_principal_id": {"key": "properties.reviewedBy.principalId", "type": "str"},
        "principal_type_properties_reviewed_by_principal_type": {
            "key": "properties.reviewedBy.principalType",
            "type": "str",
        },
        "principal_name_properties_reviewed_by_principal_name": {
            "key": "properties.reviewedBy.principalName",
            "type": "str",
        },
        "user_principal_name_properties_reviewed_by_user_principal_name": {
            "key": "properties.reviewedBy.userPrincipalName",
            "type": "str",
        },
        "type_properties_resource_type": {"key": "properties.resource.type", "type": "str"},
        "id_properties_resource_id": {"key": "properties.resource.id", "type": "str"},
        "display_name_properties_resource_display_name": {"key": "properties.resource.displayName", "type": "str"},
        "type_properties_principal_type": {"key": "properties.principal.type", "type": "str"},
        "id_properties_principal_id": {"key": "properties.principal.id", "type": "str"},
        "display_name_properties_principal_display_name": {"key": "properties.principal.displayName", "type": "str"},
    }

    def __init__(
        self,
        *,
        decision: Optional[Union[str, "_models.AccessReviewResult"]] = None,
        justification: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword decision: The decision on the approval step. This value is initially set to
         NotReviewed. Approvers can take action of Approve/Deny. Known values are: "Approve", "Deny",
         "NotReviewed", "DontKnow", and "NotNotified".
        :paramtype decision: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewResult
        :keyword justification: Justification provided by approvers for their action.
        :paramtype justification: str
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.recommendation = None
        self.decision = decision
        self.justification = justification
        self.reviewed_date_time = None
        self.apply_result = None
        self.applied_date_time = None
        self.principal_id_properties_applied_by_principal_id = None
        self.principal_type_properties_applied_by_principal_type = None
        self.principal_name_properties_applied_by_principal_name = None
        self.user_principal_name_properties_applied_by_user_principal_name = None
        self.principal_id_properties_reviewed_by_principal_id = None
        self.principal_type_properties_reviewed_by_principal_type = None
        self.principal_name_properties_reviewed_by_principal_name = None
        self.user_principal_name_properties_reviewed_by_user_principal_name = None
        self.type_properties_resource_type: Optional[str] = None
        self.id_properties_resource_id = None
        self.display_name_properties_resource_display_name = None
        self.type_properties_principal_type: Optional[str] = None
        self.id_properties_principal_id = None
        self.display_name_properties_principal_display_name = None


class AccessReviewDecisionIdentity(_serialization.Model):
    """Target of the decision.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    AccessReviewDecisionServicePrincipalIdentity, AccessReviewDecisionUserIdentity

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar type: The type of decision target : User/ServicePrincipal. Required. Known values are:
     "user" and "servicePrincipal".
    :vartype type: str or ~azure.mgmt.authorization.v2021_03_01_preview.models.DecisionTargetType
    :ivar id: The id of principal whose access was reviewed.
    :vartype id: str
    :ivar display_name: The display name of the user whose access was reviewed.
    :vartype display_name: str
    """

    _validation = {
        "type": {"required": True},
        "id": {"readonly": True},
        "display_name": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
    }

    _subtype_map = {
        "type": {
            "servicePrincipal": "AccessReviewDecisionServicePrincipalIdentity",
            "user": "AccessReviewDecisionUserIdentity",
        }
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type: Optional[str] = None
        self.id = None
        self.display_name = None


class AccessReviewDecisionListResult(_serialization.Model):
    """List of access review decisions.

    :ivar value: Access Review Decision list.
    :vartype value: list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewDecision]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[AccessReviewDecision]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.AccessReviewDecision"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Access Review Decision list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewDecision]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class AccessReviewDecisionProperties(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Approval Step.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar recommendation: The feature- generated recommendation shown to the reviewer. Known values
     are: "Approve", "Deny", and "NoInfoAvailable".
    :vartype recommendation: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessRecommendationType
    :ivar decision: The decision on the approval step. This value is initially set to NotReviewed.
     Approvers can take action of Approve/Deny. Known values are: "Approve", "Deny", "NotReviewed",
     "DontKnow", and "NotNotified".
    :vartype decision: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewResult
    :ivar justification: Justification provided by approvers for their action.
    :vartype justification: str
    :ivar reviewed_date_time: Date Time when a decision was taken.
    :vartype reviewed_date_time: ~datetime.datetime
    :ivar apply_result: The outcome of applying the decision. Known values are: "New", "Applying",
     "AppliedSuccessfully", "AppliedWithUnknownFailure", "AppliedSuccessfullyButObjectNotFound", and
     "ApplyNotSupported".
    :vartype apply_result: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewApplyResult
    :ivar applied_date_time: The date and time when the review decision was applied.
    :vartype applied_date_time: ~datetime.datetime
    :ivar principal_id_applied_by_principal_id: The identity id.
    :vartype principal_id_applied_by_principal_id: str
    :ivar principal_type_applied_by_principal_type: The identity type : user/servicePrincipal.
     Known values are: "user" and "servicePrincipal".
    :vartype principal_type_applied_by_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewActorIdentityType
    :ivar principal_name_applied_by_principal_name: The identity display name.
    :vartype principal_name_applied_by_principal_name: str
    :ivar user_principal_name_applied_by_user_principal_name: The user principal name(if valid).
    :vartype user_principal_name_applied_by_user_principal_name: str
    :ivar principal_id_reviewed_by_principal_id: The identity id.
    :vartype principal_id_reviewed_by_principal_id: str
    :ivar principal_type_reviewed_by_principal_type: The identity type : user/servicePrincipal.
     Known values are: "user" and "servicePrincipal".
    :vartype principal_type_reviewed_by_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewActorIdentityType
    :ivar principal_name_reviewed_by_principal_name: The identity display name.
    :vartype principal_name_reviewed_by_principal_name: str
    :ivar user_principal_name_reviewed_by_user_principal_name: The user principal name(if valid).
    :vartype user_principal_name_reviewed_by_user_principal_name: str
    :ivar type_resource_type: The type of resource. "azureRole"
    :vartype type_resource_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.DecisionResourceType
    :ivar id_resource_id: The id of resource associated with a decision record.
    :vartype id_resource_id: str
    :ivar display_name_resource_display_name: The display name of resource associated with a
     decision record.
    :vartype display_name_resource_display_name: str
    :ivar type_principal_type: The type of decision target : User/ServicePrincipal. Known values
     are: "user" and "servicePrincipal".
    :vartype type_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.DecisionTargetType
    :ivar id_principal_id: The id of principal whose access was reviewed.
    :vartype id_principal_id: str
    :ivar display_name_principal_display_name: The display name of the user whose access was
     reviewed.
    :vartype display_name_principal_display_name: str
    """

    _validation = {
        "recommendation": {"readonly": True},
        "reviewed_date_time": {"readonly": True},
        "apply_result": {"readonly": True},
        "applied_date_time": {"readonly": True},
        "principal_id_applied_by_principal_id": {"readonly": True},
        "principal_type_applied_by_principal_type": {"readonly": True},
        "principal_name_applied_by_principal_name": {"readonly": True},
        "user_principal_name_applied_by_user_principal_name": {"readonly": True},
        "principal_id_reviewed_by_principal_id": {"readonly": True},
        "principal_type_reviewed_by_principal_type": {"readonly": True},
        "principal_name_reviewed_by_principal_name": {"readonly": True},
        "user_principal_name_reviewed_by_user_principal_name": {"readonly": True},
        "id_resource_id": {"readonly": True},
        "display_name_resource_display_name": {"readonly": True},
        "id_principal_id": {"readonly": True},
        "display_name_principal_display_name": {"readonly": True},
    }

    _attribute_map = {
        "recommendation": {"key": "recommendation", "type": "str"},
        "decision": {"key": "decision", "type": "str"},
        "justification": {"key": "justification", "type": "str"},
        "reviewed_date_time": {"key": "reviewedDateTime", "type": "iso-8601"},
        "apply_result": {"key": "applyResult", "type": "str"},
        "applied_date_time": {"key": "appliedDateTime", "type": "iso-8601"},
        "principal_id_applied_by_principal_id": {"key": "appliedBy.principalId", "type": "str"},
        "principal_type_applied_by_principal_type": {"key": "appliedBy.principalType", "type": "str"},
        "principal_name_applied_by_principal_name": {"key": "appliedBy.principalName", "type": "str"},
        "user_principal_name_applied_by_user_principal_name": {"key": "appliedBy.userPrincipalName", "type": "str"},
        "principal_id_reviewed_by_principal_id": {"key": "reviewedBy.principalId", "type": "str"},
        "principal_type_reviewed_by_principal_type": {"key": "reviewedBy.principalType", "type": "str"},
        "principal_name_reviewed_by_principal_name": {"key": "reviewedBy.principalName", "type": "str"},
        "user_principal_name_reviewed_by_user_principal_name": {"key": "reviewedBy.userPrincipalName", "type": "str"},
        "type_resource_type": {"key": "resource.type", "type": "str"},
        "id_resource_id": {"key": "resource.id", "type": "str"},
        "display_name_resource_display_name": {"key": "resource.displayName", "type": "str"},
        "type_principal_type": {"key": "principal.type", "type": "str"},
        "id_principal_id": {"key": "principal.id", "type": "str"},
        "display_name_principal_display_name": {"key": "principal.displayName", "type": "str"},
    }

    def __init__(
        self,
        *,
        decision: Optional[Union[str, "_models.AccessReviewResult"]] = None,
        justification: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword decision: The decision on the approval step. This value is initially set to
         NotReviewed. Approvers can take action of Approve/Deny. Known values are: "Approve", "Deny",
         "NotReviewed", "DontKnow", and "NotNotified".
        :paramtype decision: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewResult
        :keyword justification: Justification provided by approvers for their action.
        :paramtype justification: str
        """
        super().__init__(**kwargs)
        self.recommendation = None
        self.decision = decision
        self.justification = justification
        self.reviewed_date_time = None
        self.apply_result = None
        self.applied_date_time = None
        self.principal_id_applied_by_principal_id = None
        self.principal_type_applied_by_principal_type = None
        self.principal_name_applied_by_principal_name = None
        self.user_principal_name_applied_by_user_principal_name = None
        self.principal_id_reviewed_by_principal_id = None
        self.principal_type_reviewed_by_principal_type = None
        self.principal_name_reviewed_by_principal_name = None
        self.user_principal_name_reviewed_by_user_principal_name = None
        self.type_resource_type: Optional[str] = None
        self.id_resource_id = None
        self.display_name_resource_display_name = None
        self.type_principal_type: Optional[str] = None
        self.id_principal_id = None
        self.display_name_principal_display_name = None


class AccessReviewDecisionResource(_serialization.Model):
    """Target of the decision.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar type: The type of resource. Required. "azureRole"
    :vartype type: str or ~azure.mgmt.authorization.v2021_03_01_preview.models.DecisionResourceType
    :ivar id: The id of resource associated with a decision record.
    :vartype id: str
    :ivar display_name: The display name of resource associated with a decision record.
    :vartype display_name: str
    """

    _validation = {
        "type": {"required": True},
        "id": {"readonly": True},
        "display_name": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type: Optional[str] = None
        self.id = None
        self.display_name = None


class AccessReviewDecisionServicePrincipalIdentity(AccessReviewDecisionIdentity):
    """Service Principal Decision Target.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar type: The type of decision target : User/ServicePrincipal. Required. Known values are:
     "user" and "servicePrincipal".
    :vartype type: str or ~azure.mgmt.authorization.v2021_03_01_preview.models.DecisionTargetType
    :ivar id: The id of principal whose access was reviewed.
    :vartype id: str
    :ivar display_name: The display name of the user whose access was reviewed.
    :vartype display_name: str
    :ivar app_id: The appId for the service principal entity being reviewed.
    :vartype app_id: str
    """

    _validation = {
        "type": {"required": True},
        "id": {"readonly": True},
        "display_name": {"readonly": True},
        "app_id": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "app_id": {"key": "appId", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type: str = "servicePrincipal"
        self.app_id = None


class AccessReviewDecisionUserIdentity(AccessReviewDecisionIdentity):
    """User Decision Target.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar type: The type of decision target : User/ServicePrincipal. Required. Known values are:
     "user" and "servicePrincipal".
    :vartype type: str or ~azure.mgmt.authorization.v2021_03_01_preview.models.DecisionTargetType
    :ivar id: The id of principal whose access was reviewed.
    :vartype id: str
    :ivar display_name: The display name of the user whose access was reviewed.
    :vartype display_name: str
    :ivar user_principal_name: The user principal name of the user whose access was reviewed.
    :vartype user_principal_name: str
    """

    _validation = {
        "type": {"required": True},
        "id": {"readonly": True},
        "display_name": {"readonly": True},
        "user_principal_name": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "user_principal_name": {"key": "userPrincipalName", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type: str = "user"
        self.user_principal_name = None


class AccessReviewDefaultSettings(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Access Review Default Settings.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The access review default settings id. This is only going to be default.
    :vartype id: str
    :ivar name: The access review default settings name. This is always going to be Access Review
     Default Settings.
    :vartype name: str
    :ivar type: The resource type.
    :vartype type: str
    :ivar mail_notifications_enabled: Flag to indicate whether sending mails to reviewers and the
     review creator is enabled.
    :vartype mail_notifications_enabled: bool
    :ivar reminder_notifications_enabled: Flag to indicate whether sending reminder emails to
     reviewers are enabled.
    :vartype reminder_notifications_enabled: bool
    :ivar default_decision_enabled: Flag to indicate whether reviewers are required to provide a
     justification when reviewing access.
    :vartype default_decision_enabled: bool
    :ivar justification_required_on_approval: Flag to indicate whether the reviewer is required to
     pass justification when recording a decision.
    :vartype justification_required_on_approval: bool
    :ivar default_decision: This specifies the behavior for the autoReview feature when an access
     review completes. Known values are: "Approve", "Deny", and "Recommendation".
    :vartype default_decision: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.DefaultDecisionType
    :ivar auto_apply_decisions_enabled: Flag to indicate whether auto-apply capability, to
     automatically change the target object access resource, is enabled. If not enabled, a user
     must, after the review completes, apply the access review.
    :vartype auto_apply_decisions_enabled: bool
    :ivar recommendations_enabled: Flag to indicate whether showing recommendations to reviewers is
     enabled.
    :vartype recommendations_enabled: bool
    :ivar instance_duration_in_days: The duration in days for an instance.
    :vartype instance_duration_in_days: int
    :ivar type_properties_recurrence_range_type: The recurrence range type. The possible values
     are: endDate, noEnd, numbered. Known values are: "endDate", "noEnd", and "numbered".
    :vartype type_properties_recurrence_range_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrenceRangeType
    :ivar number_of_occurrences: The number of times to repeat the access review. Required and must
     be positive if type is numbered.
    :vartype number_of_occurrences: int
    :ivar start_date: The DateTime when the review is scheduled to be start. This could be a date
     in the future. Required on create.
    :vartype start_date: ~datetime.datetime
    :ivar end_date: The DateTime when the review is scheduled to end. Required if type is endDate.
    :vartype end_date: ~datetime.datetime
    :ivar type_properties_recurrence_pattern_type: The recurrence type : weekly, monthly, etc.
     Known values are: "weekly" and "absoluteMonthly".
    :vartype type_properties_recurrence_pattern_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrencePatternType
    :ivar interval: The interval for recurrence. For a quarterly review, the interval is 3 for type
     : absoluteMonthly.
    :vartype interval: int
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "mail_notifications_enabled": {"key": "properties.mailNotificationsEnabled", "type": "bool"},
        "reminder_notifications_enabled": {"key": "properties.reminderNotificationsEnabled", "type": "bool"},
        "default_decision_enabled": {"key": "properties.defaultDecisionEnabled", "type": "bool"},
        "justification_required_on_approval": {"key": "properties.justificationRequiredOnApproval", "type": "bool"},
        "default_decision": {"key": "properties.defaultDecision", "type": "str"},
        "auto_apply_decisions_enabled": {"key": "properties.autoApplyDecisionsEnabled", "type": "bool"},
        "recommendations_enabled": {"key": "properties.recommendationsEnabled", "type": "bool"},
        "instance_duration_in_days": {"key": "properties.instanceDurationInDays", "type": "int"},
        "type_properties_recurrence_range_type": {"key": "properties.recurrence.range.type", "type": "str"},
        "number_of_occurrences": {"key": "properties.recurrence.range.numberOfOccurrences", "type": "int"},
        "start_date": {"key": "properties.recurrence.range.startDate", "type": "iso-8601"},
        "end_date": {"key": "properties.recurrence.range.endDate", "type": "iso-8601"},
        "type_properties_recurrence_pattern_type": {"key": "properties.recurrence.pattern.type", "type": "str"},
        "interval": {"key": "properties.recurrence.pattern.interval", "type": "int"},
    }

    def __init__(
        self,
        *,
        mail_notifications_enabled: Optional[bool] = None,
        reminder_notifications_enabled: Optional[bool] = None,
        default_decision_enabled: Optional[bool] = None,
        justification_required_on_approval: Optional[bool] = None,
        default_decision: Optional[Union[str, "_models.DefaultDecisionType"]] = None,
        auto_apply_decisions_enabled: Optional[bool] = None,
        recommendations_enabled: Optional[bool] = None,
        instance_duration_in_days: Optional[int] = None,
        type_properties_recurrence_range_type: Optional[Union[str, "_models.AccessReviewRecurrenceRangeType"]] = None,
        number_of_occurrences: Optional[int] = None,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        type_properties_recurrence_pattern_type: Optional[
            Union[str, "_models.AccessReviewRecurrencePatternType"]
        ] = None,
        interval: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword mail_notifications_enabled: Flag to indicate whether sending mails to reviewers and
         the review creator is enabled.
        :paramtype mail_notifications_enabled: bool
        :keyword reminder_notifications_enabled: Flag to indicate whether sending reminder emails to
         reviewers are enabled.
        :paramtype reminder_notifications_enabled: bool
        :keyword default_decision_enabled: Flag to indicate whether reviewers are required to provide a
         justification when reviewing access.
        :paramtype default_decision_enabled: bool
        :keyword justification_required_on_approval: Flag to indicate whether the reviewer is required
         to pass justification when recording a decision.
        :paramtype justification_required_on_approval: bool
        :keyword default_decision: This specifies the behavior for the autoReview feature when an
         access review completes. Known values are: "Approve", "Deny", and "Recommendation".
        :paramtype default_decision: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.DefaultDecisionType
        :keyword auto_apply_decisions_enabled: Flag to indicate whether auto-apply capability, to
         automatically change the target object access resource, is enabled. If not enabled, a user
         must, after the review completes, apply the access review.
        :paramtype auto_apply_decisions_enabled: bool
        :keyword recommendations_enabled: Flag to indicate whether showing recommendations to reviewers
         is enabled.
        :paramtype recommendations_enabled: bool
        :keyword instance_duration_in_days: The duration in days for an instance.
        :paramtype instance_duration_in_days: int
        :keyword type_properties_recurrence_range_type: The recurrence range type. The possible values
         are: endDate, noEnd, numbered. Known values are: "endDate", "noEnd", and "numbered".
        :paramtype type_properties_recurrence_range_type: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrenceRangeType
        :keyword number_of_occurrences: The number of times to repeat the access review. Required and
         must be positive if type is numbered.
        :paramtype number_of_occurrences: int
        :keyword start_date: The DateTime when the review is scheduled to be start. This could be a
         date in the future. Required on create.
        :paramtype start_date: ~datetime.datetime
        :keyword end_date: The DateTime when the review is scheduled to end. Required if type is
         endDate.
        :paramtype end_date: ~datetime.datetime
        :keyword type_properties_recurrence_pattern_type: The recurrence type : weekly, monthly, etc.
         Known values are: "weekly" and "absoluteMonthly".
        :paramtype type_properties_recurrence_pattern_type: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrencePatternType
        :keyword interval: The interval for recurrence. For a quarterly review, the interval is 3 for
         type : absoluteMonthly.
        :paramtype interval: int
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.mail_notifications_enabled = mail_notifications_enabled
        self.reminder_notifications_enabled = reminder_notifications_enabled
        self.default_decision_enabled = default_decision_enabled
        self.justification_required_on_approval = justification_required_on_approval
        self.default_decision = default_decision
        self.auto_apply_decisions_enabled = auto_apply_decisions_enabled
        self.recommendations_enabled = recommendations_enabled
        self.instance_duration_in_days = instance_duration_in_days
        self.type_properties_recurrence_range_type = type_properties_recurrence_range_type
        self.number_of_occurrences = number_of_occurrences
        self.start_date = start_date
        self.end_date = end_date
        self.type_properties_recurrence_pattern_type = type_properties_recurrence_pattern_type
        self.interval = interval


class AccessReviewInstance(_serialization.Model):
    """Access Review Instance.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The access review instance id.
    :vartype id: str
    :ivar name: The access review instance name.
    :vartype name: str
    :ivar type: The resource type.
    :vartype type: str
    :ivar status: This read-only field specifies the status of an access review instance. Known
     values are: "NotStarted", "InProgress", "Completed", "Applied", "Initializing", "Applying",
     "Completing", "Scheduled", "AutoReviewing", "AutoReviewed", and "Starting".
    :vartype status: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewInstanceStatus
    :ivar start_date_time: The DateTime when the review instance is scheduled to be start.
    :vartype start_date_time: ~datetime.datetime
    :ivar end_date_time: The DateTime when the review instance is scheduled to end.
    :vartype end_date_time: ~datetime.datetime
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "status": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "start_date_time": {"key": "properties.startDateTime", "type": "iso-8601"},
        "end_date_time": {"key": "properties.endDateTime", "type": "iso-8601"},
    }

    def __init__(
        self,
        *,
        start_date_time: Optional[datetime.datetime] = None,
        end_date_time: Optional[datetime.datetime] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword start_date_time: The DateTime when the review instance is scheduled to be start.
        :paramtype start_date_time: ~datetime.datetime
        :keyword end_date_time: The DateTime when the review instance is scheduled to end.
        :paramtype end_date_time: ~datetime.datetime
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.status = None
        self.start_date_time = start_date_time
        self.end_date_time = end_date_time


class AccessReviewInstanceListResult(_serialization.Model):
    """List of Access Review Instances.

    :ivar value: Access Review Instance list.
    :vartype value: list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewInstance]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[AccessReviewInstance]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.AccessReviewInstance"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Access Review Instance list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewInstance]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class AccessReviewReviewer(_serialization.Model):
    """Descriptor for what needs to be reviewed.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar principal_id: The id of the reviewer(user/servicePrincipal).
    :vartype principal_id: str
    :ivar principal_type: The identity type : user/servicePrincipal. Known values are: "user" and
     "servicePrincipal".
    :vartype principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewReviewerType
    """

    _validation = {
        "principal_type": {"readonly": True},
    }

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
        "principal_type": {"key": "principalType", "type": "str"},
    }

    def __init__(self, *, principal_id: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword principal_id: The id of the reviewer(user/servicePrincipal).
        :paramtype principal_id: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id
        self.principal_type = None


class AccessReviewScheduleDefinition(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Access Review Schedule Definition.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The access review schedule definition id.
    :vartype id: str
    :ivar name: The access review schedule definition unique id.
    :vartype name: str
    :ivar type: The resource type.
    :vartype type: str
    :ivar display_name: The display name for the schedule definition.
    :vartype display_name: str
    :ivar status: This read-only field specifies the status of an accessReview. Known values are:
     "NotStarted", "InProgress", "Completed", "Applied", "Initializing", "Applying", "Completing",
     "Scheduled", "AutoReviewing", "AutoReviewed", and "Starting".
    :vartype status: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScheduleDefinitionStatus
    :ivar description_for_admins: The description provided by the access review creator and visible
     to admins.
    :vartype description_for_admins: str
    :ivar description_for_reviewers: The description provided by the access review creator to be
     shown to reviewers.
    :vartype description_for_reviewers: str
    :ivar reviewers: This is the collection of reviewers.
    :vartype reviewers:
     list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewReviewer]
    :ivar backup_reviewers: This is the collection of backup reviewers.
    :vartype backup_reviewers:
     list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewReviewer]
    :ivar reviewers_type: This field specifies the type of reviewers for a review. Usually for a
     review, reviewers are explicitly assigned. However, in some cases, the reviewers may not be
     assigned and instead be chosen dynamically. For example managers review or self review. Known
     values are: "Assigned", "Self", and "Managers".
    :vartype reviewers_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScheduleDefinitionReviewersType
    :ivar instances: This is the collection of instances returned when one does an expand on it.
    :vartype instances:
     list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewInstance]
    :ivar resource_id: ResourceId in which this review is getting created.
    :vartype resource_id: str
    :ivar role_definition_id: This is used to indicate the role being reviewed.
    :vartype role_definition_id: str
    :ivar principal_type_properties_scope_principal_type: The identity type user/servicePrincipal
     to review. Known values are: "user", "guestUser", and "servicePrincipal".
    :vartype principal_type_properties_scope_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScopePrincipalType
    :ivar assignment_state: The role assignment state eligible/active to review. Known values are:
     "eligible" and "active".
    :vartype assignment_state: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScopeAssignmentState
    :ivar inactive_duration: Duration users are inactive for. The value should be in ISO  8601
     format (http://en.wikipedia.org/wiki/ISO_8601#Durations).This code can be used to convert
     TimeSpan to a valid interval string: XmlConvert.ToString(new TimeSpan(hours, minutes,
     seconds)).
    :vartype inactive_duration: ~datetime.timedelta
    :ivar mail_notifications_enabled: Flag to indicate whether sending mails to reviewers and the
     review creator is enabled.
    :vartype mail_notifications_enabled: bool
    :ivar reminder_notifications_enabled: Flag to indicate whether sending reminder emails to
     reviewers are enabled.
    :vartype reminder_notifications_enabled: bool
    :ivar default_decision_enabled: Flag to indicate whether reviewers are required to provide a
     justification when reviewing access.
    :vartype default_decision_enabled: bool
    :ivar justification_required_on_approval: Flag to indicate whether the reviewer is required to
     pass justification when recording a decision.
    :vartype justification_required_on_approval: bool
    :ivar default_decision: This specifies the behavior for the autoReview feature when an access
     review completes. Known values are: "Approve", "Deny", and "Recommendation".
    :vartype default_decision: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.DefaultDecisionType
    :ivar auto_apply_decisions_enabled: Flag to indicate whether auto-apply capability, to
     automatically change the target object access resource, is enabled. If not enabled, a user
     must, after the review completes, apply the access review.
    :vartype auto_apply_decisions_enabled: bool
    :ivar recommendations_enabled: Flag to indicate whether showing recommendations to reviewers is
     enabled.
    :vartype recommendations_enabled: bool
    :ivar instance_duration_in_days: The duration in days for an instance.
    :vartype instance_duration_in_days: int
    :ivar type_properties_settings_recurrence_range_type: The recurrence range type. The possible
     values are: endDate, noEnd, numbered. Known values are: "endDate", "noEnd", and "numbered".
    :vartype type_properties_settings_recurrence_range_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrenceRangeType
    :ivar number_of_occurrences: The number of times to repeat the access review. Required and must
     be positive if type is numbered.
    :vartype number_of_occurrences: int
    :ivar start_date: The DateTime when the review is scheduled to be start. This could be a date
     in the future. Required on create.
    :vartype start_date: ~datetime.datetime
    :ivar end_date: The DateTime when the review is scheduled to end. Required if type is endDate.
    :vartype end_date: ~datetime.datetime
    :ivar type_properties_settings_recurrence_pattern_type: The recurrence type : weekly, monthly,
     etc. Known values are: "weekly" and "absoluteMonthly".
    :vartype type_properties_settings_recurrence_pattern_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrencePatternType
    :ivar interval: The interval for recurrence. For a quarterly review, the interval is 3 for type
     : absoluteMonthly.
    :vartype interval: int
    :ivar principal_id: The identity id.
    :vartype principal_id: str
    :ivar principal_type_properties_created_by_principal_type: The identity type :
     user/servicePrincipal. Known values are: "user" and "servicePrincipal".
    :vartype principal_type_properties_created_by_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewActorIdentityType
    :ivar principal_name: The identity display name.
    :vartype principal_name: str
    :ivar user_principal_name: The user principal name(if valid).
    :vartype user_principal_name: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "status": {"readonly": True},
        "reviewers_type": {"readonly": True},
        "resource_id": {"readonly": True},
        "role_definition_id": {"readonly": True},
        "principal_type_properties_scope_principal_type": {"readonly": True},
        "assignment_state": {"readonly": True},
        "principal_id": {"readonly": True},
        "principal_type_properties_created_by_principal_type": {"readonly": True},
        "principal_name": {"readonly": True},
        "user_principal_name": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "display_name": {"key": "properties.displayName", "type": "str"},
        "status": {"key": "properties.status", "type": "str"},
        "description_for_admins": {"key": "properties.descriptionForAdmins", "type": "str"},
        "description_for_reviewers": {"key": "properties.descriptionForReviewers", "type": "str"},
        "reviewers": {"key": "properties.reviewers", "type": "[AccessReviewReviewer]"},
        "backup_reviewers": {"key": "properties.backupReviewers", "type": "[AccessReviewReviewer]"},
        "reviewers_type": {"key": "properties.reviewersType", "type": "str"},
        "instances": {"key": "properties.instances", "type": "[AccessReviewInstance]"},
        "resource_id": {"key": "properties.scope.resourceId", "type": "str"},
        "role_definition_id": {"key": "properties.scope.roleDefinitionId", "type": "str"},
        "principal_type_properties_scope_principal_type": {"key": "properties.scope.principalType", "type": "str"},
        "assignment_state": {"key": "properties.scope.assignmentState", "type": "str"},
        "inactive_duration": {"key": "properties.scope.inactiveDuration", "type": "duration"},
        "mail_notifications_enabled": {"key": "properties.settings.mailNotificationsEnabled", "type": "bool"},
        "reminder_notifications_enabled": {"key": "properties.settings.reminderNotificationsEnabled", "type": "bool"},
        "default_decision_enabled": {"key": "properties.settings.defaultDecisionEnabled", "type": "bool"},
        "justification_required_on_approval": {
            "key": "properties.settings.justificationRequiredOnApproval",
            "type": "bool",
        },
        "default_decision": {"key": "properties.settings.defaultDecision", "type": "str"},
        "auto_apply_decisions_enabled": {"key": "properties.settings.autoApplyDecisionsEnabled", "type": "bool"},
        "recommendations_enabled": {"key": "properties.settings.recommendationsEnabled", "type": "bool"},
        "instance_duration_in_days": {"key": "properties.settings.instanceDurationInDays", "type": "int"},
        "type_properties_settings_recurrence_range_type": {
            "key": "properties.settings.recurrence.range.type",
            "type": "str",
        },
        "number_of_occurrences": {"key": "properties.settings.recurrence.range.numberOfOccurrences", "type": "int"},
        "start_date": {"key": "properties.settings.recurrence.range.startDate", "type": "iso-8601"},
        "end_date": {"key": "properties.settings.recurrence.range.endDate", "type": "iso-8601"},
        "type_properties_settings_recurrence_pattern_type": {
            "key": "properties.settings.recurrence.pattern.type",
            "type": "str",
        },
        "interval": {"key": "properties.settings.recurrence.pattern.interval", "type": "int"},
        "principal_id": {"key": "properties.createdBy.principalId", "type": "str"},
        "principal_type_properties_created_by_principal_type": {
            "key": "properties.createdBy.principalType",
            "type": "str",
        },
        "principal_name": {"key": "properties.createdBy.principalName", "type": "str"},
        "user_principal_name": {"key": "properties.createdBy.userPrincipalName", "type": "str"},
    }

    def __init__(  # pylint: disable=too-many-locals
        self,
        *,
        display_name: Optional[str] = None,
        description_for_admins: Optional[str] = None,
        description_for_reviewers: Optional[str] = None,
        reviewers: Optional[List["_models.AccessReviewReviewer"]] = None,
        backup_reviewers: Optional[List["_models.AccessReviewReviewer"]] = None,
        instances: Optional[List["_models.AccessReviewInstance"]] = None,
        inactive_duration: Optional[datetime.timedelta] = None,
        mail_notifications_enabled: Optional[bool] = None,
        reminder_notifications_enabled: Optional[bool] = None,
        default_decision_enabled: Optional[bool] = None,
        justification_required_on_approval: Optional[bool] = None,
        default_decision: Optional[Union[str, "_models.DefaultDecisionType"]] = None,
        auto_apply_decisions_enabled: Optional[bool] = None,
        recommendations_enabled: Optional[bool] = None,
        instance_duration_in_days: Optional[int] = None,
        type_properties_settings_recurrence_range_type: Optional[
            Union[str, "_models.AccessReviewRecurrenceRangeType"]
        ] = None,
        number_of_occurrences: Optional[int] = None,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        type_properties_settings_recurrence_pattern_type: Optional[
            Union[str, "_models.AccessReviewRecurrencePatternType"]
        ] = None,
        interval: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword display_name: The display name for the schedule definition.
        :paramtype display_name: str
        :keyword description_for_admins: The description provided by the access review creator and
         visible to admins.
        :paramtype description_for_admins: str
        :keyword description_for_reviewers: The description provided by the access review creator to be
         shown to reviewers.
        :paramtype description_for_reviewers: str
        :keyword reviewers: This is the collection of reviewers.
        :paramtype reviewers:
         list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewReviewer]
        :keyword backup_reviewers: This is the collection of backup reviewers.
        :paramtype backup_reviewers:
         list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewReviewer]
        :keyword instances: This is the collection of instances returned when one does an expand on it.
        :paramtype instances:
         list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewInstance]
        :keyword inactive_duration: Duration users are inactive for. The value should be in ISO  8601
         format (http://en.wikipedia.org/wiki/ISO_8601#Durations).This code can be used to convert
         TimeSpan to a valid interval string: XmlConvert.ToString(new TimeSpan(hours, minutes,
         seconds)).
        :paramtype inactive_duration: ~datetime.timedelta
        :keyword mail_notifications_enabled: Flag to indicate whether sending mails to reviewers and
         the review creator is enabled.
        :paramtype mail_notifications_enabled: bool
        :keyword reminder_notifications_enabled: Flag to indicate whether sending reminder emails to
         reviewers are enabled.
        :paramtype reminder_notifications_enabled: bool
        :keyword default_decision_enabled: Flag to indicate whether reviewers are required to provide a
         justification when reviewing access.
        :paramtype default_decision_enabled: bool
        :keyword justification_required_on_approval: Flag to indicate whether the reviewer is required
         to pass justification when recording a decision.
        :paramtype justification_required_on_approval: bool
        :keyword default_decision: This specifies the behavior for the autoReview feature when an
         access review completes. Known values are: "Approve", "Deny", and "Recommendation".
        :paramtype default_decision: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.DefaultDecisionType
        :keyword auto_apply_decisions_enabled: Flag to indicate whether auto-apply capability, to
         automatically change the target object access resource, is enabled. If not enabled, a user
         must, after the review completes, apply the access review.
        :paramtype auto_apply_decisions_enabled: bool
        :keyword recommendations_enabled: Flag to indicate whether showing recommendations to reviewers
         is enabled.
        :paramtype recommendations_enabled: bool
        :keyword instance_duration_in_days: The duration in days for an instance.
        :paramtype instance_duration_in_days: int
        :keyword type_properties_settings_recurrence_range_type: The recurrence range type. The
         possible values are: endDate, noEnd, numbered. Known values are: "endDate", "noEnd", and
         "numbered".
        :paramtype type_properties_settings_recurrence_range_type: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrenceRangeType
        :keyword number_of_occurrences: The number of times to repeat the access review. Required and
         must be positive if type is numbered.
        :paramtype number_of_occurrences: int
        :keyword start_date: The DateTime when the review is scheduled to be start. This could be a
         date in the future. Required on create.
        :paramtype start_date: ~datetime.datetime
        :keyword end_date: The DateTime when the review is scheduled to end. Required if type is
         endDate.
        :paramtype end_date: ~datetime.datetime
        :keyword type_properties_settings_recurrence_pattern_type: The recurrence type : weekly,
         monthly, etc. Known values are: "weekly" and "absoluteMonthly".
        :paramtype type_properties_settings_recurrence_pattern_type: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrencePatternType
        :keyword interval: The interval for recurrence. For a quarterly review, the interval is 3 for
         type : absoluteMonthly.
        :paramtype interval: int
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.display_name = display_name
        self.status = None
        self.description_for_admins = description_for_admins
        self.description_for_reviewers = description_for_reviewers
        self.reviewers = reviewers
        self.backup_reviewers = backup_reviewers
        self.reviewers_type = None
        self.instances = instances
        self.resource_id = None
        self.role_definition_id = None
        self.principal_type_properties_scope_principal_type = None
        self.assignment_state = None
        self.inactive_duration = inactive_duration
        self.mail_notifications_enabled = mail_notifications_enabled
        self.reminder_notifications_enabled = reminder_notifications_enabled
        self.default_decision_enabled = default_decision_enabled
        self.justification_required_on_approval = justification_required_on_approval
        self.default_decision = default_decision
        self.auto_apply_decisions_enabled = auto_apply_decisions_enabled
        self.recommendations_enabled = recommendations_enabled
        self.instance_duration_in_days = instance_duration_in_days
        self.type_properties_settings_recurrence_range_type = type_properties_settings_recurrence_range_type
        self.number_of_occurrences = number_of_occurrences
        self.start_date = start_date
        self.end_date = end_date
        self.type_properties_settings_recurrence_pattern_type = type_properties_settings_recurrence_pattern_type
        self.interval = interval
        self.principal_id = None
        self.principal_type_properties_created_by_principal_type = None
        self.principal_name = None
        self.user_principal_name = None


class AccessReviewScheduleDefinitionListResult(_serialization.Model):
    """List of Access Review Schedule Definitions.

    :ivar value: Access Review Schedule Definition list.
    :vartype value:
     list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScheduleDefinition]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[AccessReviewScheduleDefinition]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.AccessReviewScheduleDefinition"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: Access Review Schedule Definition list.
        :paramtype value:
         list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScheduleDefinition]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class AccessReviewScheduleDefinitionProperties(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Access Review.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar display_name: The display name for the schedule definition.
    :vartype display_name: str
    :ivar status: This read-only field specifies the status of an accessReview. Known values are:
     "NotStarted", "InProgress", "Completed", "Applied", "Initializing", "Applying", "Completing",
     "Scheduled", "AutoReviewing", "AutoReviewed", and "Starting".
    :vartype status: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScheduleDefinitionStatus
    :ivar description_for_admins: The description provided by the access review creator and visible
     to admins.
    :vartype description_for_admins: str
    :ivar description_for_reviewers: The description provided by the access review creator to be
     shown to reviewers.
    :vartype description_for_reviewers: str
    :ivar reviewers: This is the collection of reviewers.
    :vartype reviewers:
     list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewReviewer]
    :ivar backup_reviewers: This is the collection of backup reviewers.
    :vartype backup_reviewers:
     list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewReviewer]
    :ivar reviewers_type: This field specifies the type of reviewers for a review. Usually for a
     review, reviewers are explicitly assigned. However, in some cases, the reviewers may not be
     assigned and instead be chosen dynamically. For example managers review or self review. Known
     values are: "Assigned", "Self", and "Managers".
    :vartype reviewers_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScheduleDefinitionReviewersType
    :ivar instances: This is the collection of instances returned when one does an expand on it.
    :vartype instances:
     list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewInstance]
    :ivar resource_id: ResourceId in which this review is getting created.
    :vartype resource_id: str
    :ivar role_definition_id: This is used to indicate the role being reviewed.
    :vartype role_definition_id: str
    :ivar principal_type_scope_principal_type: The identity type user/servicePrincipal to review.
     Known values are: "user", "guestUser", and "servicePrincipal".
    :vartype principal_type_scope_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScopePrincipalType
    :ivar assignment_state: The role assignment state eligible/active to review. Known values are:
     "eligible" and "active".
    :vartype assignment_state: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewScopeAssignmentState
    :ivar inactive_duration: Duration users are inactive for. The value should be in ISO  8601
     format (http://en.wikipedia.org/wiki/ISO_8601#Durations).This code can be used to convert
     TimeSpan to a valid interval string: XmlConvert.ToString(new TimeSpan(hours, minutes,
     seconds)).
    :vartype inactive_duration: ~datetime.timedelta
    :ivar mail_notifications_enabled: Flag to indicate whether sending mails to reviewers and the
     review creator is enabled.
    :vartype mail_notifications_enabled: bool
    :ivar reminder_notifications_enabled: Flag to indicate whether sending reminder emails to
     reviewers are enabled.
    :vartype reminder_notifications_enabled: bool
    :ivar default_decision_enabled: Flag to indicate whether reviewers are required to provide a
     justification when reviewing access.
    :vartype default_decision_enabled: bool
    :ivar justification_required_on_approval: Flag to indicate whether the reviewer is required to
     pass justification when recording a decision.
    :vartype justification_required_on_approval: bool
    :ivar default_decision: This specifies the behavior for the autoReview feature when an access
     review completes. Known values are: "Approve", "Deny", and "Recommendation".
    :vartype default_decision: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.DefaultDecisionType
    :ivar auto_apply_decisions_enabled: Flag to indicate whether auto-apply capability, to
     automatically change the target object access resource, is enabled. If not enabled, a user
     must, after the review completes, apply the access review.
    :vartype auto_apply_decisions_enabled: bool
    :ivar recommendations_enabled: Flag to indicate whether showing recommendations to reviewers is
     enabled.
    :vartype recommendations_enabled: bool
    :ivar instance_duration_in_days: The duration in days for an instance.
    :vartype instance_duration_in_days: int
    :ivar type_settings_recurrence_range_type: The recurrence range type. The possible values are:
     endDate, noEnd, numbered. Known values are: "endDate", "noEnd", and "numbered".
    :vartype type_settings_recurrence_range_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrenceRangeType
    :ivar number_of_occurrences: The number of times to repeat the access review. Required and must
     be positive if type is numbered.
    :vartype number_of_occurrences: int
    :ivar start_date: The DateTime when the review is scheduled to be start. This could be a date
     in the future. Required on create.
    :vartype start_date: ~datetime.datetime
    :ivar end_date: The DateTime when the review is scheduled to end. Required if type is endDate.
    :vartype end_date: ~datetime.datetime
    :ivar type_settings_recurrence_pattern_type: The recurrence type : weekly, monthly, etc. Known
     values are: "weekly" and "absoluteMonthly".
    :vartype type_settings_recurrence_pattern_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrencePatternType
    :ivar interval: The interval for recurrence. For a quarterly review, the interval is 3 for type
     : absoluteMonthly.
    :vartype interval: int
    :ivar principal_id: The identity id.
    :vartype principal_id: str
    :ivar principal_type_created_by_principal_type: The identity type : user/servicePrincipal.
     Known values are: "user" and "servicePrincipal".
    :vartype principal_type_created_by_principal_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewActorIdentityType
    :ivar principal_name: The identity display name.
    :vartype principal_name: str
    :ivar user_principal_name: The user principal name(if valid).
    :vartype user_principal_name: str
    """

    _validation = {
        "status": {"readonly": True},
        "reviewers_type": {"readonly": True},
        "resource_id": {"readonly": True},
        "role_definition_id": {"readonly": True},
        "principal_type_scope_principal_type": {"readonly": True},
        "assignment_state": {"readonly": True},
        "principal_id": {"readonly": True},
        "principal_type_created_by_principal_type": {"readonly": True},
        "principal_name": {"readonly": True},
        "user_principal_name": {"readonly": True},
    }

    _attribute_map = {
        "display_name": {"key": "displayName", "type": "str"},
        "status": {"key": "status", "type": "str"},
        "description_for_admins": {"key": "descriptionForAdmins", "type": "str"},
        "description_for_reviewers": {"key": "descriptionForReviewers", "type": "str"},
        "reviewers": {"key": "reviewers", "type": "[AccessReviewReviewer]"},
        "backup_reviewers": {"key": "backupReviewers", "type": "[AccessReviewReviewer]"},
        "reviewers_type": {"key": "reviewersType", "type": "str"},
        "instances": {"key": "instances", "type": "[AccessReviewInstance]"},
        "resource_id": {"key": "scope.resourceId", "type": "str"},
        "role_definition_id": {"key": "scope.roleDefinitionId", "type": "str"},
        "principal_type_scope_principal_type": {"key": "scope.principalType", "type": "str"},
        "assignment_state": {"key": "scope.assignmentState", "type": "str"},
        "inactive_duration": {"key": "scope.inactiveDuration", "type": "duration"},
        "mail_notifications_enabled": {"key": "settings.mailNotificationsEnabled", "type": "bool"},
        "reminder_notifications_enabled": {"key": "settings.reminderNotificationsEnabled", "type": "bool"},
        "default_decision_enabled": {"key": "settings.defaultDecisionEnabled", "type": "bool"},
        "justification_required_on_approval": {"key": "settings.justificationRequiredOnApproval", "type": "bool"},
        "default_decision": {"key": "settings.defaultDecision", "type": "str"},
        "auto_apply_decisions_enabled": {"key": "settings.autoApplyDecisionsEnabled", "type": "bool"},
        "recommendations_enabled": {"key": "settings.recommendationsEnabled", "type": "bool"},
        "instance_duration_in_days": {"key": "settings.instanceDurationInDays", "type": "int"},
        "type_settings_recurrence_range_type": {"key": "settings.recurrence.range.type", "type": "str"},
        "number_of_occurrences": {"key": "settings.recurrence.range.numberOfOccurrences", "type": "int"},
        "start_date": {"key": "settings.recurrence.range.startDate", "type": "iso-8601"},
        "end_date": {"key": "settings.recurrence.range.endDate", "type": "iso-8601"},
        "type_settings_recurrence_pattern_type": {"key": "settings.recurrence.pattern.type", "type": "str"},
        "interval": {"key": "settings.recurrence.pattern.interval", "type": "int"},
        "principal_id": {"key": "createdBy.principalId", "type": "str"},
        "principal_type_created_by_principal_type": {"key": "createdBy.principalType", "type": "str"},
        "principal_name": {"key": "createdBy.principalName", "type": "str"},
        "user_principal_name": {"key": "createdBy.userPrincipalName", "type": "str"},
    }

    def __init__(  # pylint: disable=too-many-locals
        self,
        *,
        display_name: Optional[str] = None,
        description_for_admins: Optional[str] = None,
        description_for_reviewers: Optional[str] = None,
        reviewers: Optional[List["_models.AccessReviewReviewer"]] = None,
        backup_reviewers: Optional[List["_models.AccessReviewReviewer"]] = None,
        instances: Optional[List["_models.AccessReviewInstance"]] = None,
        inactive_duration: Optional[datetime.timedelta] = None,
        mail_notifications_enabled: Optional[bool] = None,
        reminder_notifications_enabled: Optional[bool] = None,
        default_decision_enabled: Optional[bool] = None,
        justification_required_on_approval: Optional[bool] = None,
        default_decision: Optional[Union[str, "_models.DefaultDecisionType"]] = None,
        auto_apply_decisions_enabled: Optional[bool] = None,
        recommendations_enabled: Optional[bool] = None,
        instance_duration_in_days: Optional[int] = None,
        type_settings_recurrence_range_type: Optional[Union[str, "_models.AccessReviewRecurrenceRangeType"]] = None,
        number_of_occurrences: Optional[int] = None,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        type_settings_recurrence_pattern_type: Optional[Union[str, "_models.AccessReviewRecurrencePatternType"]] = None,
        interval: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword display_name: The display name for the schedule definition.
        :paramtype display_name: str
        :keyword description_for_admins: The description provided by the access review creator and
         visible to admins.
        :paramtype description_for_admins: str
        :keyword description_for_reviewers: The description provided by the access review creator to be
         shown to reviewers.
        :paramtype description_for_reviewers: str
        :keyword reviewers: This is the collection of reviewers.
        :paramtype reviewers:
         list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewReviewer]
        :keyword backup_reviewers: This is the collection of backup reviewers.
        :paramtype backup_reviewers:
         list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewReviewer]
        :keyword instances: This is the collection of instances returned when one does an expand on it.
        :paramtype instances:
         list[~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewInstance]
        :keyword inactive_duration: Duration users are inactive for. The value should be in ISO  8601
         format (http://en.wikipedia.org/wiki/ISO_8601#Durations).This code can be used to convert
         TimeSpan to a valid interval string: XmlConvert.ToString(new TimeSpan(hours, minutes,
         seconds)).
        :paramtype inactive_duration: ~datetime.timedelta
        :keyword mail_notifications_enabled: Flag to indicate whether sending mails to reviewers and
         the review creator is enabled.
        :paramtype mail_notifications_enabled: bool
        :keyword reminder_notifications_enabled: Flag to indicate whether sending reminder emails to
         reviewers are enabled.
        :paramtype reminder_notifications_enabled: bool
        :keyword default_decision_enabled: Flag to indicate whether reviewers are required to provide a
         justification when reviewing access.
        :paramtype default_decision_enabled: bool
        :keyword justification_required_on_approval: Flag to indicate whether the reviewer is required
         to pass justification when recording a decision.
        :paramtype justification_required_on_approval: bool
        :keyword default_decision: This specifies the behavior for the autoReview feature when an
         access review completes. Known values are: "Approve", "Deny", and "Recommendation".
        :paramtype default_decision: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.DefaultDecisionType
        :keyword auto_apply_decisions_enabled: Flag to indicate whether auto-apply capability, to
         automatically change the target object access resource, is enabled. If not enabled, a user
         must, after the review completes, apply the access review.
        :paramtype auto_apply_decisions_enabled: bool
        :keyword recommendations_enabled: Flag to indicate whether showing recommendations to reviewers
         is enabled.
        :paramtype recommendations_enabled: bool
        :keyword instance_duration_in_days: The duration in days for an instance.
        :paramtype instance_duration_in_days: int
        :keyword type_settings_recurrence_range_type: The recurrence range type. The possible values
         are: endDate, noEnd, numbered. Known values are: "endDate", "noEnd", and "numbered".
        :paramtype type_settings_recurrence_range_type: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrenceRangeType
        :keyword number_of_occurrences: The number of times to repeat the access review. Required and
         must be positive if type is numbered.
        :paramtype number_of_occurrences: int
        :keyword start_date: The DateTime when the review is scheduled to be start. This could be a
         date in the future. Required on create.
        :paramtype start_date: ~datetime.datetime
        :keyword end_date: The DateTime when the review is scheduled to end. Required if type is
         endDate.
        :paramtype end_date: ~datetime.datetime
        :keyword type_settings_recurrence_pattern_type: The recurrence type : weekly, monthly, etc.
         Known values are: "weekly" and "absoluteMonthly".
        :paramtype type_settings_recurrence_pattern_type: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrencePatternType
        :keyword interval: The interval for recurrence. For a quarterly review, the interval is 3 for
         type : absoluteMonthly.
        :paramtype interval: int
        """
        super().__init__(**kwargs)
        self.display_name = display_name
        self.status = None
        self.description_for_admins = description_for_admins
        self.description_for_reviewers = description_for_reviewers
        self.reviewers = reviewers
        self.backup_reviewers = backup_reviewers
        self.reviewers_type = None
        self.instances = instances
        self.resource_id = None
        self.role_definition_id = None
        self.principal_type_scope_principal_type = None
        self.assignment_state = None
        self.inactive_duration = inactive_duration
        self.mail_notifications_enabled = mail_notifications_enabled
        self.reminder_notifications_enabled = reminder_notifications_enabled
        self.default_decision_enabled = default_decision_enabled
        self.justification_required_on_approval = justification_required_on_approval
        self.default_decision = default_decision
        self.auto_apply_decisions_enabled = auto_apply_decisions_enabled
        self.recommendations_enabled = recommendations_enabled
        self.instance_duration_in_days = instance_duration_in_days
        self.type_settings_recurrence_range_type = type_settings_recurrence_range_type
        self.number_of_occurrences = number_of_occurrences
        self.start_date = start_date
        self.end_date = end_date
        self.type_settings_recurrence_pattern_type = type_settings_recurrence_pattern_type
        self.interval = interval
        self.principal_id = None
        self.principal_type_created_by_principal_type = None
        self.principal_name = None
        self.user_principal_name = None


class AccessReviewScheduleSettings(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Settings of an Access Review.

    :ivar mail_notifications_enabled: Flag to indicate whether sending mails to reviewers and the
     review creator is enabled.
    :vartype mail_notifications_enabled: bool
    :ivar reminder_notifications_enabled: Flag to indicate whether sending reminder emails to
     reviewers are enabled.
    :vartype reminder_notifications_enabled: bool
    :ivar default_decision_enabled: Flag to indicate whether reviewers are required to provide a
     justification when reviewing access.
    :vartype default_decision_enabled: bool
    :ivar justification_required_on_approval: Flag to indicate whether the reviewer is required to
     pass justification when recording a decision.
    :vartype justification_required_on_approval: bool
    :ivar default_decision: This specifies the behavior for the autoReview feature when an access
     review completes. Known values are: "Approve", "Deny", and "Recommendation".
    :vartype default_decision: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.DefaultDecisionType
    :ivar auto_apply_decisions_enabled: Flag to indicate whether auto-apply capability, to
     automatically change the target object access resource, is enabled. If not enabled, a user
     must, after the review completes, apply the access review.
    :vartype auto_apply_decisions_enabled: bool
    :ivar recommendations_enabled: Flag to indicate whether showing recommendations to reviewers is
     enabled.
    :vartype recommendations_enabled: bool
    :ivar instance_duration_in_days: The duration in days for an instance.
    :vartype instance_duration_in_days: int
    :ivar type_recurrence_range_type: The recurrence range type. The possible values are: endDate,
     noEnd, numbered. Known values are: "endDate", "noEnd", and "numbered".
    :vartype type_recurrence_range_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrenceRangeType
    :ivar number_of_occurrences: The number of times to repeat the access review. Required and must
     be positive if type is numbered.
    :vartype number_of_occurrences: int
    :ivar start_date: The DateTime when the review is scheduled to be start. This could be a date
     in the future. Required on create.
    :vartype start_date: ~datetime.datetime
    :ivar end_date: The DateTime when the review is scheduled to end. Required if type is endDate.
    :vartype end_date: ~datetime.datetime
    :ivar type_recurrence_pattern_type: The recurrence type : weekly, monthly, etc. Known values
     are: "weekly" and "absoluteMonthly".
    :vartype type_recurrence_pattern_type: str or
     ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrencePatternType
    :ivar interval: The interval for recurrence. For a quarterly review, the interval is 3 for type
     : absoluteMonthly.
    :vartype interval: int
    """

    _attribute_map = {
        "mail_notifications_enabled": {"key": "mailNotificationsEnabled", "type": "bool"},
        "reminder_notifications_enabled": {"key": "reminderNotificationsEnabled", "type": "bool"},
        "default_decision_enabled": {"key": "defaultDecisionEnabled", "type": "bool"},
        "justification_required_on_approval": {"key": "justificationRequiredOnApproval", "type": "bool"},
        "default_decision": {"key": "defaultDecision", "type": "str"},
        "auto_apply_decisions_enabled": {"key": "autoApplyDecisionsEnabled", "type": "bool"},
        "recommendations_enabled": {"key": "recommendationsEnabled", "type": "bool"},
        "instance_duration_in_days": {"key": "instanceDurationInDays", "type": "int"},
        "type_recurrence_range_type": {"key": "recurrence.range.type", "type": "str"},
        "number_of_occurrences": {"key": "recurrence.range.numberOfOccurrences", "type": "int"},
        "start_date": {"key": "recurrence.range.startDate", "type": "iso-8601"},
        "end_date": {"key": "recurrence.range.endDate", "type": "iso-8601"},
        "type_recurrence_pattern_type": {"key": "recurrence.pattern.type", "type": "str"},
        "interval": {"key": "recurrence.pattern.interval", "type": "int"},
    }

    def __init__(
        self,
        *,
        mail_notifications_enabled: Optional[bool] = None,
        reminder_notifications_enabled: Optional[bool] = None,
        default_decision_enabled: Optional[bool] = None,
        justification_required_on_approval: Optional[bool] = None,
        default_decision: Optional[Union[str, "_models.DefaultDecisionType"]] = None,
        auto_apply_decisions_enabled: Optional[bool] = None,
        recommendations_enabled: Optional[bool] = None,
        instance_duration_in_days: Optional[int] = None,
        type_recurrence_range_type: Optional[Union[str, "_models.AccessReviewRecurrenceRangeType"]] = None,
        number_of_occurrences: Optional[int] = None,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        type_recurrence_pattern_type: Optional[Union[str, "_models.AccessReviewRecurrencePatternType"]] = None,
        interval: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword mail_notifications_enabled: Flag to indicate whether sending mails to reviewers and
         the review creator is enabled.
        :paramtype mail_notifications_enabled: bool
        :keyword reminder_notifications_enabled: Flag to indicate whether sending reminder emails to
         reviewers are enabled.
        :paramtype reminder_notifications_enabled: bool
        :keyword default_decision_enabled: Flag to indicate whether reviewers are required to provide a
         justification when reviewing access.
        :paramtype default_decision_enabled: bool
        :keyword justification_required_on_approval: Flag to indicate whether the reviewer is required
         to pass justification when recording a decision.
        :paramtype justification_required_on_approval: bool
        :keyword default_decision: This specifies the behavior for the autoReview feature when an
         access review completes. Known values are: "Approve", "Deny", and "Recommendation".
        :paramtype default_decision: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.DefaultDecisionType
        :keyword auto_apply_decisions_enabled: Flag to indicate whether auto-apply capability, to
         automatically change the target object access resource, is enabled. If not enabled, a user
         must, after the review completes, apply the access review.
        :paramtype auto_apply_decisions_enabled: bool
        :keyword recommendations_enabled: Flag to indicate whether showing recommendations to reviewers
         is enabled.
        :paramtype recommendations_enabled: bool
        :keyword instance_duration_in_days: The duration in days for an instance.
        :paramtype instance_duration_in_days: int
        :keyword type_recurrence_range_type: The recurrence range type. The possible values are:
         endDate, noEnd, numbered. Known values are: "endDate", "noEnd", and "numbered".
        :paramtype type_recurrence_range_type: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrenceRangeType
        :keyword number_of_occurrences: The number of times to repeat the access review. Required and
         must be positive if type is numbered.
        :paramtype number_of_occurrences: int
        :keyword start_date: The DateTime when the review is scheduled to be start. This could be a
         date in the future. Required on create.
        :paramtype start_date: ~datetime.datetime
        :keyword end_date: The DateTime when the review is scheduled to end. Required if type is
         endDate.
        :paramtype end_date: ~datetime.datetime
        :keyword type_recurrence_pattern_type: The recurrence type : weekly, monthly, etc. Known values
         are: "weekly" and "absoluteMonthly".
        :paramtype type_recurrence_pattern_type: str or
         ~azure.mgmt.authorization.v2021_03_01_preview.models.AccessReviewRecurrencePatternType
        :keyword interval: The interval for recurrence. For a quarterly review, the interval is 3 for
         type : absoluteMonthly.
        :paramtype interval: int
        """
        super().__init__(**kwargs)
        self.mail_notifications_enabled = mail_notifications_enabled
        self.reminder_notifications_enabled = reminder_notifications_enabled
        self.default_decision_enabled = default_decision_enabled
        self.justification_required_on_approval = justification_required_on_approval
        self.default_decision = default_decision
        self.auto_apply_decisions_enabled = auto_apply_decisions_enabled
        self.recommendations_enabled = recommendations_enabled
        self.instance_duration_in_days = instance_duration_in_days
        self.type_recurrence_range_type = type_recurrence_range_type
        self.number_of_occurrences = number_of_occurrences
        self.start_date = start_date
        self.end_date = end_date
        self.type_recurrence_pattern_type = type_recurrence_pattern_type
        self.interval = interval


class ErrorDefinition(_serialization.Model):
    """Error description and code explaining why an operation failed.

    :ivar error: Error of the list gateway status.
    :vartype error: ~azure.mgmt.authorization.v2021_03_01_preview.models.ErrorDefinitionProperties
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDefinitionProperties"},
    }

    def __init__(self, *, error: Optional["_models.ErrorDefinitionProperties"] = None, **kwargs: Any) -> None:
        """
        :keyword error: Error of the list gateway status.
        :paramtype error:
         ~azure.mgmt.authorization.v2021_03_01_preview.models.ErrorDefinitionProperties
        """
        super().__init__(**kwargs)
        self.error = error


class ErrorDefinitionProperties(_serialization.Model):
    """Error description and code explaining why an operation failed.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar message: Description of the error.
    :vartype message: str
    :ivar code: Error code of list gateway.
    :vartype code: str
    """

    _validation = {
        "message": {"readonly": True},
    }

    _attribute_map = {
        "message": {"key": "message", "type": "str"},
        "code": {"key": "code", "type": "str"},
    }

    def __init__(self, *, code: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword code: Error code of list gateway.
        :paramtype code: str
        """
        super().__init__(**kwargs)
        self.message = None
        self.code = code


class Operation(_serialization.Model):
    """The definition of a Microsoft.Authorization operation.

    :ivar name: Name of the operation.
    :vartype name: str
    :ivar is_data_action: Indicates whether the operation is a data action.
    :vartype is_data_action: bool
    :ivar display: Display of the operation.
    :vartype display: ~azure.mgmt.authorization.v2021_03_01_preview.models.OperationDisplay
    :ivar origin: Origin of the operation.
    :vartype origin: str
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "is_data_action": {"key": "isDataAction", "type": "bool"},
        "display": {"key": "display", "type": "OperationDisplay"},
        "origin": {"key": "origin", "type": "str"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        is_data_action: Optional[bool] = None,
        display: Optional["_models.OperationDisplay"] = None,
        origin: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Name of the operation.
        :paramtype name: str
        :keyword is_data_action: Indicates whether the operation is a data action.
        :paramtype is_data_action: bool
        :keyword display: Display of the operation.
        :paramtype display: ~azure.mgmt.authorization.v2021_03_01_preview.models.OperationDisplay
        :keyword origin: Origin of the operation.
        :paramtype origin: str
        """
        super().__init__(**kwargs)
        self.name = name
        self.is_data_action = is_data_action
        self.display = display
        self.origin = origin


class OperationDisplay(_serialization.Model):
    """The display information for a Microsoft.Authorization operation.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar provider: The resource provider name: Microsoft.Authorization.
    :vartype provider: str
    :ivar resource: The resource on which the operation is performed.
    :vartype resource: str
    :ivar operation: The operation that users can perform.
    :vartype operation: str
    :ivar description: The description for the operation.
    :vartype description: str
    """

    _validation = {
        "provider": {"readonly": True},
        "resource": {"readonly": True},
        "operation": {"readonly": True},
        "description": {"readonly": True},
    }

    _attribute_map = {
        "provider": {"key": "provider", "type": "str"},
        "resource": {"key": "resource", "type": "str"},
        "operation": {"key": "operation", "type": "str"},
        "description": {"key": "description", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.provider = None
        self.resource = None
        self.operation = None
        self.description = None


class OperationListResult(_serialization.Model):
    """The result of a request to list Microsoft.Authorization operations.

    :ivar value: The collection value.
    :vartype value: list[~azure.mgmt.authorization.v2021_03_01_preview.models.Operation]
    :ivar next_link: The URI that can be used to request the next set of paged results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[Operation]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.Operation"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: The collection value.
        :paramtype value: list[~azure.mgmt.authorization.v2021_03_01_preview.models.Operation]
        :keyword next_link: The URI that can be used to request the next set of paged results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link
