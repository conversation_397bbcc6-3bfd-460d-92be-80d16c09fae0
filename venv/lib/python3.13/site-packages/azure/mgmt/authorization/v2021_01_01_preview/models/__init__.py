# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._models_py3 import ErrorDefinition
from ._models_py3 import ErrorDefinitionProperties
from ._models_py3 import Operation
from ._models_py3 import OperationDisplay
from ._models_py3 import OperationListResult
from ._models_py3 import RoleAssignmentApproval
from ._models_py3 import RoleAssignmentA<PERSON>rovalListResult
from ._models_py3 import RoleAssignmentApprovalStep
from ._models_py3 import RoleAssignmentApprovalStepListResult
from ._models_py3 import RoleAssignmentApprovalStepProperties

from ._authorization_management_client_enums import RoleAssignmentApprovalActorIdentityType
from ._authorization_management_client_enums import RoleAssignmentApprovalStepReviewResult
from ._authorization_management_client_enums import RoleAssignmentApprovalStepStatus
from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "ErrorDefinition",
    "ErrorDefinitionProperties",
    "Operation",
    "OperationDisplay",
    "OperationListResult",
    "RoleAssignmentApproval",
    "RoleAssignmentApprovalListResult",
    "RoleAssignmentApprovalStep",
    "RoleAssignmentApprovalStepListResult",
    "RoleAssignmentApprovalStepProperties",
    "RoleAssignmentApprovalActorIdentityType",
    "RoleAssignmentApprovalStepReviewResult",
    "RoleAssignmentApprovalStepStatus",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
