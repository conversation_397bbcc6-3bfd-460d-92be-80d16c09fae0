# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from enum import Enum
from azure.core import CaseInsensitiveEnumMeta


class RoleAssignmentApprovalActorIdentityType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The identity type : user/servicePrincipal."""

    USER = "user"
    SERVICE_PRINCIPAL = "servicePrincipal"


class RoleAssignmentApprovalStepReviewResult(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The decision on the approval stage. This value is initially set to NotReviewed. Approvers can
    take action of Approve/Deny.
    """

    APPROVE = "Approve"
    DENY = "Deny"
    NOT_REVIEWED = "NotReviewed"


class RoleAssignmentApprovalStepStatus(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """This read-only field specifies the status of an approval."""

    NOT_STARTED = "NotStarted"
    IN_PROGRESS = "InProgress"
    COMPLETED = "Completed"
    EXPIRED = "Expired"
    INITIALIZING = "Initializing"
    ESCALATING = "Escalating"
    COMPLETING = "Completing"
    ESCALATED = "Escalated"
