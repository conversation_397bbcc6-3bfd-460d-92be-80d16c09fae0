# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

import sys
from typing import Any, List, Optional, TYPE_CHECKING, Union

from ... import _serialization

if sys.version_info >= (3, 9):
    from collections.abc import MutableMapping
else:
    from typing import MutableMapping  # type: ignore  # pylint: disable=ungrouped-imports

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models
JSON = MutableMapping[str, Any]  # pylint: disable=unsubscriptable-object


class ApprovalSettings(_serialization.Model):
    """The approval settings.

    :ivar is_approval_required: Determines whether approval is required or not.
    :vartype is_approval_required: bool
    :ivar is_approval_required_for_extension: Determines whether approval is required for
     assignment extension.
    :vartype is_approval_required_for_extension: bool
    :ivar is_requestor_justification_required: Determine whether requestor justification is
     required.
    :vartype is_requestor_justification_required: bool
    :ivar approval_mode: The type of rule. Known values are: "SingleStage", "Serial", "Parallel",
     and "NoApproval".
    :vartype approval_mode: str or ~azure.mgmt.authorization.v2022_04_01.models.ApprovalMode
    :ivar approval_stages: The approval stages of the request.
    :vartype approval_stages: list[~azure.mgmt.authorization.v2022_04_01.models.ApprovalStage]
    """

    _attribute_map = {
        "is_approval_required": {"key": "isApprovalRequired", "type": "bool"},
        "is_approval_required_for_extension": {"key": "isApprovalRequiredForExtension", "type": "bool"},
        "is_requestor_justification_required": {"key": "isRequestorJustificationRequired", "type": "bool"},
        "approval_mode": {"key": "approvalMode", "type": "str"},
        "approval_stages": {"key": "approvalStages", "type": "[ApprovalStage]"},
    }

    def __init__(
        self,
        *,
        is_approval_required: Optional[bool] = None,
        is_approval_required_for_extension: Optional[bool] = None,
        is_requestor_justification_required: Optional[bool] = None,
        approval_mode: Optional[Union[str, "_models.ApprovalMode"]] = None,
        approval_stages: Optional[List["_models.ApprovalStage"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword is_approval_required: Determines whether approval is required or not.
        :paramtype is_approval_required: bool
        :keyword is_approval_required_for_extension: Determines whether approval is required for
         assignment extension.
        :paramtype is_approval_required_for_extension: bool
        :keyword is_requestor_justification_required: Determine whether requestor justification is
         required.
        :paramtype is_requestor_justification_required: bool
        :keyword approval_mode: The type of rule. Known values are: "SingleStage", "Serial",
         "Parallel", and "NoApproval".
        :paramtype approval_mode: str or ~azure.mgmt.authorization.v2022_04_01.models.ApprovalMode
        :keyword approval_stages: The approval stages of the request.
        :paramtype approval_stages: list[~azure.mgmt.authorization.v2022_04_01.models.ApprovalStage]
        """
        super().__init__(**kwargs)
        self.is_approval_required = is_approval_required
        self.is_approval_required_for_extension = is_approval_required_for_extension
        self.is_requestor_justification_required = is_requestor_justification_required
        self.approval_mode = approval_mode
        self.approval_stages = approval_stages


class ApprovalStage(_serialization.Model):
    """The approval stage.

    :ivar approval_stage_time_out_in_days: The time in days when approval request would be timed
     out.
    :vartype approval_stage_time_out_in_days: int
    :ivar is_approver_justification_required: Determines whether approver need to provide
     justification for his decision.
    :vartype is_approver_justification_required: bool
    :ivar escalation_time_in_minutes: The time in minutes when the approval request would be
     escalated if the primary approver does not approve.
    :vartype escalation_time_in_minutes: int
    :ivar primary_approvers: The primary approver of the request.
    :vartype primary_approvers: list[~azure.mgmt.authorization.v2022_04_01.models.UserSet]
    :ivar is_escalation_enabled: The value determine whether escalation feature is enabled.
    :vartype is_escalation_enabled: bool
    :ivar escalation_approvers: The escalation approver of the request.
    :vartype escalation_approvers: list[~azure.mgmt.authorization.v2022_04_01.models.UserSet]
    """

    _attribute_map = {
        "approval_stage_time_out_in_days": {"key": "approvalStageTimeOutInDays", "type": "int"},
        "is_approver_justification_required": {"key": "isApproverJustificationRequired", "type": "bool"},
        "escalation_time_in_minutes": {"key": "escalationTimeInMinutes", "type": "int"},
        "primary_approvers": {"key": "primaryApprovers", "type": "[UserSet]"},
        "is_escalation_enabled": {"key": "isEscalationEnabled", "type": "bool"},
        "escalation_approvers": {"key": "escalationApprovers", "type": "[UserSet]"},
    }

    def __init__(
        self,
        *,
        approval_stage_time_out_in_days: Optional[int] = None,
        is_approver_justification_required: Optional[bool] = None,
        escalation_time_in_minutes: Optional[int] = None,
        primary_approvers: Optional[List["_models.UserSet"]] = None,
        is_escalation_enabled: Optional[bool] = None,
        escalation_approvers: Optional[List["_models.UserSet"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword approval_stage_time_out_in_days: The time in days when approval request would be timed
         out.
        :paramtype approval_stage_time_out_in_days: int
        :keyword is_approver_justification_required: Determines whether approver need to provide
         justification for his decision.
        :paramtype is_approver_justification_required: bool
        :keyword escalation_time_in_minutes: The time in minutes when the approval request would be
         escalated if the primary approver does not approve.
        :paramtype escalation_time_in_minutes: int
        :keyword primary_approvers: The primary approver of the request.
        :paramtype primary_approvers: list[~azure.mgmt.authorization.v2022_04_01.models.UserSet]
        :keyword is_escalation_enabled: The value determine whether escalation feature is enabled.
        :paramtype is_escalation_enabled: bool
        :keyword escalation_approvers: The escalation approver of the request.
        :paramtype escalation_approvers: list[~azure.mgmt.authorization.v2022_04_01.models.UserSet]
        """
        super().__init__(**kwargs)
        self.approval_stage_time_out_in_days = approval_stage_time_out_in_days
        self.is_approver_justification_required = is_approver_justification_required
        self.escalation_time_in_minutes = escalation_time_in_minutes
        self.primary_approvers = primary_approvers
        self.is_escalation_enabled = is_escalation_enabled
        self.escalation_approvers = escalation_approvers


class DenyAssignment(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Deny Assignment.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The deny assignment ID.
    :vartype id: str
    :ivar name: The deny assignment name.
    :vartype name: str
    :ivar type: The deny assignment type.
    :vartype type: str
    :ivar deny_assignment_name: The display name of the deny assignment.
    :vartype deny_assignment_name: str
    :ivar description: The description of the deny assignment.
    :vartype description: str
    :ivar permissions: An array of permissions that are denied by the deny assignment.
    :vartype permissions:
     list[~azure.mgmt.authorization.v2022_04_01.models.DenyAssignmentPermission]
    :ivar scope: The deny assignment scope.
    :vartype scope: str
    :ivar do_not_apply_to_child_scopes: Determines if the deny assignment applies to child scopes.
     Default value is false.
    :vartype do_not_apply_to_child_scopes: bool
    :ivar principals: Array of principals to which the deny assignment applies.
    :vartype principals: list[~azure.mgmt.authorization.v2022_04_01.models.Principal]
    :ivar exclude_principals: Array of principals to which the deny assignment does not apply.
    :vartype exclude_principals: list[~azure.mgmt.authorization.v2022_04_01.models.Principal]
    :ivar is_system_protected: Specifies whether this deny assignment was created by Azure and
     cannot be edited or deleted.
    :vartype is_system_protected: bool
    :ivar condition: The conditions on the deny assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition.
    :vartype condition_version: str
    :ivar created_on: Time it was created.
    :vartype created_on: ~datetime.datetime
    :ivar updated_on: Time it was updated.
    :vartype updated_on: ~datetime.datetime
    :ivar created_by: Id of the user who created the assignment.
    :vartype created_by: str
    :ivar updated_by: Id of the user who updated the assignment.
    :vartype updated_by: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "created_on": {"readonly": True},
        "updated_on": {"readonly": True},
        "created_by": {"readonly": True},
        "updated_by": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "deny_assignment_name": {"key": "properties.denyAssignmentName", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "permissions": {"key": "properties.permissions", "type": "[DenyAssignmentPermission]"},
        "scope": {"key": "properties.scope", "type": "str"},
        "do_not_apply_to_child_scopes": {"key": "properties.doNotApplyToChildScopes", "type": "bool"},
        "principals": {"key": "properties.principals", "type": "[Principal]"},
        "exclude_principals": {"key": "properties.excludePrincipals", "type": "[Principal]"},
        "is_system_protected": {"key": "properties.isSystemProtected", "type": "bool"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "updated_on": {"key": "properties.updatedOn", "type": "iso-8601"},
        "created_by": {"key": "properties.createdBy", "type": "str"},
        "updated_by": {"key": "properties.updatedBy", "type": "str"},
    }

    def __init__(
        self,
        *,
        deny_assignment_name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[List["_models.DenyAssignmentPermission"]] = None,
        scope: Optional[str] = None,
        do_not_apply_to_child_scopes: Optional[bool] = None,
        principals: Optional[List["_models.Principal"]] = None,
        exclude_principals: Optional[List["_models.Principal"]] = None,
        is_system_protected: Optional[bool] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword deny_assignment_name: The display name of the deny assignment.
        :paramtype deny_assignment_name: str
        :keyword description: The description of the deny assignment.
        :paramtype description: str
        :keyword permissions: An array of permissions that are denied by the deny assignment.
        :paramtype permissions:
         list[~azure.mgmt.authorization.v2022_04_01.models.DenyAssignmentPermission]
        :keyword scope: The deny assignment scope.
        :paramtype scope: str
        :keyword do_not_apply_to_child_scopes: Determines if the deny assignment applies to child
         scopes. Default value is false.
        :paramtype do_not_apply_to_child_scopes: bool
        :keyword principals: Array of principals to which the deny assignment applies.
        :paramtype principals: list[~azure.mgmt.authorization.v2022_04_01.models.Principal]
        :keyword exclude_principals: Array of principals to which the deny assignment does not apply.
        :paramtype exclude_principals: list[~azure.mgmt.authorization.v2022_04_01.models.Principal]
        :keyword is_system_protected: Specifies whether this deny assignment was created by Azure and
         cannot be edited or deleted.
        :paramtype is_system_protected: bool
        :keyword condition: The conditions on the deny assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition.
        :paramtype condition_version: str
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.deny_assignment_name = deny_assignment_name
        self.description = description
        self.permissions = permissions
        self.scope = scope
        self.do_not_apply_to_child_scopes = do_not_apply_to_child_scopes
        self.principals = principals
        self.exclude_principals = exclude_principals
        self.is_system_protected = is_system_protected
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = None
        self.updated_on = None
        self.created_by = None
        self.updated_by = None


class DenyAssignmentFilter(_serialization.Model):
    """Deny Assignments filter.

    :ivar deny_assignment_name: Return deny assignment with specified name.
    :vartype deny_assignment_name: str
    :ivar principal_id: Return all deny assignments where the specified principal is listed in the
     principals list of deny assignments.
    :vartype principal_id: str
    :ivar gdpr_export_principal_id: Return all deny assignments where the specified principal is
     listed either in the principals list or exclude principals list of deny assignments.
    :vartype gdpr_export_principal_id: str
    """

    _attribute_map = {
        "deny_assignment_name": {"key": "denyAssignmentName", "type": "str"},
        "principal_id": {"key": "principalId", "type": "str"},
        "gdpr_export_principal_id": {"key": "gdprExportPrincipalId", "type": "str"},
    }

    def __init__(
        self,
        *,
        deny_assignment_name: Optional[str] = None,
        principal_id: Optional[str] = None,
        gdpr_export_principal_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword deny_assignment_name: Return deny assignment with specified name.
        :paramtype deny_assignment_name: str
        :keyword principal_id: Return all deny assignments where the specified principal is listed in
         the principals list of deny assignments.
        :paramtype principal_id: str
        :keyword gdpr_export_principal_id: Return all deny assignments where the specified principal is
         listed either in the principals list or exclude principals list of deny assignments.
        :paramtype gdpr_export_principal_id: str
        """
        super().__init__(**kwargs)
        self.deny_assignment_name = deny_assignment_name
        self.principal_id = principal_id
        self.gdpr_export_principal_id = gdpr_export_principal_id


class DenyAssignmentListResult(_serialization.Model):
    """Deny assignment list operation result.

    :ivar value: Deny assignment list.
    :vartype value: list[~azure.mgmt.authorization.v2022_04_01.models.DenyAssignment]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[DenyAssignment]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.DenyAssignment"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: Deny assignment list.
        :paramtype value: list[~azure.mgmt.authorization.v2022_04_01.models.DenyAssignment]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class DenyAssignmentPermission(_serialization.Model):
    """Deny assignment permissions.

    :ivar actions: Actions to which the deny assignment does not grant access.
    :vartype actions: list[str]
    :ivar not_actions: Actions to exclude from that the deny assignment does not grant access.
    :vartype not_actions: list[str]
    :ivar data_actions: Data actions to which the deny assignment does not grant access.
    :vartype data_actions: list[str]
    :ivar not_data_actions: Data actions to exclude from that the deny assignment does not grant
     access.
    :vartype not_data_actions: list[str]
    :ivar condition: The conditions on the Deny assignment permission. This limits the resources it
     applies to.
    :vartype condition: str
    :ivar condition_version: Version of the condition.
    :vartype condition_version: str
    """

    _attribute_map = {
        "actions": {"key": "actions", "type": "[str]"},
        "not_actions": {"key": "notActions", "type": "[str]"},
        "data_actions": {"key": "dataActions", "type": "[str]"},
        "not_data_actions": {"key": "notDataActions", "type": "[str]"},
        "condition": {"key": "condition", "type": "str"},
        "condition_version": {"key": "conditionVersion", "type": "str"},
    }

    def __init__(
        self,
        *,
        actions: Optional[List[str]] = None,
        not_actions: Optional[List[str]] = None,
        data_actions: Optional[List[str]] = None,
        not_data_actions: Optional[List[str]] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword actions: Actions to which the deny assignment does not grant access.
        :paramtype actions: list[str]
        :keyword not_actions: Actions to exclude from that the deny assignment does not grant access.
        :paramtype not_actions: list[str]
        :keyword data_actions: Data actions to which the deny assignment does not grant access.
        :paramtype data_actions: list[str]
        :keyword not_data_actions: Data actions to exclude from that the deny assignment does not grant
         access.
        :paramtype not_data_actions: list[str]
        :keyword condition: The conditions on the Deny assignment permission. This limits the resources
         it applies to.
        :paramtype condition: str
        :keyword condition_version: Version of the condition.
        :paramtype condition_version: str
        """
        super().__init__(**kwargs)
        self.actions = actions
        self.not_actions = not_actions
        self.data_actions = data_actions
        self.not_data_actions = not_data_actions
        self.condition = condition
        self.condition_version = condition_version


class ErrorAdditionalInfo(_serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: JSON
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(_serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.authorization.v2022_04_01.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info:
     list[~azure.mgmt.authorization.v2022_04_01.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(_serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed
    operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.mgmt.authorization.v2022_04_01.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["_models.ErrorDetail"] = None, **kwargs: Any) -> None:
        """
        :keyword error: The error object.
        :paramtype error: ~azure.mgmt.authorization.v2022_04_01.models.ErrorDetail
        """
        super().__init__(**kwargs)
        self.error = error


class Permission(_serialization.Model):
    """Role definition permissions.

    :ivar actions: Allowed actions.
    :vartype actions: list[str]
    :ivar not_actions: Denied actions.
    :vartype not_actions: list[str]
    :ivar data_actions: Allowed Data actions.
    :vartype data_actions: list[str]
    :ivar not_data_actions: Denied Data actions.
    :vartype not_data_actions: list[str]
    """

    _attribute_map = {
        "actions": {"key": "actions", "type": "[str]"},
        "not_actions": {"key": "notActions", "type": "[str]"},
        "data_actions": {"key": "dataActions", "type": "[str]"},
        "not_data_actions": {"key": "notDataActions", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        actions: Optional[List[str]] = None,
        not_actions: Optional[List[str]] = None,
        data_actions: Optional[List[str]] = None,
        not_data_actions: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword actions: Allowed actions.
        :paramtype actions: list[str]
        :keyword not_actions: Denied actions.
        :paramtype not_actions: list[str]
        :keyword data_actions: Allowed Data actions.
        :paramtype data_actions: list[str]
        :keyword not_data_actions: Denied Data actions.
        :paramtype not_data_actions: list[str]
        """
        super().__init__(**kwargs)
        self.actions = actions
        self.not_actions = not_actions
        self.data_actions = data_actions
        self.not_data_actions = not_data_actions


class PermissionGetResult(_serialization.Model):
    """Permissions information.

    :ivar value: An array of permissions.
    :vartype value: list[~azure.mgmt.authorization.v2022_04_01.models.Permission]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[Permission]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.Permission"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: An array of permissions.
        :paramtype value: list[~azure.mgmt.authorization.v2022_04_01.models.Permission]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class Principal(_serialization.Model):
    """The name of the entity last modified it.

    :ivar id: The id of the principal made changes.
    :vartype id: str
    :ivar display_name: The name of the principal made changes.
    :vartype display_name: str
    :ivar type: Type of principal such as user , group etc.
    :vartype type: str
    :ivar email: Email of principal.
    :vartype email: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "email": {"key": "email", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        email: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the principal made changes.
        :paramtype id: str
        :keyword display_name: The name of the principal made changes.
        :paramtype display_name: str
        :keyword type: Type of principal such as user , group etc.
        :paramtype type: str
        :keyword email: Email of principal.
        :paramtype email: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type
        self.email = email


class ProviderOperation(_serialization.Model):
    """Operation.

    :ivar name: The operation name.
    :vartype name: str
    :ivar display_name: The operation display name.
    :vartype display_name: str
    :ivar description: The operation description.
    :vartype description: str
    :ivar origin: The operation origin.
    :vartype origin: str
    :ivar properties: The operation properties.
    :vartype properties: JSON
    :ivar is_data_action: The dataAction flag to specify the operation type.
    :vartype is_data_action: bool
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "description": {"key": "description", "type": "str"},
        "origin": {"key": "origin", "type": "str"},
        "properties": {"key": "properties", "type": "object"},
        "is_data_action": {"key": "isDataAction", "type": "bool"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        display_name: Optional[str] = None,
        description: Optional[str] = None,
        origin: Optional[str] = None,
        properties: Optional[JSON] = None,
        is_data_action: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: The operation name.
        :paramtype name: str
        :keyword display_name: The operation display name.
        :paramtype display_name: str
        :keyword description: The operation description.
        :paramtype description: str
        :keyword origin: The operation origin.
        :paramtype origin: str
        :keyword properties: The operation properties.
        :paramtype properties: JSON
        :keyword is_data_action: The dataAction flag to specify the operation type.
        :paramtype is_data_action: bool
        """
        super().__init__(**kwargs)
        self.name = name
        self.display_name = display_name
        self.description = description
        self.origin = origin
        self.properties = properties
        self.is_data_action = is_data_action


class ProviderOperationsMetadata(_serialization.Model):
    """Provider Operations metadata.

    :ivar id: The provider id.
    :vartype id: str
    :ivar name: The provider name.
    :vartype name: str
    :ivar type: The provider type.
    :vartype type: str
    :ivar display_name: The provider display name.
    :vartype display_name: str
    :ivar resource_types: The provider resource types.
    :vartype resource_types: list[~azure.mgmt.authorization.v2022_04_01.models.ResourceType]
    :ivar operations: The provider operations.
    :vartype operations: list[~azure.mgmt.authorization.v2022_04_01.models.ProviderOperation]
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "resource_types": {"key": "resourceTypes", "type": "[ResourceType]"},
        "operations": {"key": "operations", "type": "[ProviderOperation]"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        name: Optional[str] = None,
        type: Optional[str] = None,
        display_name: Optional[str] = None,
        resource_types: Optional[List["_models.ResourceType"]] = None,
        operations: Optional[List["_models.ProviderOperation"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The provider id.
        :paramtype id: str
        :keyword name: The provider name.
        :paramtype name: str
        :keyword type: The provider type.
        :paramtype type: str
        :keyword display_name: The provider display name.
        :paramtype display_name: str
        :keyword resource_types: The provider resource types.
        :paramtype resource_types: list[~azure.mgmt.authorization.v2022_04_01.models.ResourceType]
        :keyword operations: The provider operations.
        :paramtype operations: list[~azure.mgmt.authorization.v2022_04_01.models.ProviderOperation]
        """
        super().__init__(**kwargs)
        self.id = id
        self.name = name
        self.type = type
        self.display_name = display_name
        self.resource_types = resource_types
        self.operations = operations


class ProviderOperationsMetadataListResult(_serialization.Model):
    """Provider operations metadata list.

    :ivar value: The list of providers.
    :vartype value: list[~azure.mgmt.authorization.v2022_04_01.models.ProviderOperationsMetadata]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[ProviderOperationsMetadata]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.ProviderOperationsMetadata"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: The list of providers.
        :paramtype value: list[~azure.mgmt.authorization.v2022_04_01.models.ProviderOperationsMetadata]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class ResourceType(_serialization.Model):
    """Resource Type.

    :ivar name: The resource type name.
    :vartype name: str
    :ivar display_name: The resource type display name.
    :vartype display_name: str
    :ivar operations: The resource type operations.
    :vartype operations: list[~azure.mgmt.authorization.v2022_04_01.models.ProviderOperation]
    """

    _attribute_map = {
        "name": {"key": "name", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "operations": {"key": "operations", "type": "[ProviderOperation]"},
    }

    def __init__(
        self,
        *,
        name: Optional[str] = None,
        display_name: Optional[str] = None,
        operations: Optional[List["_models.ProviderOperation"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: The resource type name.
        :paramtype name: str
        :keyword display_name: The resource type display name.
        :paramtype display_name: str
        :keyword operations: The resource type operations.
        :paramtype operations: list[~azure.mgmt.authorization.v2022_04_01.models.ProviderOperation]
        """
        super().__init__(**kwargs)
        self.name = name
        self.display_name = display_name
        self.operations = operations


class RoleAssignment(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role Assignments.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role assignment ID.
    :vartype id: str
    :ivar name: The role assignment name.
    :vartype name: str
    :ivar type: The role assignment type.
    :vartype type: str
    :ivar scope: The role assignment scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or ~azure.mgmt.authorization.v2022_04_01.models.PrincipalType
    :ivar description: Description of role assignment.
    :vartype description: str
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently the only accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: Time it was created.
    :vartype created_on: ~datetime.datetime
    :ivar updated_on: Time it was updated.
    :vartype updated_on: ~datetime.datetime
    :ivar created_by: Id of the user who created the assignment.
    :vartype created_by: str
    :ivar updated_by: Id of the user who updated the assignment.
    :vartype updated_by: str
    :ivar delegated_managed_identity_resource_id: Id of the delegated managed identity resource.
    :vartype delegated_managed_identity_resource_id: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "scope": {"readonly": True},
        "created_on": {"readonly": True},
        "updated_on": {"readonly": True},
        "created_by": {"readonly": True},
        "updated_by": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "updated_on": {"key": "properties.updatedOn", "type": "iso-8601"},
        "created_by": {"key": "properties.createdBy", "type": "str"},
        "updated_by": {"key": "properties.updatedBy", "type": "str"},
        "delegated_managed_identity_resource_id": {
            "key": "properties.delegatedManagedIdentityResourceId",
            "type": "str",
        },
    }

    def __init__(
        self,
        *,
        role_definition_id: Optional[str] = None,
        principal_id: Optional[str] = None,
        principal_type: Union[str, "_models.PrincipalType"] = "User",
        description: Optional[str] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        delegated_managed_identity_resource_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_definition_id: The role definition ID.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
        :paramtype principal_type: str or ~azure.mgmt.authorization.v2022_04_01.models.PrincipalType
        :keyword description: Description of role assignment.
        :paramtype description: str
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently the only accepted value is
         '2.0'.
        :paramtype condition_version: str
        :keyword delegated_managed_identity_resource_id: Id of the delegated managed identity resource.
        :paramtype delegated_managed_identity_resource_id: str
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.scope = None
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.description = description
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = None
        self.updated_on = None
        self.created_by = None
        self.updated_by = None
        self.delegated_managed_identity_resource_id = delegated_managed_identity_resource_id


class RoleAssignmentCreateParameters(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role assignment create parameters.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to Azure.

    :ivar scope: The role assignment scope.
    :vartype scope: str
    :ivar role_definition_id: The role definition ID. Required.
    :vartype role_definition_id: str
    :ivar principal_id: The principal ID. Required.
    :vartype principal_id: str
    :ivar principal_type: The principal type of the assigned principal ID. Known values are:
     "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
    :vartype principal_type: str or ~azure.mgmt.authorization.v2022_04_01.models.PrincipalType
    :ivar description: Description of role assignment.
    :vartype description: str
    :ivar condition: The conditions on the role assignment. This limits the resources it can be
     assigned to. e.g.:
     @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
     StringEqualsIgnoreCase 'foo_storage_container'.
    :vartype condition: str
    :ivar condition_version: Version of the condition. Currently the only accepted value is '2.0'.
    :vartype condition_version: str
    :ivar created_on: Time it was created.
    :vartype created_on: ~datetime.datetime
    :ivar updated_on: Time it was updated.
    :vartype updated_on: ~datetime.datetime
    :ivar created_by: Id of the user who created the assignment.
    :vartype created_by: str
    :ivar updated_by: Id of the user who updated the assignment.
    :vartype updated_by: str
    :ivar delegated_managed_identity_resource_id: Id of the delegated managed identity resource.
    :vartype delegated_managed_identity_resource_id: str
    """

    _validation = {
        "scope": {"readonly": True},
        "role_definition_id": {"required": True},
        "principal_id": {"required": True},
        "created_on": {"readonly": True},
        "updated_on": {"readonly": True},
        "created_by": {"readonly": True},
        "updated_by": {"readonly": True},
    }

    _attribute_map = {
        "scope": {"key": "properties.scope", "type": "str"},
        "role_definition_id": {"key": "properties.roleDefinitionId", "type": "str"},
        "principal_id": {"key": "properties.principalId", "type": "str"},
        "principal_type": {"key": "properties.principalType", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "condition": {"key": "properties.condition", "type": "str"},
        "condition_version": {"key": "properties.conditionVersion", "type": "str"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "updated_on": {"key": "properties.updatedOn", "type": "iso-8601"},
        "created_by": {"key": "properties.createdBy", "type": "str"},
        "updated_by": {"key": "properties.updatedBy", "type": "str"},
        "delegated_managed_identity_resource_id": {
            "key": "properties.delegatedManagedIdentityResourceId",
            "type": "str",
        },
    }

    def __init__(
        self,
        *,
        role_definition_id: str,
        principal_id: str,
        principal_type: Union[str, "_models.PrincipalType"] = "User",
        description: Optional[str] = None,
        condition: Optional[str] = None,
        condition_version: Optional[str] = None,
        delegated_managed_identity_resource_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_definition_id: The role definition ID. Required.
        :paramtype role_definition_id: str
        :keyword principal_id: The principal ID. Required.
        :paramtype principal_id: str
        :keyword principal_type: The principal type of the assigned principal ID. Known values are:
         "User", "Group", "ServicePrincipal", "ForeignGroup", and "Device".
        :paramtype principal_type: str or ~azure.mgmt.authorization.v2022_04_01.models.PrincipalType
        :keyword description: Description of role assignment.
        :paramtype description: str
        :keyword condition: The conditions on the role assignment. This limits the resources it can be
         assigned to. e.g.:
         @Resource[Microsoft.Storage/storageAccounts/blobServices/containers:ContainerName]
         StringEqualsIgnoreCase 'foo_storage_container'.
        :paramtype condition: str
        :keyword condition_version: Version of the condition. Currently the only accepted value is
         '2.0'.
        :paramtype condition_version: str
        :keyword delegated_managed_identity_resource_id: Id of the delegated managed identity resource.
        :paramtype delegated_managed_identity_resource_id: str
        """
        super().__init__(**kwargs)
        self.scope = None
        self.role_definition_id = role_definition_id
        self.principal_id = principal_id
        self.principal_type = principal_type
        self.description = description
        self.condition = condition
        self.condition_version = condition_version
        self.created_on = None
        self.updated_on = None
        self.created_by = None
        self.updated_by = None
        self.delegated_managed_identity_resource_id = delegated_managed_identity_resource_id


class RoleAssignmentFilter(_serialization.Model):
    """Role Assignments filter.

    :ivar principal_id: Returns role assignment of the specific principal.
    :vartype principal_id: str
    """

    _attribute_map = {
        "principal_id": {"key": "principalId", "type": "str"},
    }

    def __init__(self, *, principal_id: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword principal_id: Returns role assignment of the specific principal.
        :paramtype principal_id: str
        """
        super().__init__(**kwargs)
        self.principal_id = principal_id


class RoleAssignmentListResult(_serialization.Model):
    """Role assignment list operation result.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar value: Role assignment list.
    :vartype value: list[~azure.mgmt.authorization.v2022_04_01.models.RoleAssignment]
    :ivar next_link: The skipToken to use for getting the next set of results.
    :vartype next_link: str
    """

    _validation = {
        "next_link": {"readonly": True},
    }

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleAssignment]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(self, *, value: Optional[List["_models.RoleAssignment"]] = None, **kwargs: Any) -> None:
        """
        :keyword value: Role assignment list.
        :paramtype value: list[~azure.mgmt.authorization.v2022_04_01.models.RoleAssignment]
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = None


class RoleDefinition(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Role definition.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The role definition ID.
    :vartype id: str
    :ivar name: The role definition name.
    :vartype name: str
    :ivar type: The role definition type.
    :vartype type: str
    :ivar role_name: The role name.
    :vartype role_name: str
    :ivar description: The role definition description.
    :vartype description: str
    :ivar role_type: The role type.
    :vartype role_type: str
    :ivar permissions: Role definition permissions.
    :vartype permissions: list[~azure.mgmt.authorization.v2022_04_01.models.Permission]
    :ivar assignable_scopes: Role definition assignable scopes.
    :vartype assignable_scopes: list[str]
    :ivar created_on: Time it was created.
    :vartype created_on: ~datetime.datetime
    :ivar updated_on: Time it was updated.
    :vartype updated_on: ~datetime.datetime
    :ivar created_by: Id of the user who created the assignment.
    :vartype created_by: str
    :ivar updated_by: Id of the user who updated the assignment.
    :vartype updated_by: str
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
        "created_on": {"readonly": True},
        "updated_on": {"readonly": True},
        "created_by": {"readonly": True},
        "updated_by": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "role_name": {"key": "properties.roleName", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "role_type": {"key": "properties.type", "type": "str"},
        "permissions": {"key": "properties.permissions", "type": "[Permission]"},
        "assignable_scopes": {"key": "properties.assignableScopes", "type": "[str]"},
        "created_on": {"key": "properties.createdOn", "type": "iso-8601"},
        "updated_on": {"key": "properties.updatedOn", "type": "iso-8601"},
        "created_by": {"key": "properties.createdBy", "type": "str"},
        "updated_by": {"key": "properties.updatedBy", "type": "str"},
    }

    def __init__(
        self,
        *,
        role_name: Optional[str] = None,
        description: Optional[str] = None,
        role_type: Optional[str] = None,
        permissions: Optional[List["_models.Permission"]] = None,
        assignable_scopes: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword role_name: The role name.
        :paramtype role_name: str
        :keyword description: The role definition description.
        :paramtype description: str
        :keyword role_type: The role type.
        :paramtype role_type: str
        :keyword permissions: Role definition permissions.
        :paramtype permissions: list[~azure.mgmt.authorization.v2022_04_01.models.Permission]
        :keyword assignable_scopes: Role definition assignable scopes.
        :paramtype assignable_scopes: list[str]
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.role_name = role_name
        self.description = description
        self.role_type = role_type
        self.permissions = permissions
        self.assignable_scopes = assignable_scopes
        self.created_on = None
        self.updated_on = None
        self.created_by = None
        self.updated_by = None


class RoleDefinitionFilter(_serialization.Model):
    """Role Definitions filter.

    :ivar role_name: Returns role definition with the specific name.
    :vartype role_name: str
    :ivar type: Returns role definition with the specific type.
    :vartype type: str
    """

    _attribute_map = {
        "role_name": {"key": "roleName", "type": "str"},
        "type": {"key": "type", "type": "str"},
    }

    def __init__(self, *, role_name: Optional[str] = None, type: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword role_name: Returns role definition with the specific name.
        :paramtype role_name: str
        :keyword type: Returns role definition with the specific type.
        :paramtype type: str
        """
        super().__init__(**kwargs)
        self.role_name = role_name
        self.type = type


class RoleDefinitionListResult(_serialization.Model):
    """Role definition list operation result.

    :ivar value: Role definition list.
    :vartype value: list[~azure.mgmt.authorization.v2022_04_01.models.RoleDefinition]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[RoleDefinition]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.RoleDefinition"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: Role definition list.
        :paramtype value: list[~azure.mgmt.authorization.v2022_04_01.models.RoleDefinition]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class RoleManagementPolicyRule(_serialization.Model):
    """The role management policy rule.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RoleManagementPolicyApprovalRule, RoleManagementPolicyAuthenticationContextRule,
    RoleManagementPolicyEnablementRule, RoleManagementPolicyExpirationRule,
    RoleManagementPolicyNotificationRule

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
    }

    _subtype_map = {
        "rule_type": {
            "RoleManagementPolicyApprovalRule": "RoleManagementPolicyApprovalRule",
            "RoleManagementPolicyAuthenticationContextRule": "RoleManagementPolicyAuthenticationContextRule",
            "RoleManagementPolicyEnablementRule": "RoleManagementPolicyEnablementRule",
            "RoleManagementPolicyExpirationRule": "RoleManagementPolicyExpirationRule",
            "RoleManagementPolicyNotificationRule": "RoleManagementPolicyNotificationRule",
        }
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
        """
        super().__init__(**kwargs)
        self.id = id
        self.rule_type: Optional[str] = None
        self.target = target


class RoleManagementPolicyApprovalRule(RoleManagementPolicyRule):
    """The role management policy approval rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
    :ivar setting: The approval setting.
    :vartype setting: ~azure.mgmt.authorization.v2022_04_01.models.ApprovalSettings
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "setting": {"key": "setting", "type": "ApprovalSettings"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        setting: Optional["_models.ApprovalSettings"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
        :keyword setting: The approval setting.
        :paramtype setting: ~azure.mgmt.authorization.v2022_04_01.models.ApprovalSettings
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyApprovalRule"
        self.setting = setting


class RoleManagementPolicyAuthenticationContextRule(RoleManagementPolicyRule):
    """The role management policy authentication context rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
    :ivar is_enabled: The value indicating if rule is enabled.
    :vartype is_enabled: bool
    :ivar claim_value: The claim value.
    :vartype claim_value: str
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "is_enabled": {"key": "isEnabled", "type": "bool"},
        "claim_value": {"key": "claimValue", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        is_enabled: Optional[bool] = None,
        claim_value: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
        :keyword is_enabled: The value indicating if rule is enabled.
        :paramtype is_enabled: bool
        :keyword claim_value: The claim value.
        :paramtype claim_value: str
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyAuthenticationContextRule"
        self.is_enabled = is_enabled
        self.claim_value = claim_value


class RoleManagementPolicyEnablementRule(RoleManagementPolicyRule):
    """The role management policy enablement rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
    :ivar enabled_rules: The list of enabled rules.
    :vartype enabled_rules: list[str or
     ~azure.mgmt.authorization.v2022_04_01.models.EnablementRules]
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "enabled_rules": {"key": "enabledRules", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        enabled_rules: Optional[List[Union[str, "_models.EnablementRules"]]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
        :keyword enabled_rules: The list of enabled rules.
        :paramtype enabled_rules: list[str or
         ~azure.mgmt.authorization.v2022_04_01.models.EnablementRules]
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyEnablementRule"
        self.enabled_rules = enabled_rules


class RoleManagementPolicyExpirationRule(RoleManagementPolicyRule):
    """The role management policy expiration rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
    :ivar is_expiration_required: The value indicating whether expiration is required.
    :vartype is_expiration_required: bool
    :ivar maximum_duration: The maximum duration of expiration in timespan.
    :vartype maximum_duration: str
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "is_expiration_required": {"key": "isExpirationRequired", "type": "bool"},
        "maximum_duration": {"key": "maximumDuration", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        is_expiration_required: Optional[bool] = None,
        maximum_duration: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
        :keyword is_expiration_required: The value indicating whether expiration is required.
        :paramtype is_expiration_required: bool
        :keyword maximum_duration: The maximum duration of expiration in timespan.
        :paramtype maximum_duration: str
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyExpirationRule"
        self.is_expiration_required = is_expiration_required
        self.maximum_duration = maximum_duration


class RoleManagementPolicyNotificationRule(RoleManagementPolicyRule):
    """The role management policy notification rule.

    All required parameters must be populated in order to send to Azure.

    :ivar id: The id of the rule.
    :vartype id: str
    :ivar rule_type: The type of rule. Required. Known values are:
     "RoleManagementPolicyApprovalRule", "RoleManagementPolicyAuthenticationContextRule",
     "RoleManagementPolicyEnablementRule", "RoleManagementPolicyExpirationRule", and
     "RoleManagementPolicyNotificationRule".
    :vartype rule_type: str or
     ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleType
    :ivar target: The target of the current rule.
    :vartype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
    :ivar notification_type: The type of notification. "Email"
    :vartype notification_type: str or
     ~azure.mgmt.authorization.v2022_04_01.models.NotificationDeliveryMechanism
    :ivar notification_level: The notification level. Known values are: "None", "Critical", and
     "All".
    :vartype notification_level: str or
     ~azure.mgmt.authorization.v2022_04_01.models.NotificationLevel
    :ivar recipient_type: The recipient type. Known values are: "Requestor", "Approver", and
     "Admin".
    :vartype recipient_type: str or ~azure.mgmt.authorization.v2022_04_01.models.RecipientType
    :ivar notification_recipients: The list of notification recipients.
    :vartype notification_recipients: list[str]
    :ivar is_default_recipients_enabled: Determines if the notification will be sent to the
     recipient type specified in the policy rule.
    :vartype is_default_recipients_enabled: bool
    """

    _validation = {
        "rule_type": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "rule_type": {"key": "ruleType", "type": "str"},
        "target": {"key": "target", "type": "RoleManagementPolicyRuleTarget"},
        "notification_type": {"key": "notificationType", "type": "str"},
        "notification_level": {"key": "notificationLevel", "type": "str"},
        "recipient_type": {"key": "recipientType", "type": "str"},
        "notification_recipients": {"key": "notificationRecipients", "type": "[str]"},
        "is_default_recipients_enabled": {"key": "isDefaultRecipientsEnabled", "type": "bool"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        target: Optional["_models.RoleManagementPolicyRuleTarget"] = None,
        notification_type: Optional[Union[str, "_models.NotificationDeliveryMechanism"]] = None,
        notification_level: Optional[Union[str, "_models.NotificationLevel"]] = None,
        recipient_type: Optional[Union[str, "_models.RecipientType"]] = None,
        notification_recipients: Optional[List[str]] = None,
        is_default_recipients_enabled: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the rule.
        :paramtype id: str
        :keyword target: The target of the current rule.
        :paramtype target: ~azure.mgmt.authorization.v2022_04_01.models.RoleManagementPolicyRuleTarget
        :keyword notification_type: The type of notification. "Email"
        :paramtype notification_type: str or
         ~azure.mgmt.authorization.v2022_04_01.models.NotificationDeliveryMechanism
        :keyword notification_level: The notification level. Known values are: "None", "Critical", and
         "All".
        :paramtype notification_level: str or
         ~azure.mgmt.authorization.v2022_04_01.models.NotificationLevel
        :keyword recipient_type: The recipient type. Known values are: "Requestor", "Approver", and
         "Admin".
        :paramtype recipient_type: str or ~azure.mgmt.authorization.v2022_04_01.models.RecipientType
        :keyword notification_recipients: The list of notification recipients.
        :paramtype notification_recipients: list[str]
        :keyword is_default_recipients_enabled: Determines if the notification will be sent to the
         recipient type specified in the policy rule.
        :paramtype is_default_recipients_enabled: bool
        """
        super().__init__(id=id, target=target, **kwargs)
        self.rule_type: str = "RoleManagementPolicyNotificationRule"
        self.notification_type = notification_type
        self.notification_level = notification_level
        self.recipient_type = recipient_type
        self.notification_recipients = notification_recipients
        self.is_default_recipients_enabled = is_default_recipients_enabled


class RoleManagementPolicyRuleTarget(_serialization.Model):
    """The role management policy rule target.

    :ivar caller: The caller of the setting.
    :vartype caller: str
    :ivar operations: The type of operation.
    :vartype operations: list[str]
    :ivar level: The assignment level to which rule is applied.
    :vartype level: str
    :ivar target_objects: The list of target objects.
    :vartype target_objects: list[str]
    :ivar inheritable_settings: The list of inheritable settings.
    :vartype inheritable_settings: list[str]
    :ivar enforced_settings: The list of enforced settings.
    :vartype enforced_settings: list[str]
    """

    _attribute_map = {
        "caller": {"key": "caller", "type": "str"},
        "operations": {"key": "operations", "type": "[str]"},
        "level": {"key": "level", "type": "str"},
        "target_objects": {"key": "targetObjects", "type": "[str]"},
        "inheritable_settings": {"key": "inheritableSettings", "type": "[str]"},
        "enforced_settings": {"key": "enforcedSettings", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        caller: Optional[str] = None,
        operations: Optional[List[str]] = None,
        level: Optional[str] = None,
        target_objects: Optional[List[str]] = None,
        inheritable_settings: Optional[List[str]] = None,
        enforced_settings: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword caller: The caller of the setting.
        :paramtype caller: str
        :keyword operations: The type of operation.
        :paramtype operations: list[str]
        :keyword level: The assignment level to which rule is applied.
        :paramtype level: str
        :keyword target_objects: The list of target objects.
        :paramtype target_objects: list[str]
        :keyword inheritable_settings: The list of inheritable settings.
        :paramtype inheritable_settings: list[str]
        :keyword enforced_settings: The list of enforced settings.
        :paramtype enforced_settings: list[str]
        """
        super().__init__(**kwargs)
        self.caller = caller
        self.operations = operations
        self.level = level
        self.target_objects = target_objects
        self.inheritable_settings = inheritable_settings
        self.enforced_settings = enforced_settings


class UserSet(_serialization.Model):
    """The detail of a user.

    :ivar user_type: The type of user. Known values are: "User" and "Group".
    :vartype user_type: str or ~azure.mgmt.authorization.v2022_04_01.models.UserType
    :ivar is_backup: The value indicating whether the user is a backup fallback approver.
    :vartype is_backup: bool
    :ivar id: The object id of the user.
    :vartype id: str
    :ivar description: The description of the user.
    :vartype description: str
    """

    _attribute_map = {
        "user_type": {"key": "userType", "type": "str"},
        "is_backup": {"key": "isBackup", "type": "bool"},
        "id": {"key": "id", "type": "str"},
        "description": {"key": "description", "type": "str"},
    }

    def __init__(
        self,
        *,
        user_type: Optional[Union[str, "_models.UserType"]] = None,
        is_backup: Optional[bool] = None,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        description: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword user_type: The type of user. Known values are: "User" and "Group".
        :paramtype user_type: str or ~azure.mgmt.authorization.v2022_04_01.models.UserType
        :keyword is_backup: The value indicating whether the user is a backup fallback approver.
        :paramtype is_backup: bool
        :keyword id: The object id of the user.
        :paramtype id: str
        :keyword description: The description of the user.
        :paramtype description: str
        """
        super().__init__(**kwargs)
        self.user_type = user_type
        self.is_backup = is_backup
        self.id = id
        self.description = description


class ValidationResponse(_serialization.Model):
    """Validation response.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar is_valid: Whether or not validation succeeded.
    :vartype is_valid: bool
    :ivar error_info: Failed validation result details.
    :vartype error_info: ~azure.mgmt.authorization.v2022_04_01.models.ValidationResponseErrorInfo
    """

    _validation = {
        "is_valid": {"readonly": True},
    }

    _attribute_map = {
        "is_valid": {"key": "isValid", "type": "bool"},
        "error_info": {"key": "errorInfo", "type": "ValidationResponseErrorInfo"},
    }

    def __init__(self, *, error_info: Optional["_models.ValidationResponseErrorInfo"] = None, **kwargs: Any) -> None:
        """
        :keyword error_info: Failed validation result details.
        :paramtype error_info: ~azure.mgmt.authorization.v2022_04_01.models.ValidationResponseErrorInfo
        """
        super().__init__(**kwargs)
        self.is_valid = None
        self.error_info = error_info


class ValidationResponseErrorInfo(_serialization.Model):
    """Failed validation result details.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: Error code indicating why validation failed.
    :vartype code: str
    :ivar message: Message indicating why validation failed.
    :vartype message: str
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
