# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from enum import Enum
from azure.core import CaseInsensitiveEnumMeta


class ApprovalMode(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of rule."""

    SINGLE_STAGE = "SingleStage"
    SERIAL = "Serial"
    PARALLEL = "Parallel"
    NO_APPROVAL = "NoApproval"


class EnablementRules(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of enablement rule."""

    MULTI_FACTOR_AUTHENTICATION = "MultiFactorAuthentication"
    JUSTIFICATION = "Justification"
    TICKETING = "Ticketing"


class NotificationDeliveryMechanism(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of notification."""

    EMAIL = "Email"


class NotificationLevel(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The notification level."""

    NONE = "None"
    CRITICAL = "Critical"
    ALL = "All"


class PrincipalType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The principal type of the assigned principal ID."""

    USER = "User"
    GROUP = "Group"
    SERVICE_PRINCIPAL = "ServicePrincipal"
    FOREIGN_GROUP = "ForeignGroup"
    DEVICE = "Device"


class RecipientType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The recipient type."""

    REQUESTOR = "Requestor"
    APPROVER = "Approver"
    ADMIN = "Admin"


class RoleManagementPolicyRuleType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of rule."""

    ROLE_MANAGEMENT_POLICY_APPROVAL_RULE = "RoleManagementPolicyApprovalRule"
    ROLE_MANAGEMENT_POLICY_AUTHENTICATION_CONTEXT_RULE = "RoleManagementPolicyAuthenticationContextRule"
    ROLE_MANAGEMENT_POLICY_ENABLEMENT_RULE = "RoleManagementPolicyEnablementRule"
    ROLE_MANAGEMENT_POLICY_EXPIRATION_RULE = "RoleManagementPolicyExpirationRule"
    ROLE_MANAGEMENT_POLICY_NOTIFICATION_RULE = "RoleManagementPolicyNotificationRule"


class UserType(str, Enum, metaclass=CaseInsensitiveEnumMeta):
    """The type of user."""

    USER = "User"
    GROUP = "Group"
