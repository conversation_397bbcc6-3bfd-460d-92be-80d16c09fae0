# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from ._models_py3 import DenyAssignment
from ._models_py3 import DenyAssignmentFilter
from ._models_py3 import DenyAssignmentListResult
from ._models_py3 import DenyAssignmentPermission
from ._models_py3 import ErrorAdditionalInfo
from ._models_py3 import ErrorDetail
from ._models_py3 import ErrorResponse
from ._models_py3 import Principal
from ._patch import __all__ as _patch_all
from ._patch import *  # pylint: disable=unused-wildcard-import
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "DenyAssignment",
    "DenyAssignmentFilter",
    "DenyAssignmentListResult",
    "DenyAssignmentPermission",
    "ErrorAdditionalInfo",
    "ErrorDetail",
    "ErrorResponse",
    "Principal",
]
__all__.extend([p for p in _patch_all if p not in __all__])
_patch_sdk()
