# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import Any, List, Optional, TYPE_CHECKING

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class DenyAssignment(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Deny Assignment.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar id: The deny assignment ID.
    :vartype id: str
    :ivar name: The deny assignment name.
    :vartype name: str
    :ivar type: The deny assignment type.
    :vartype type: str
    :ivar deny_assignment_name: The display name of the deny assignment.
    :vartype deny_assignment_name: str
    :ivar description: The description of the deny assignment.
    :vartype description: str
    :ivar permissions: An array of permissions that are denied by the deny assignment.
    :vartype permissions:
     list[~azure.mgmt.authorization.v2018_07_01_preview.models.DenyAssignmentPermission]
    :ivar scope: The deny assignment scope.
    :vartype scope: str
    :ivar do_not_apply_to_child_scopes: Determines if the deny assignment applies to child scopes.
     Default value is false.
    :vartype do_not_apply_to_child_scopes: bool
    :ivar principals: Array of principals to which the deny assignment applies.
    :vartype principals: list[~azure.mgmt.authorization.v2018_07_01_preview.models.Principal]
    :ivar exclude_principals: Array of principals to which the deny assignment does not apply.
    :vartype exclude_principals:
     list[~azure.mgmt.authorization.v2018_07_01_preview.models.Principal]
    :ivar is_system_protected: Specifies whether this deny assignment was created by Azure and
     cannot be edited or deleted.
    :vartype is_system_protected: bool
    """

    _validation = {
        "id": {"readonly": True},
        "name": {"readonly": True},
        "type": {"readonly": True},
    }

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "deny_assignment_name": {"key": "properties.denyAssignmentName", "type": "str"},
        "description": {"key": "properties.description", "type": "str"},
        "permissions": {"key": "properties.permissions", "type": "[DenyAssignmentPermission]"},
        "scope": {"key": "properties.scope", "type": "str"},
        "do_not_apply_to_child_scopes": {"key": "properties.doNotApplyToChildScopes", "type": "bool"},
        "principals": {"key": "properties.principals", "type": "[Principal]"},
        "exclude_principals": {"key": "properties.excludePrincipals", "type": "[Principal]"},
        "is_system_protected": {"key": "properties.isSystemProtected", "type": "bool"},
    }

    def __init__(
        self,
        *,
        deny_assignment_name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[List["_models.DenyAssignmentPermission"]] = None,
        scope: Optional[str] = None,
        do_not_apply_to_child_scopes: Optional[bool] = None,
        principals: Optional[List["_models.Principal"]] = None,
        exclude_principals: Optional[List["_models.Principal"]] = None,
        is_system_protected: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword deny_assignment_name: The display name of the deny assignment.
        :paramtype deny_assignment_name: str
        :keyword description: The description of the deny assignment.
        :paramtype description: str
        :keyword permissions: An array of permissions that are denied by the deny assignment.
        :paramtype permissions:
         list[~azure.mgmt.authorization.v2018_07_01_preview.models.DenyAssignmentPermission]
        :keyword scope: The deny assignment scope.
        :paramtype scope: str
        :keyword do_not_apply_to_child_scopes: Determines if the deny assignment applies to child
         scopes. Default value is false.
        :paramtype do_not_apply_to_child_scopes: bool
        :keyword principals: Array of principals to which the deny assignment applies.
        :paramtype principals: list[~azure.mgmt.authorization.v2018_07_01_preview.models.Principal]
        :keyword exclude_principals: Array of principals to which the deny assignment does not apply.
        :paramtype exclude_principals:
         list[~azure.mgmt.authorization.v2018_07_01_preview.models.Principal]
        :keyword is_system_protected: Specifies whether this deny assignment was created by Azure and
         cannot be edited or deleted.
        :paramtype is_system_protected: bool
        """
        super().__init__(**kwargs)
        self.id = None
        self.name = None
        self.type = None
        self.deny_assignment_name = deny_assignment_name
        self.description = description
        self.permissions = permissions
        self.scope = scope
        self.do_not_apply_to_child_scopes = do_not_apply_to_child_scopes
        self.principals = principals
        self.exclude_principals = exclude_principals
        self.is_system_protected = is_system_protected


class DenyAssignmentFilter(_serialization.Model):
    """Deny Assignments filter.

    :ivar deny_assignment_name: Return deny assignment with specified name.
    :vartype deny_assignment_name: str
    :ivar principal_id: Return all deny assignments where the specified principal is listed in the
     principals list of deny assignments.
    :vartype principal_id: str
    :ivar gdpr_export_principal_id: Return all deny assignments where the specified principal is
     listed either in the principals list or exclude principals list of deny assignments.
    :vartype gdpr_export_principal_id: str
    """

    _attribute_map = {
        "deny_assignment_name": {"key": "denyAssignmentName", "type": "str"},
        "principal_id": {"key": "principalId", "type": "str"},
        "gdpr_export_principal_id": {"key": "gdprExportPrincipalId", "type": "str"},
    }

    def __init__(
        self,
        *,
        deny_assignment_name: Optional[str] = None,
        principal_id: Optional[str] = None,
        gdpr_export_principal_id: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword deny_assignment_name: Return deny assignment with specified name.
        :paramtype deny_assignment_name: str
        :keyword principal_id: Return all deny assignments where the specified principal is listed in
         the principals list of deny assignments.
        :paramtype principal_id: str
        :keyword gdpr_export_principal_id: Return all deny assignments where the specified principal is
         listed either in the principals list or exclude principals list of deny assignments.
        :paramtype gdpr_export_principal_id: str
        """
        super().__init__(**kwargs)
        self.deny_assignment_name = deny_assignment_name
        self.principal_id = principal_id
        self.gdpr_export_principal_id = gdpr_export_principal_id


class DenyAssignmentListResult(_serialization.Model):
    """Deny assignment list operation result.

    :ivar value: Deny assignment list.
    :vartype value: list[~azure.mgmt.authorization.v2018_07_01_preview.models.DenyAssignment]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[DenyAssignment]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self, *, value: Optional[List["_models.DenyAssignment"]] = None, next_link: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword value: Deny assignment list.
        :paramtype value: list[~azure.mgmt.authorization.v2018_07_01_preview.models.DenyAssignment]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class DenyAssignmentPermission(_serialization.Model):
    """Deny assignment permissions.

    :ivar actions: Actions to which the deny assignment does not grant access.
    :vartype actions: list[str]
    :ivar not_actions: Actions to exclude from that the deny assignment does not grant access.
    :vartype not_actions: list[str]
    :ivar data_actions: Data actions to which the deny assignment does not grant access.
    :vartype data_actions: list[str]
    :ivar not_data_actions: Data actions to exclude from that the deny assignment does not grant
     access.
    :vartype not_data_actions: list[str]
    """

    _attribute_map = {
        "actions": {"key": "actions", "type": "[str]"},
        "not_actions": {"key": "notActions", "type": "[str]"},
        "data_actions": {"key": "dataActions", "type": "[str]"},
        "not_data_actions": {"key": "notDataActions", "type": "[str]"},
    }

    def __init__(
        self,
        *,
        actions: Optional[List[str]] = None,
        not_actions: Optional[List[str]] = None,
        data_actions: Optional[List[str]] = None,
        not_data_actions: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword actions: Actions to which the deny assignment does not grant access.
        :paramtype actions: list[str]
        :keyword not_actions: Actions to exclude from that the deny assignment does not grant access.
        :paramtype not_actions: list[str]
        :keyword data_actions: Data actions to which the deny assignment does not grant access.
        :paramtype data_actions: list[str]
        :keyword not_data_actions: Data actions to exclude from that the deny assignment does not grant
         access.
        :paramtype not_data_actions: list[str]
        """
        super().__init__(**kwargs)
        self.actions = actions
        self.not_actions = not_actions
        self.data_actions = data_actions
        self.not_data_actions = not_data_actions


class ErrorAdditionalInfo(_serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: JSON
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(_serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.authorization.v2018_07_01_preview.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info:
     list[~azure.mgmt.authorization.v2018_07_01_preview.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(_serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed
    operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.mgmt.authorization.v2018_07_01_preview.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["_models.ErrorDetail"] = None, **kwargs: Any) -> None:
        """
        :keyword error: The error object.
        :paramtype error: ~azure.mgmt.authorization.v2018_07_01_preview.models.ErrorDetail
        """
        super().__init__(**kwargs)
        self.error = error


class Principal(_serialization.Model):
    """The name of the entity last modified it.

    :ivar id: The id of the principal made changes.
    :vartype id: str
    :ivar display_name: The name of the principal made changes.
    :vartype display_name: str
    :ivar type: Type of principal such as user , group etc.
    :vartype type: str
    :ivar email: Email of principal.
    :vartype email: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "display_name": {"key": "displayName", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "email": {"key": "email", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        display_name: Optional[str] = None,
        type: Optional[str] = None,
        email: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The id of the principal made changes.
        :paramtype id: str
        :keyword display_name: The name of the principal made changes.
        :paramtype display_name: str
        :keyword type: Type of principal such as user , group etc.
        :paramtype type: str
        :keyword email: Email of principal.
        :paramtype email: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.display_name = display_name
        self.type = type
        self.email = email
