# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for
# license information.
#
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is
# regenerated.
# --------------------------------------------------------------------------

from typing import Any, Optional, TYPE_CHECKING

from azure.mgmt.core import AsyncARMPipelineClient
from azure.profiles import KnownProfiles, ProfileDefinition
from azure.profiles.multiapiclient import MultiApiClientMixin

from .._serialization import Deserializer, Serializer
from ._configuration import AuthorizationManagementClientConfiguration

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from azure.core.credentials_async import AsyncTokenCredential

class _SDKClient(object):
    def __init__(self, *args, **kwargs):
        """This is a fake class to support current implemetation of MultiApiClientMixin."
        Will be removed in final version of multiapi azure-core based client
        """
        pass

class AuthorizationManagementClient(MultiApiClientMixin, _SDKClient):
    """Role based access control provides you a way to apply granular level policy administration down to individual resources or resource groups. These operations enable you to get deny assignments. A deny assignment describes the set of actions on resources that are denied for Azure Active Directory users.

    This ready contains multiple API versions, to help you deal with all of the Azure clouds
    (Azure Stack, Azure Government, Azure China, etc.).
    By default, it uses the latest API version available on public Azure.
    For production, you should stick to a particular api-version and/or profile.
    The profile sets a mapping between an operation group and its API version.
    The api-version parameter sets the default API version if the operation
    group is not described in the profile.

    :param credential: Credential needed for the client to connect to Azure. Required.
    :type credential: ~azure.core.credentials_async.AsyncTokenCredential
    :param subscription_id: The ID of the target subscription. Required.
    :type subscription_id: str
    :param api_version: API version to use if no profile is provided, or if missing in profile.
    :type api_version: str
    :param base_url: Service URL
    :type base_url: str
    :param profile: A profile definition, from KnownProfiles to dict.
    :type profile: azure.profiles.KnownProfiles
    :keyword int polling_interval: Default waiting time between two polls for LRO operations if no Retry-After header is present.
    """

    DEFAULT_API_VERSION = '2022-04-01'
    _PROFILE_TAG = "azure.mgmt.authorization.AuthorizationManagementClient"
    LATEST_PROFILE = ProfileDefinition({
        _PROFILE_TAG: {
            None: DEFAULT_API_VERSION,
            'access_review_default_settings': '2021-12-01-preview',
            'access_review_history_definition': '2021-12-01-preview',
            'access_review_history_definition_instance': '2021-12-01-preview',
            'access_review_history_definition_instances': '2021-12-01-preview',
            'access_review_history_definitions': '2021-12-01-preview',
            'access_review_instance': '2021-12-01-preview',
            'access_review_instance_contacted_reviewers': '2021-12-01-preview',
            'access_review_instance_decisions': '2021-12-01-preview',
            'access_review_instance_my_decisions': '2021-12-01-preview',
            'access_review_instances': '2021-12-01-preview',
            'access_review_instances_assigned_for_my_approval': '2021-12-01-preview',
            'access_review_schedule_definitions': '2021-12-01-preview',
            'access_review_schedule_definitions_assigned_for_my_approval': '2021-12-01-preview',
            'classic_administrators': '2015-07-01',
            'eligible_child_resources': '2020-10-01',
            'global_administrator': '2015-07-01',
            'operations': '2021-12-01-preview',
            'role_assignment_approval': '2021-01-01-preview',
            'role_assignment_approval_step': '2021-01-01-preview',
            'role_assignment_approval_steps': '2021-01-01-preview',
            'role_assignment_metrics': '2019-08-01-preview',
            'role_assignment_schedule_instances': '2020-10-01',
            'role_assignment_schedule_requests': '2020-10-01',
            'role_assignment_schedules': '2020-10-01',
            'role_eligibility_schedule_instances': '2020-10-01',
            'role_eligibility_schedule_requests': '2020-10-01',
            'role_eligibility_schedules': '2020-10-01',
            'role_management_policies': '2020-10-01',
            'role_management_policy_assignments': '2020-10-01',
            'scope_access_review_default_settings': '2021-12-01-preview',
            'scope_access_review_history_definition': '2021-12-01-preview',
            'scope_access_review_history_definition_instance': '2021-12-01-preview',
            'scope_access_review_history_definition_instances': '2021-12-01-preview',
            'scope_access_review_history_definitions': '2021-12-01-preview',
            'scope_access_review_instance': '2021-12-01-preview',
            'scope_access_review_instance_contacted_reviewers': '2021-12-01-preview',
            'scope_access_review_instance_decisions': '2021-12-01-preview',
            'scope_access_review_instances': '2021-12-01-preview',
            'scope_access_review_schedule_definitions': '2021-12-01-preview',
            'scope_role_assignment_approval': '2021-01-01-preview',
            'scope_role_assignment_approval_step': '2021-01-01-preview',
            'scope_role_assignment_approval_steps': '2021-01-01-preview',
            'tenant_level_access_review_instance_contacted_reviewers': '2021-12-01-preview',
        }},
        _PROFILE_TAG + " latest"
    )

    def __init__(
        self,
        credential: "AsyncTokenCredential",
        subscription_id: str,
        api_version: Optional[str] = None,
        base_url: str = "https://management.azure.com",
        profile: KnownProfiles = KnownProfiles.default,
        **kwargs: Any
    ) -> None:
        if api_version:
            kwargs.setdefault('api_version', api_version)
        self._config = AuthorizationManagementClientConfiguration(credential, subscription_id, **kwargs)
        self._client = AsyncARMPipelineClient(base_url=base_url, config=self._config, **kwargs)
        super(AuthorizationManagementClient, self).__init__(
            api_version=api_version,
            profile=profile
        )

    @classmethod
    def _models_dict(cls, api_version):
        return {k: v for k, v in cls.models(api_version).__dict__.items() if isinstance(v, type)}

    @classmethod
    def models(cls, api_version=DEFAULT_API_VERSION):
        """Module depends on the API version:

           * 2015-06-01: :mod:`v2015_06_01.models<azure.mgmt.authorization.v2015_06_01.models>`
           * 2015-07-01: :mod:`v2015_07_01.models<azure.mgmt.authorization.v2015_07_01.models>`
           * 2018-01-01-preview: :mod:`v2018_01_01_preview.models<azure.mgmt.authorization.v2018_01_01_preview.models>`
           * 2018-05-01-preview: :mod:`v2018_05_01_preview.models<azure.mgmt.authorization.v2018_05_01_preview.models>`
           * 2018-07-01-preview: :mod:`v2018_07_01_preview.models<azure.mgmt.authorization.v2018_07_01_preview.models>`
           * 2018-09-01-preview: :mod:`v2018_09_01_preview.models<azure.mgmt.authorization.v2018_09_01_preview.models>`
           * 2019-08-01-preview: :mod:`v2019_08_01_preview.models<azure.mgmt.authorization.v2019_08_01_preview.models>`
           * 2020-04-01-preview: :mod:`v2020_04_01_preview.models<azure.mgmt.authorization.v2020_04_01_preview.models>`
           * 2020-10-01: :mod:`v2020_10_01.models<azure.mgmt.authorization.v2020_10_01.models>`
           * 2020-10-01-preview: :mod:`v2020_10_01_preview.models<azure.mgmt.authorization.v2020_10_01_preview.models>`
           * 2021-01-01-preview: :mod:`v2021_01_01_preview.models<azure.mgmt.authorization.v2021_01_01_preview.models>`
           * 2021-03-01-preview: :mod:`v2021_03_01_preview.models<azure.mgmt.authorization.v2021_03_01_preview.models>`
           * 2021-07-01-preview: :mod:`v2021_07_01_preview.models<azure.mgmt.authorization.v2021_07_01_preview.models>`
           * 2021-12-01-preview: :mod:`v2021_12_01_preview.models<azure.mgmt.authorization.v2021_12_01_preview.models>`
           * 2022-04-01: :mod:`v2022_04_01.models<azure.mgmt.authorization.v2022_04_01.models>`
           * 2022-04-01-preview: :mod:`v2022_04_01_preview.models<azure.mgmt.authorization.v2022_04_01_preview.models>`
           * 2022-05-01-preview: :mod:`v2022_05_01_preview.models<azure.mgmt.authorization.v2022_05_01_preview.models>`
           * 2022-08-01-preview: :mod:`v2022_08_01_preview.models<azure.mgmt.authorization.v2022_08_01_preview.models>`
        """
        if api_version == '2015-06-01':
            from ..v2015_06_01 import models
            return models
        elif api_version == '2015-07-01':
            from ..v2015_07_01 import models
            return models
        elif api_version == '2018-01-01-preview':
            from ..v2018_01_01_preview import models
            return models
        elif api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview import models
            return models
        elif api_version == '2018-07-01-preview':
            from ..v2018_07_01_preview import models
            return models
        elif api_version == '2018-09-01-preview':
            from ..v2018_09_01_preview import models
            return models
        elif api_version == '2019-08-01-preview':
            from ..v2019_08_01_preview import models
            return models
        elif api_version == '2020-04-01-preview':
            from ..v2020_04_01_preview import models
            return models
        elif api_version == '2020-10-01':
            from ..v2020_10_01 import models
            return models
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview import models
            return models
        elif api_version == '2021-01-01-preview':
            from ..v2021_01_01_preview import models
            return models
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview import models
            return models
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview import models
            return models
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview import models
            return models
        elif api_version == '2022-04-01':
            from ..v2022_04_01 import models
            return models
        elif api_version == '2022-04-01-preview':
            from ..v2022_04_01_preview import models
            return models
        elif api_version == '2022-05-01-preview':
            from ..v2022_05_01_preview import models
            return models
        elif api_version == '2022-08-01-preview':
            from ..v2022_08_01_preview import models
            return models
        raise ValueError("API version {} is not available".format(api_version))

    @property
    def access_review_default_settings(self):
        """Instance depends on the API version:

           * 2018-05-01-preview: :class:`AccessReviewDefaultSettingsOperations<azure.mgmt.authorization.v2018_05_01_preview.aio.operations.AccessReviewDefaultSettingsOperations>`
           * 2021-03-01-preview: :class:`AccessReviewDefaultSettingsOperations<azure.mgmt.authorization.v2021_03_01_preview.aio.operations.AccessReviewDefaultSettingsOperations>`
           * 2021-07-01-preview: :class:`AccessReviewDefaultSettingsOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.AccessReviewDefaultSettingsOperations>`
           * 2021-12-01-preview: :class:`AccessReviewDefaultSettingsOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewDefaultSettingsOperations>`
        """
        api_version = self._get_api_version('access_review_default_settings')
        if api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview.aio.operations import AccessReviewDefaultSettingsOperations as OperationClass
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview.aio.operations import AccessReviewDefaultSettingsOperations as OperationClass
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import AccessReviewDefaultSettingsOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewDefaultSettingsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_default_settings'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_history_definition(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`AccessReviewHistoryDefinitionOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewHistoryDefinitionOperations>`
        """
        api_version = self._get_api_version('access_review_history_definition')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewHistoryDefinitionOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_history_definition'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_history_definition_instance(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`AccessReviewHistoryDefinitionInstanceOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewHistoryDefinitionInstanceOperations>`
        """
        api_version = self._get_api_version('access_review_history_definition_instance')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewHistoryDefinitionInstanceOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_history_definition_instance'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_history_definition_instances(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`AccessReviewHistoryDefinitionInstancesOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewHistoryDefinitionInstancesOperations>`
        """
        api_version = self._get_api_version('access_review_history_definition_instances')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewHistoryDefinitionInstancesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_history_definition_instances'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_history_definitions(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`AccessReviewHistoryDefinitionsOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewHistoryDefinitionsOperations>`
        """
        api_version = self._get_api_version('access_review_history_definitions')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewHistoryDefinitionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_history_definitions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_instance(self):
        """Instance depends on the API version:

           * 2018-05-01-preview: :class:`AccessReviewInstanceOperations<azure.mgmt.authorization.v2018_05_01_preview.aio.operations.AccessReviewInstanceOperations>`
           * 2021-03-01-preview: :class:`AccessReviewInstanceOperations<azure.mgmt.authorization.v2021_03_01_preview.aio.operations.AccessReviewInstanceOperations>`
           * 2021-07-01-preview: :class:`AccessReviewInstanceOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.AccessReviewInstanceOperations>`
           * 2021-12-01-preview: :class:`AccessReviewInstanceOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewInstanceOperations>`
        """
        api_version = self._get_api_version('access_review_instance')
        if api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview.aio.operations import AccessReviewInstanceOperations as OperationClass
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview.aio.operations import AccessReviewInstanceOperations as OperationClass
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import AccessReviewInstanceOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewInstanceOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_instance'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_instance_contacted_reviewers(self):
        """Instance depends on the API version:

           * 2021-07-01-preview: :class:`AccessReviewInstanceContactedReviewersOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.AccessReviewInstanceContactedReviewersOperations>`
           * 2021-12-01-preview: :class:`AccessReviewInstanceContactedReviewersOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewInstanceContactedReviewersOperations>`
        """
        api_version = self._get_api_version('access_review_instance_contacted_reviewers')
        if api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import AccessReviewInstanceContactedReviewersOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewInstanceContactedReviewersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_instance_contacted_reviewers'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_instance_decisions(self):
        """Instance depends on the API version:

           * 2018-05-01-preview: :class:`AccessReviewInstanceDecisionsOperations<azure.mgmt.authorization.v2018_05_01_preview.aio.operations.AccessReviewInstanceDecisionsOperations>`
           * 2021-03-01-preview: :class:`AccessReviewInstanceDecisionsOperations<azure.mgmt.authorization.v2021_03_01_preview.aio.operations.AccessReviewInstanceDecisionsOperations>`
           * 2021-07-01-preview: :class:`AccessReviewInstanceDecisionsOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.AccessReviewInstanceDecisionsOperations>`
           * 2021-12-01-preview: :class:`AccessReviewInstanceDecisionsOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewInstanceDecisionsOperations>`
        """
        api_version = self._get_api_version('access_review_instance_decisions')
        if api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview.aio.operations import AccessReviewInstanceDecisionsOperations as OperationClass
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview.aio.operations import AccessReviewInstanceDecisionsOperations as OperationClass
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import AccessReviewInstanceDecisionsOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewInstanceDecisionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_instance_decisions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_instance_my_decisions(self):
        """Instance depends on the API version:

           * 2018-05-01-preview: :class:`AccessReviewInstanceMyDecisionsOperations<azure.mgmt.authorization.v2018_05_01_preview.aio.operations.AccessReviewInstanceMyDecisionsOperations>`
           * 2021-03-01-preview: :class:`AccessReviewInstanceMyDecisionsOperations<azure.mgmt.authorization.v2021_03_01_preview.aio.operations.AccessReviewInstanceMyDecisionsOperations>`
           * 2021-07-01-preview: :class:`AccessReviewInstanceMyDecisionsOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.AccessReviewInstanceMyDecisionsOperations>`
           * 2021-12-01-preview: :class:`AccessReviewInstanceMyDecisionsOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewInstanceMyDecisionsOperations>`
        """
        api_version = self._get_api_version('access_review_instance_my_decisions')
        if api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview.aio.operations import AccessReviewInstanceMyDecisionsOperations as OperationClass
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview.aio.operations import AccessReviewInstanceMyDecisionsOperations as OperationClass
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import AccessReviewInstanceMyDecisionsOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewInstanceMyDecisionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_instance_my_decisions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_instances(self):
        """Instance depends on the API version:

           * 2018-05-01-preview: :class:`AccessReviewInstancesOperations<azure.mgmt.authorization.v2018_05_01_preview.aio.operations.AccessReviewInstancesOperations>`
           * 2021-03-01-preview: :class:`AccessReviewInstancesOperations<azure.mgmt.authorization.v2021_03_01_preview.aio.operations.AccessReviewInstancesOperations>`
           * 2021-07-01-preview: :class:`AccessReviewInstancesOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.AccessReviewInstancesOperations>`
           * 2021-12-01-preview: :class:`AccessReviewInstancesOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewInstancesOperations>`
        """
        api_version = self._get_api_version('access_review_instances')
        if api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview.aio.operations import AccessReviewInstancesOperations as OperationClass
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview.aio.operations import AccessReviewInstancesOperations as OperationClass
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import AccessReviewInstancesOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewInstancesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_instances'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_instances_assigned_for_my_approval(self):
        """Instance depends on the API version:

           * 2018-05-01-preview: :class:`AccessReviewInstancesAssignedForMyApprovalOperations<azure.mgmt.authorization.v2018_05_01_preview.aio.operations.AccessReviewInstancesAssignedForMyApprovalOperations>`
           * 2021-03-01-preview: :class:`AccessReviewInstancesAssignedForMyApprovalOperations<azure.mgmt.authorization.v2021_03_01_preview.aio.operations.AccessReviewInstancesAssignedForMyApprovalOperations>`
           * 2021-07-01-preview: :class:`AccessReviewInstancesAssignedForMyApprovalOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.AccessReviewInstancesAssignedForMyApprovalOperations>`
           * 2021-12-01-preview: :class:`AccessReviewInstancesAssignedForMyApprovalOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewInstancesAssignedForMyApprovalOperations>`
        """
        api_version = self._get_api_version('access_review_instances_assigned_for_my_approval')
        if api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview.aio.operations import AccessReviewInstancesAssignedForMyApprovalOperations as OperationClass
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview.aio.operations import AccessReviewInstancesAssignedForMyApprovalOperations as OperationClass
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import AccessReviewInstancesAssignedForMyApprovalOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewInstancesAssignedForMyApprovalOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_instances_assigned_for_my_approval'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_schedule_definitions(self):
        """Instance depends on the API version:

           * 2018-05-01-preview: :class:`AccessReviewScheduleDefinitionsOperations<azure.mgmt.authorization.v2018_05_01_preview.aio.operations.AccessReviewScheduleDefinitionsOperations>`
           * 2021-03-01-preview: :class:`AccessReviewScheduleDefinitionsOperations<azure.mgmt.authorization.v2021_03_01_preview.aio.operations.AccessReviewScheduleDefinitionsOperations>`
           * 2021-07-01-preview: :class:`AccessReviewScheduleDefinitionsOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.AccessReviewScheduleDefinitionsOperations>`
           * 2021-12-01-preview: :class:`AccessReviewScheduleDefinitionsOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewScheduleDefinitionsOperations>`
        """
        api_version = self._get_api_version('access_review_schedule_definitions')
        if api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview.aio.operations import AccessReviewScheduleDefinitionsOperations as OperationClass
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview.aio.operations import AccessReviewScheduleDefinitionsOperations as OperationClass
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import AccessReviewScheduleDefinitionsOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewScheduleDefinitionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_schedule_definitions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def access_review_schedule_definitions_assigned_for_my_approval(self):
        """Instance depends on the API version:

           * 2018-05-01-preview: :class:`AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations<azure.mgmt.authorization.v2018_05_01_preview.aio.operations.AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations>`
           * 2021-03-01-preview: :class:`AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations<azure.mgmt.authorization.v2021_03_01_preview.aio.operations.AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations>`
           * 2021-07-01-preview: :class:`AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations>`
           * 2021-12-01-preview: :class:`AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations>`
        """
        api_version = self._get_api_version('access_review_schedule_definitions_assigned_for_my_approval')
        if api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview.aio.operations import AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations as OperationClass
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview.aio.operations import AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations as OperationClass
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import AccessReviewScheduleDefinitionsAssignedForMyApprovalOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'access_review_schedule_definitions_assigned_for_my_approval'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def alert_configurations(self):
        """Instance depends on the API version:

           * 2022-08-01-preview: :class:`AlertConfigurationsOperations<azure.mgmt.authorization.v2022_08_01_preview.aio.operations.AlertConfigurationsOperations>`
        """
        api_version = self._get_api_version('alert_configurations')
        if api_version == '2022-08-01-preview':
            from ..v2022_08_01_preview.aio.operations import AlertConfigurationsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'alert_configurations'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def alert_definitions(self):
        """Instance depends on the API version:

           * 2022-08-01-preview: :class:`AlertDefinitionsOperations<azure.mgmt.authorization.v2022_08_01_preview.aio.operations.AlertDefinitionsOperations>`
        """
        api_version = self._get_api_version('alert_definitions')
        if api_version == '2022-08-01-preview':
            from ..v2022_08_01_preview.aio.operations import AlertDefinitionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'alert_definitions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def alert_incidents(self):
        """Instance depends on the API version:

           * 2022-08-01-preview: :class:`AlertIncidentsOperations<azure.mgmt.authorization.v2022_08_01_preview.aio.operations.AlertIncidentsOperations>`
        """
        api_version = self._get_api_version('alert_incidents')
        if api_version == '2022-08-01-preview':
            from ..v2022_08_01_preview.aio.operations import AlertIncidentsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'alert_incidents'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def alert_operation(self):
        """Instance depends on the API version:

           * 2022-08-01-preview: :class:`AlertOperationOperations<azure.mgmt.authorization.v2022_08_01_preview.aio.operations.AlertOperationOperations>`
        """
        api_version = self._get_api_version('alert_operation')
        if api_version == '2022-08-01-preview':
            from ..v2022_08_01_preview.aio.operations import AlertOperationOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'alert_operation'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def alerts(self):
        """Instance depends on the API version:

           * 2022-08-01-preview: :class:`AlertsOperations<azure.mgmt.authorization.v2022_08_01_preview.aio.operations.AlertsOperations>`
        """
        api_version = self._get_api_version('alerts')
        if api_version == '2022-08-01-preview':
            from ..v2022_08_01_preview.aio.operations import AlertsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'alerts'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def classic_administrators(self):
        """Instance depends on the API version:

           * 2015-06-01: :class:`ClassicAdministratorsOperations<azure.mgmt.authorization.v2015_06_01.aio.operations.ClassicAdministratorsOperations>`
           * 2015-07-01: :class:`ClassicAdministratorsOperations<azure.mgmt.authorization.v2015_07_01.aio.operations.ClassicAdministratorsOperations>`
        """
        api_version = self._get_api_version('classic_administrators')
        if api_version == '2015-06-01':
            from ..v2015_06_01.aio.operations import ClassicAdministratorsOperations as OperationClass
        elif api_version == '2015-07-01':
            from ..v2015_07_01.aio.operations import ClassicAdministratorsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'classic_administrators'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def deny_assignments(self):
        """Instance depends on the API version:

           * 2018-07-01-preview: :class:`DenyAssignmentsOperations<azure.mgmt.authorization.v2018_07_01_preview.aio.operations.DenyAssignmentsOperations>`
           * 2022-04-01: :class:`DenyAssignmentsOperations<azure.mgmt.authorization.v2022_04_01.aio.operations.DenyAssignmentsOperations>`
        """
        api_version = self._get_api_version('deny_assignments')
        if api_version == '2018-07-01-preview':
            from ..v2018_07_01_preview.aio.operations import DenyAssignmentsOperations as OperationClass
        elif api_version == '2022-04-01':
            from ..v2022_04_01.aio.operations import DenyAssignmentsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'deny_assignments'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def eligible_child_resources(self):
        """Instance depends on the API version:

           * 2020-10-01: :class:`EligibleChildResourcesOperations<azure.mgmt.authorization.v2020_10_01.aio.operations.EligibleChildResourcesOperations>`
           * 2020-10-01-preview: :class:`EligibleChildResourcesOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.EligibleChildResourcesOperations>`
        """
        api_version = self._get_api_version('eligible_child_resources')
        if api_version == '2020-10-01':
            from ..v2020_10_01.aio.operations import EligibleChildResourcesOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import EligibleChildResourcesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'eligible_child_resources'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def global_administrator(self):
        """Instance depends on the API version:

           * 2015-07-01: :class:`GlobalAdministratorOperations<azure.mgmt.authorization.v2015_07_01.aio.operations.GlobalAdministratorOperations>`
        """
        api_version = self._get_api_version('global_administrator')
        if api_version == '2015-07-01':
            from ..v2015_07_01.aio.operations import GlobalAdministratorOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'global_administrator'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def operations(self):
        """Instance depends on the API version:

           * 2018-05-01-preview: :class:`Operations<azure.mgmt.authorization.v2018_05_01_preview.aio.operations.Operations>`
           * 2021-01-01-preview: :class:`Operations<azure.mgmt.authorization.v2021_01_01_preview.aio.operations.Operations>`
           * 2021-03-01-preview: :class:`Operations<azure.mgmt.authorization.v2021_03_01_preview.aio.operations.Operations>`
           * 2021-07-01-preview: :class:`Operations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.Operations>`
           * 2021-12-01-preview: :class:`Operations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.Operations>`
        """
        api_version = self._get_api_version('operations')
        if api_version == '2018-05-01-preview':
            from ..v2018_05_01_preview.aio.operations import Operations as OperationClass
        elif api_version == '2021-01-01-preview':
            from ..v2021_01_01_preview.aio.operations import Operations as OperationClass
        elif api_version == '2021-03-01-preview':
            from ..v2021_03_01_preview.aio.operations import Operations as OperationClass
        elif api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import Operations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import Operations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'operations'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def permissions(self):
        """Instance depends on the API version:

           * 2015-07-01: :class:`PermissionsOperations<azure.mgmt.authorization.v2015_07_01.aio.operations.PermissionsOperations>`
           * 2018-01-01-preview: :class:`PermissionsOperations<azure.mgmt.authorization.v2018_01_01_preview.aio.operations.PermissionsOperations>`
           * 2022-04-01: :class:`PermissionsOperations<azure.mgmt.authorization.v2022_04_01.aio.operations.PermissionsOperations>`
           * 2022-05-01-preview: :class:`PermissionsOperations<azure.mgmt.authorization.v2022_05_01_preview.aio.operations.PermissionsOperations>`
        """
        api_version = self._get_api_version('permissions')
        if api_version == '2015-07-01':
            from ..v2015_07_01.aio.operations import PermissionsOperations as OperationClass
        elif api_version == '2018-01-01-preview':
            from ..v2018_01_01_preview.aio.operations import PermissionsOperations as OperationClass
        elif api_version == '2022-04-01':
            from ..v2022_04_01.aio.operations import PermissionsOperations as OperationClass
        elif api_version == '2022-05-01-preview':
            from ..v2022_05_01_preview.aio.operations import PermissionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'permissions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def provider_operations_metadata(self):
        """Instance depends on the API version:

           * 2015-07-01: :class:`ProviderOperationsMetadataOperations<azure.mgmt.authorization.v2015_07_01.aio.operations.ProviderOperationsMetadataOperations>`
           * 2018-01-01-preview: :class:`ProviderOperationsMetadataOperations<azure.mgmt.authorization.v2018_01_01_preview.aio.operations.ProviderOperationsMetadataOperations>`
           * 2022-04-01: :class:`ProviderOperationsMetadataOperations<azure.mgmt.authorization.v2022_04_01.aio.operations.ProviderOperationsMetadataOperations>`
        """
        api_version = self._get_api_version('provider_operations_metadata')
        if api_version == '2015-07-01':
            from ..v2015_07_01.aio.operations import ProviderOperationsMetadataOperations as OperationClass
        elif api_version == '2018-01-01-preview':
            from ..v2018_01_01_preview.aio.operations import ProviderOperationsMetadataOperations as OperationClass
        elif api_version == '2022-04-01':
            from ..v2022_04_01.aio.operations import ProviderOperationsMetadataOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'provider_operations_metadata'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_assignment_approval(self):
        """Instance depends on the API version:

           * 2021-01-01-preview: :class:`RoleAssignmentApprovalOperations<azure.mgmt.authorization.v2021_01_01_preview.aio.operations.RoleAssignmentApprovalOperations>`
        """
        api_version = self._get_api_version('role_assignment_approval')
        if api_version == '2021-01-01-preview':
            from ..v2021_01_01_preview.aio.operations import RoleAssignmentApprovalOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_assignment_approval'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_assignment_approval_step(self):
        """Instance depends on the API version:

           * 2021-01-01-preview: :class:`RoleAssignmentApprovalStepOperations<azure.mgmt.authorization.v2021_01_01_preview.aio.operations.RoleAssignmentApprovalStepOperations>`
        """
        api_version = self._get_api_version('role_assignment_approval_step')
        if api_version == '2021-01-01-preview':
            from ..v2021_01_01_preview.aio.operations import RoleAssignmentApprovalStepOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_assignment_approval_step'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_assignment_approval_steps(self):
        """Instance depends on the API version:

           * 2021-01-01-preview: :class:`RoleAssignmentApprovalStepsOperations<azure.mgmt.authorization.v2021_01_01_preview.aio.operations.RoleAssignmentApprovalStepsOperations>`
        """
        api_version = self._get_api_version('role_assignment_approval_steps')
        if api_version == '2021-01-01-preview':
            from ..v2021_01_01_preview.aio.operations import RoleAssignmentApprovalStepsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_assignment_approval_steps'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_assignment_metrics(self):
        """Instance depends on the API version:

           * 2019-08-01-preview: :class:`RoleAssignmentMetricsOperations<azure.mgmt.authorization.v2019_08_01_preview.aio.operations.RoleAssignmentMetricsOperations>`
        """
        api_version = self._get_api_version('role_assignment_metrics')
        if api_version == '2019-08-01-preview':
            from ..v2019_08_01_preview.aio.operations import RoleAssignmentMetricsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_assignment_metrics'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_assignment_schedule_instances(self):
        """Instance depends on the API version:

           * 2020-10-01: :class:`RoleAssignmentScheduleInstancesOperations<azure.mgmt.authorization.v2020_10_01.aio.operations.RoleAssignmentScheduleInstancesOperations>`
           * 2020-10-01-preview: :class:`RoleAssignmentScheduleInstancesOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.RoleAssignmentScheduleInstancesOperations>`
        """
        api_version = self._get_api_version('role_assignment_schedule_instances')
        if api_version == '2020-10-01':
            from ..v2020_10_01.aio.operations import RoleAssignmentScheduleInstancesOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import RoleAssignmentScheduleInstancesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_assignment_schedule_instances'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_assignment_schedule_requests(self):
        """Instance depends on the API version:

           * 2020-10-01: :class:`RoleAssignmentScheduleRequestsOperations<azure.mgmt.authorization.v2020_10_01.aio.operations.RoleAssignmentScheduleRequestsOperations>`
           * 2020-10-01-preview: :class:`RoleAssignmentScheduleRequestsOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.RoleAssignmentScheduleRequestsOperations>`
           * 2022-04-01-preview: :class:`RoleAssignmentScheduleRequestsOperations<azure.mgmt.authorization.v2022_04_01_preview.aio.operations.RoleAssignmentScheduleRequestsOperations>`
        """
        api_version = self._get_api_version('role_assignment_schedule_requests')
        if api_version == '2020-10-01':
            from ..v2020_10_01.aio.operations import RoleAssignmentScheduleRequestsOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import RoleAssignmentScheduleRequestsOperations as OperationClass
        elif api_version == '2022-04-01-preview':
            from ..v2022_04_01_preview.aio.operations import RoleAssignmentScheduleRequestsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_assignment_schedule_requests'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_assignment_schedules(self):
        """Instance depends on the API version:

           * 2020-10-01: :class:`RoleAssignmentSchedulesOperations<azure.mgmt.authorization.v2020_10_01.aio.operations.RoleAssignmentSchedulesOperations>`
           * 2020-10-01-preview: :class:`RoleAssignmentSchedulesOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.RoleAssignmentSchedulesOperations>`
        """
        api_version = self._get_api_version('role_assignment_schedules')
        if api_version == '2020-10-01':
            from ..v2020_10_01.aio.operations import RoleAssignmentSchedulesOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import RoleAssignmentSchedulesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_assignment_schedules'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_assignments(self):
        """Instance depends on the API version:

           * 2015-07-01: :class:`RoleAssignmentsOperations<azure.mgmt.authorization.v2015_07_01.aio.operations.RoleAssignmentsOperations>`
           * 2018-01-01-preview: :class:`RoleAssignmentsOperations<azure.mgmt.authorization.v2018_01_01_preview.aio.operations.RoleAssignmentsOperations>`
           * 2018-09-01-preview: :class:`RoleAssignmentsOperations<azure.mgmt.authorization.v2018_09_01_preview.aio.operations.RoleAssignmentsOperations>`
           * 2020-04-01-preview: :class:`RoleAssignmentsOperations<azure.mgmt.authorization.v2020_04_01_preview.aio.operations.RoleAssignmentsOperations>`
           * 2020-10-01-preview: :class:`RoleAssignmentsOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.RoleAssignmentsOperations>`
           * 2022-04-01: :class:`RoleAssignmentsOperations<azure.mgmt.authorization.v2022_04_01.aio.operations.RoleAssignmentsOperations>`
        """
        api_version = self._get_api_version('role_assignments')
        if api_version == '2015-07-01':
            from ..v2015_07_01.aio.operations import RoleAssignmentsOperations as OperationClass
        elif api_version == '2018-01-01-preview':
            from ..v2018_01_01_preview.aio.operations import RoleAssignmentsOperations as OperationClass
        elif api_version == '2018-09-01-preview':
            from ..v2018_09_01_preview.aio.operations import RoleAssignmentsOperations as OperationClass
        elif api_version == '2020-04-01-preview':
            from ..v2020_04_01_preview.aio.operations import RoleAssignmentsOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import RoleAssignmentsOperations as OperationClass
        elif api_version == '2022-04-01':
            from ..v2022_04_01.aio.operations import RoleAssignmentsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_assignments'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_definitions(self):
        """Instance depends on the API version:

           * 2015-07-01: :class:`RoleDefinitionsOperations<azure.mgmt.authorization.v2015_07_01.aio.operations.RoleDefinitionsOperations>`
           * 2018-01-01-preview: :class:`RoleDefinitionsOperations<azure.mgmt.authorization.v2018_01_01_preview.aio.operations.RoleDefinitionsOperations>`
           * 2022-04-01: :class:`RoleDefinitionsOperations<azure.mgmt.authorization.v2022_04_01.aio.operations.RoleDefinitionsOperations>`
           * 2022-05-01-preview: :class:`RoleDefinitionsOperations<azure.mgmt.authorization.v2022_05_01_preview.aio.operations.RoleDefinitionsOperations>`
        """
        api_version = self._get_api_version('role_definitions')
        if api_version == '2015-07-01':
            from ..v2015_07_01.aio.operations import RoleDefinitionsOperations as OperationClass
        elif api_version == '2018-01-01-preview':
            from ..v2018_01_01_preview.aio.operations import RoleDefinitionsOperations as OperationClass
        elif api_version == '2022-04-01':
            from ..v2022_04_01.aio.operations import RoleDefinitionsOperations as OperationClass
        elif api_version == '2022-05-01-preview':
            from ..v2022_05_01_preview.aio.operations import RoleDefinitionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_definitions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_eligibility_schedule_instances(self):
        """Instance depends on the API version:

           * 2020-10-01: :class:`RoleEligibilityScheduleInstancesOperations<azure.mgmt.authorization.v2020_10_01.aio.operations.RoleEligibilityScheduleInstancesOperations>`
           * 2020-10-01-preview: :class:`RoleEligibilityScheduleInstancesOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.RoleEligibilityScheduleInstancesOperations>`
        """
        api_version = self._get_api_version('role_eligibility_schedule_instances')
        if api_version == '2020-10-01':
            from ..v2020_10_01.aio.operations import RoleEligibilityScheduleInstancesOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import RoleEligibilityScheduleInstancesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_eligibility_schedule_instances'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_eligibility_schedule_requests(self):
        """Instance depends on the API version:

           * 2020-10-01: :class:`RoleEligibilityScheduleRequestsOperations<azure.mgmt.authorization.v2020_10_01.aio.operations.RoleEligibilityScheduleRequestsOperations>`
           * 2020-10-01-preview: :class:`RoleEligibilityScheduleRequestsOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.RoleEligibilityScheduleRequestsOperations>`
           * 2022-04-01-preview: :class:`RoleEligibilityScheduleRequestsOperations<azure.mgmt.authorization.v2022_04_01_preview.aio.operations.RoleEligibilityScheduleRequestsOperations>`
        """
        api_version = self._get_api_version('role_eligibility_schedule_requests')
        if api_version == '2020-10-01':
            from ..v2020_10_01.aio.operations import RoleEligibilityScheduleRequestsOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import RoleEligibilityScheduleRequestsOperations as OperationClass
        elif api_version == '2022-04-01-preview':
            from ..v2022_04_01_preview.aio.operations import RoleEligibilityScheduleRequestsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_eligibility_schedule_requests'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_eligibility_schedules(self):
        """Instance depends on the API version:

           * 2020-10-01: :class:`RoleEligibilitySchedulesOperations<azure.mgmt.authorization.v2020_10_01.aio.operations.RoleEligibilitySchedulesOperations>`
           * 2020-10-01-preview: :class:`RoleEligibilitySchedulesOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.RoleEligibilitySchedulesOperations>`
        """
        api_version = self._get_api_version('role_eligibility_schedules')
        if api_version == '2020-10-01':
            from ..v2020_10_01.aio.operations import RoleEligibilitySchedulesOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import RoleEligibilitySchedulesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_eligibility_schedules'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_management_policies(self):
        """Instance depends on the API version:

           * 2020-10-01: :class:`RoleManagementPoliciesOperations<azure.mgmt.authorization.v2020_10_01.aio.operations.RoleManagementPoliciesOperations>`
           * 2020-10-01-preview: :class:`RoleManagementPoliciesOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.RoleManagementPoliciesOperations>`
        """
        api_version = self._get_api_version('role_management_policies')
        if api_version == '2020-10-01':
            from ..v2020_10_01.aio.operations import RoleManagementPoliciesOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import RoleManagementPoliciesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_management_policies'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def role_management_policy_assignments(self):
        """Instance depends on the API version:

           * 2020-10-01: :class:`RoleManagementPolicyAssignmentsOperations<azure.mgmt.authorization.v2020_10_01.aio.operations.RoleManagementPolicyAssignmentsOperations>`
           * 2020-10-01-preview: :class:`RoleManagementPolicyAssignmentsOperations<azure.mgmt.authorization.v2020_10_01_preview.aio.operations.RoleManagementPolicyAssignmentsOperations>`
        """
        api_version = self._get_api_version('role_management_policy_assignments')
        if api_version == '2020-10-01':
            from ..v2020_10_01.aio.operations import RoleManagementPolicyAssignmentsOperations as OperationClass
        elif api_version == '2020-10-01-preview':
            from ..v2020_10_01_preview.aio.operations import RoleManagementPolicyAssignmentsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'role_management_policy_assignments'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_default_settings(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewDefaultSettingsOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewDefaultSettingsOperations>`
        """
        api_version = self._get_api_version('scope_access_review_default_settings')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewDefaultSettingsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_default_settings'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_history_definition(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewHistoryDefinitionOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewHistoryDefinitionOperations>`
        """
        api_version = self._get_api_version('scope_access_review_history_definition')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewHistoryDefinitionOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_history_definition'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_history_definition_instance(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewHistoryDefinitionInstanceOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewHistoryDefinitionInstanceOperations>`
        """
        api_version = self._get_api_version('scope_access_review_history_definition_instance')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewHistoryDefinitionInstanceOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_history_definition_instance'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_history_definition_instances(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewHistoryDefinitionInstancesOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewHistoryDefinitionInstancesOperations>`
        """
        api_version = self._get_api_version('scope_access_review_history_definition_instances')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewHistoryDefinitionInstancesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_history_definition_instances'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_history_definitions(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewHistoryDefinitionsOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewHistoryDefinitionsOperations>`
        """
        api_version = self._get_api_version('scope_access_review_history_definitions')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewHistoryDefinitionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_history_definitions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_instance(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewInstanceOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewInstanceOperations>`
        """
        api_version = self._get_api_version('scope_access_review_instance')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewInstanceOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_instance'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_instance_contacted_reviewers(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewInstanceContactedReviewersOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewInstanceContactedReviewersOperations>`
        """
        api_version = self._get_api_version('scope_access_review_instance_contacted_reviewers')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewInstanceContactedReviewersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_instance_contacted_reviewers'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_instance_decisions(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewInstanceDecisionsOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewInstanceDecisionsOperations>`
        """
        api_version = self._get_api_version('scope_access_review_instance_decisions')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewInstanceDecisionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_instance_decisions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_instances(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewInstancesOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewInstancesOperations>`
        """
        api_version = self._get_api_version('scope_access_review_instances')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewInstancesOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_instances'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_access_review_schedule_definitions(self):
        """Instance depends on the API version:

           * 2021-12-01-preview: :class:`ScopeAccessReviewScheduleDefinitionsOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.ScopeAccessReviewScheduleDefinitionsOperations>`
        """
        api_version = self._get_api_version('scope_access_review_schedule_definitions')
        if api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import ScopeAccessReviewScheduleDefinitionsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_access_review_schedule_definitions'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_role_assignment_approval(self):
        """Instance depends on the API version:

           * 2021-01-01-preview: :class:`ScopeRoleAssignmentApprovalOperations<azure.mgmt.authorization.v2021_01_01_preview.aio.operations.ScopeRoleAssignmentApprovalOperations>`
        """
        api_version = self._get_api_version('scope_role_assignment_approval')
        if api_version == '2021-01-01-preview':
            from ..v2021_01_01_preview.aio.operations import ScopeRoleAssignmentApprovalOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_role_assignment_approval'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_role_assignment_approval_step(self):
        """Instance depends on the API version:

           * 2021-01-01-preview: :class:`ScopeRoleAssignmentApprovalStepOperations<azure.mgmt.authorization.v2021_01_01_preview.aio.operations.ScopeRoleAssignmentApprovalStepOperations>`
        """
        api_version = self._get_api_version('scope_role_assignment_approval_step')
        if api_version == '2021-01-01-preview':
            from ..v2021_01_01_preview.aio.operations import ScopeRoleAssignmentApprovalStepOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_role_assignment_approval_step'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def scope_role_assignment_approval_steps(self):
        """Instance depends on the API version:

           * 2021-01-01-preview: :class:`ScopeRoleAssignmentApprovalStepsOperations<azure.mgmt.authorization.v2021_01_01_preview.aio.operations.ScopeRoleAssignmentApprovalStepsOperations>`
        """
        api_version = self._get_api_version('scope_role_assignment_approval_steps')
        if api_version == '2021-01-01-preview':
            from ..v2021_01_01_preview.aio.operations import ScopeRoleAssignmentApprovalStepsOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'scope_role_assignment_approval_steps'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    @property
    def tenant_level_access_review_instance_contacted_reviewers(self):
        """Instance depends on the API version:

           * 2021-07-01-preview: :class:`TenantLevelAccessReviewInstanceContactedReviewersOperations<azure.mgmt.authorization.v2021_07_01_preview.aio.operations.TenantLevelAccessReviewInstanceContactedReviewersOperations>`
           * 2021-12-01-preview: :class:`TenantLevelAccessReviewInstanceContactedReviewersOperations<azure.mgmt.authorization.v2021_12_01_preview.aio.operations.TenantLevelAccessReviewInstanceContactedReviewersOperations>`
        """
        api_version = self._get_api_version('tenant_level_access_review_instance_contacted_reviewers')
        if api_version == '2021-07-01-preview':
            from ..v2021_07_01_preview.aio.operations import TenantLevelAccessReviewInstanceContactedReviewersOperations as OperationClass
        elif api_version == '2021-12-01-preview':
            from ..v2021_12_01_preview.aio.operations import TenantLevelAccessReviewInstanceContactedReviewersOperations as OperationClass
        else:
            raise ValueError("API version {} does not have operation group 'tenant_level_access_review_instance_contacted_reviewers'".format(api_version))
        self._config.api_version = api_version
        return OperationClass(self._client, self._config, Serializer(self._models_dict(api_version)), Deserializer(self._models_dict(api_version)), api_version)

    async def close(self):
        await self._client.close()
    async def __aenter__(self):
        await self._client.__aenter__()
        return self
    async def __aexit__(self, *exc_details):
        await self._client.__aexit__(*exc_details)
