# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import Any, List, Optional, TYPE_CHECKING

from ... import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class ClassicAdministrator(_serialization.Model):
    """Classic Administrators.

    :ivar id: The ID of the administrator.
    :vartype id: str
    :ivar name: The name of the administrator.
    :vartype name: str
    :ivar type: The type of the administrator.
    :vartype type: str
    :ivar email_address: The email address of the administrator.
    :vartype email_address: str
    :ivar role: The role of the administrator.
    :vartype role: str
    """

    _attribute_map = {
        "id": {"key": "id", "type": "str"},
        "name": {"key": "name", "type": "str"},
        "type": {"key": "type", "type": "str"},
        "email_address": {"key": "properties.emailAddress", "type": "str"},
        "role": {"key": "properties.role", "type": "str"},
    }

    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        name: Optional[str] = None,
        type: Optional[str] = None,
        email_address: Optional[str] = None,
        role: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: The ID of the administrator.
        :paramtype id: str
        :keyword name: The name of the administrator.
        :paramtype name: str
        :keyword type: The type of the administrator.
        :paramtype type: str
        :keyword email_address: The email address of the administrator.
        :paramtype email_address: str
        :keyword role: The role of the administrator.
        :paramtype role: str
        """
        super().__init__(**kwargs)
        self.id = id
        self.name = name
        self.type = type
        self.email_address = email_address
        self.role = role


class ClassicAdministratorListResult(_serialization.Model):
    """ClassicAdministrator list result information.

    :ivar value: An array of administrators.
    :vartype value: list[~azure.mgmt.authorization.v2015_06_01.models.ClassicAdministrator]
    :ivar next_link: The URL to use for getting the next set of results.
    :vartype next_link: str
    """

    _attribute_map = {
        "value": {"key": "value", "type": "[ClassicAdministrator]"},
        "next_link": {"key": "nextLink", "type": "str"},
    }

    def __init__(
        self,
        *,
        value: Optional[List["_models.ClassicAdministrator"]] = None,
        next_link: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword value: An array of administrators.
        :paramtype value: list[~azure.mgmt.authorization.v2015_06_01.models.ClassicAdministrator]
        :keyword next_link: The URL to use for getting the next set of results.
        :paramtype next_link: str
        """
        super().__init__(**kwargs)
        self.value = value
        self.next_link = next_link


class ErrorAdditionalInfo(_serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: JSON
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(_serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.mgmt.authorization.v2015_06_01.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info:
     list[~azure.mgmt.authorization.v2015_06_01.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(_serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed
    operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.mgmt.authorization.v2015_06_01.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["_models.ErrorDetail"] = None, **kwargs: Any) -> None:
        """
        :keyword error: The error object.
        :paramtype error: ~azure.mgmt.authorization.v2015_06_01.models.ErrorDetail
        """
        super().__init__(**kwargs)
        self.error = error
