import json
from datetime import datetime
from pathlib import Path
from azure_collectors.azure_client import AzureClient
from utils.config import Config
from utils.email_notifier import EmailNotifier

class ExposedVMDetector:
    """Detector for VMs with exposed public IPs"""
    
    def __init__(self):
        self.azure_client = AzureClient()
        self.email_notifier = EmailNotifier()
        self.alerts_log = Config.ALERTS_LOG
        self.alerts_log.parent.mkdir(parents=True, exist_ok=True)
    
    def detect_exposed_vms(self):
        """Detect VMs with public IP addresses that may be exposed"""
        alerts = []
        
        try:
            # Get all VMs and public IPs
            vms = self.azure_client.get_virtual_machines()
            public_ips = self.azure_client.get_public_ips()
            nsgs = self.azure_client.get_network_security_groups()
            
            # Check for VMs with public IPs
            for pip in public_ips:
                if pip['ip_address'] and pip['associated_resource']:
                    # Check if associated with a VM
                    if '/virtualMachines/' in pip['associated_resource']:
                        vm_name = self._extract_vm_name(pip['associated_resource'])
                        
                        # Check NSG rules for this VM
                        risky_rules = self._check_nsg_rules(nsgs, pip['resource_group'])
                        
                        if risky_rules:
                            alert = {
                                'timestamp': datetime.utcnow().isoformat(),
                                'type': 'exposed_vm',
                                'vm_name': vm_name,
                                'public_ip': pip['ip_address'],
                                'resource_group': pip['resource_group'],
                                'risky_rules': risky_rules,
                                'severity': 'HIGH',
                                'message': f"VM {vm_name} has public IP {pip['ip_address']} with risky NSG rules"
                            }
                            alerts.append(alert)
                            
                            # Log alert
                            self._log_alert(alert)
                            
                            # Send email notification
                            self.email_notifier.send_alert('exposed_vm', alert)
            
            # Output results
            if alerts:
                print(f"\n🚨 EXPOSED VM ALERTS: {len(alerts)} found")
                for alert in alerts:
                    print(f"{alert['timestamp']} | {alert['message']}")
            else:
                print("✅ No exposed VMs with risky configurations detected.")
                
            return alerts
            
        except Exception as e:
            print(f"Error in exposed VM detection: {e}")
            return []
    
    def _extract_vm_name(self, resource_id):
        """Extract VM name from resource ID"""
        try:
            return resource_id.split('/virtualMachines/')[1].split('/')[0]
        except:
            return "unknown"
    
    def _check_nsg_rules(self, nsgs, resource_group):
        """Check for risky NSG rules"""
        risky_rules = []
        
        for nsg in nsgs:
            if nsg['resource_group'] == resource_group:
                for rule in nsg['security_rules']:
                    # Check for risky inbound rules
                    if (rule['direction'] == 'Inbound' and 
                        rule['access'] == 'Allow' and
                        rule['source_address_prefix'] in ['*', '0.0.0.0/0', 'Internet']):
                        
                        # Common risky ports
                        risky_ports = ['22', '3389', '1433', '3306', '5432', '27017']
                        
                        if (rule['destination_port_range'] in risky_ports or
                            rule['destination_port_range'] == '*'):
                            
                            risky_rules.append({
                                'nsg_name': nsg['name'],
                                'rule_name': rule['name'],
                                'port': rule['destination_port_range'],
                                'protocol': rule['protocol'],
                                'priority': rule['priority']
                            })
        
        return risky_rules
    
    def _log_alert(self, alert):
        """Log alert to file"""
        try:
            with open(self.alerts_log, 'a') as f:
                f.write(json.dumps(alert) + '\n')
        except Exception as e:
            print(f"Error logging alert: {e}")

def main():
    detector = ExposedVMDetector()
    detector.detect_exposed_vms()

if __name__ == "__main__":
    main()
