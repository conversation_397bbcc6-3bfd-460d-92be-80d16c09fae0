import json
from datetime import datetime, timedelta
from collections import defaultdict
from pathlib import Path
from utils.config import Config
from utils.email_notifier import EmailNotifier

class PortScanDetector:
    """Detector for port scanning activities"""
    
    def __init__(self):
        self.email_notifier = EmailNotifier()
        self.events_log = Config.EVENTS_LOG
        self.alerts_log = Config.ALERTS_LOG
        self.alerts_log.parent.mkdir(parents=True, exist_ok=True)
        
        # Port scan detection thresholds
        self.port_threshold = 5  # Number of different ports
        self.time_window = 300   # 5 minutes in seconds
        self.scan_threshold = 10 # Number of scan attempts
    
    def detect_port_scans(self):
        """Detect port scanning patterns in network events"""
        alerts = []
        
        if not self.events_log.exists():
            print("Events log file not found.")
            return alerts
        
        try:
            # Parse events and group by source IP
            ip_activities = defaultdict(list)
            
            with open(self.events_log, 'r') as f:
                for line in f:
                    try:
                        event = json.loads(line)
                        if event.get('type') == 'port_scan':
                            ip_activities[event.get('ip', 'unknown')].append(event)
                    except json.JSONDecodeError:
                        continue
            
            # Analyze each IP's activity
            for source_ip, events in ip_activities.items():
                if len(events) >= self.scan_threshold:
                    # Check for port scanning patterns
                    scan_analysis = self._analyze_scan_pattern(events)
                    
                    if scan_analysis['is_scan']:
                        alert = {
                            'timestamp': datetime.utcnow().isoformat(),
                            'type': 'port_scan',
                            'source_ip': source_ip,
                            'total_attempts': len(events),
                            'unique_ports': scan_analysis['unique_ports'],
                            'time_span_minutes': scan_analysis['time_span_minutes'],
                            'target_ports': scan_analysis['ports_scanned'],
                            'severity': self._calculate_severity(scan_analysis),
                            'message': f"Port scan detected from {source_ip}: {len(events)} attempts on {scan_analysis['unique_ports']} ports"
                        }
                        alerts.append(alert)
                        
                        # Log alert
                        self._log_alert(alert)
                        
                        # Send email notification
                        self.email_notifier.send_alert('port_scan', alert)
            
            # Output results
            if alerts:
                print(f"\n🚨 PORT SCAN ALERTS: {len(alerts)} found")
                for alert in alerts:
                    print(f"{alert['timestamp']} | {alert['message']}")
            else:
                print("✅ No port scanning activity detected.")
                
            return alerts
            
        except Exception as e:
            print(f"Error in port scan detection: {e}")
            return []
    
    def _analyze_scan_pattern(self, events):
        """Analyze events to determine if they represent a port scan"""
        if not events:
            return {'is_scan': False}
        
        # Extract timestamps and ports
        timestamps = []
        ports = set()
        
        for event in events:
            try:
                timestamp = datetime.fromisoformat(event['timestamp'].replace('Z', '+00:00'))
                timestamps.append(timestamp)
                ports.add(event.get('port', 0))
            except:
                continue
        
        if not timestamps:
            return {'is_scan': False}
        
        # Calculate time span
        timestamps.sort()
        time_span = (timestamps[-1] - timestamps[0]).total_seconds()
        time_span_minutes = time_span / 60
        
        # Determine if this is a scan
        unique_ports = len(ports)
        is_scan = (
            unique_ports >= self.port_threshold or
            (len(events) >= self.scan_threshold and time_span <= self.time_window)
        )
        
        return {
            'is_scan': is_scan,
            'unique_ports': unique_ports,
            'time_span_minutes': round(time_span_minutes, 2),
            'ports_scanned': sorted(list(ports)),
            'total_events': len(events)
        }
    
    def _calculate_severity(self, analysis):
        """Calculate severity based on scan characteristics"""
        if analysis['unique_ports'] >= 20:
            return 'CRITICAL'
        elif analysis['unique_ports'] >= 10:
            return 'HIGH'
        elif analysis['time_span_minutes'] <= 1:
            return 'HIGH'
        else:
            return 'MEDIUM'
    
    def _log_alert(self, alert):
        """Log alert to file"""
        try:
            with open(self.alerts_log, 'a') as f:
                f.write(json.dumps(alert) + '\n')
        except Exception as e:
            print(f"Error logging alert: {e}")

def main():
    detector = PortScanDetector()
    detector.detect_port_scans()

if __name__ == "__main__":
    main()
