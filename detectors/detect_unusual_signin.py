import json
import requests
from datetime import datetime, timedelta
from collections import defaultdict
from pathlib import Path
from utils.config import Config
from utils.email_notifier import EmailNotifier

class UnusualSignInDetector:
    """Detector for unusual sign-in locations"""
    
    def __init__(self):
        self.email_notifier = EmailNotifier()
        self.events_log = Config.EVENTS_LOG
        self.alerts_log = Config.ALERTS_LOG
        self.alerts_log.parent.mkdir(parents=True, exist_ok=True)
        self.location_threshold_km = Config.UNUSUAL_LOCATION_THRESHOLD_KM
        
        # User location history (in production, this would be in a database)
        self.user_locations = defaultdict(list)
    
    def detect_unusual_signins(self):
        """Detect sign-ins from unusual locations"""
        alerts = []
        
        if not self.events_log.exists():
            print("Events log file not found.")
            return alerts
        
        try:
            # Parse sign-in events
            signin_events = []
            
            with open(self.events_log, 'r') as f:
                for line in f:
                    try:
                        event = json.loads(line)
                        if event.get('type') in ['failed_login', 'successful_login']:
                            signin_events.append(event)
                    except json.JSONDecodeError:
                        continue
            
            # Group by user and analyze locations
            user_events = defaultdict(list)
            for event in signin_events:
                user = event.get('user', 'unknown')
                user_events[user].append(event)
            
            # Analyze each user's sign-in patterns
            for user, events in user_events.items():
                unusual_signins = self._analyze_user_locations(user, events)
                
                for unusual_signin in unusual_signins:
                    alert = {
                        'timestamp': datetime.utcnow().isoformat(),
                        'type': 'unusual_signin',
                        'user': user,
                        'ip_address': unusual_signin['ip'],
                        'location': unusual_signin['location'],
                        'distance_km': unusual_signin['distance_km'],
                        'previous_location': unusual_signin['previous_location'],
                        'time_difference_hours': unusual_signin['time_diff_hours'],
                        'severity': self._calculate_severity(unusual_signin),
                        'message': f"Unusual sign-in for {user} from {unusual_signin['location']} ({unusual_signin['distance_km']}km from usual location)"
                    }
                    alerts.append(alert)
                    
                    # Log alert
                    self._log_alert(alert)
                    
                    # Send email notification (to both admin and user)
                    self.email_notifier.send_alert('unusual_signin', alert, user_email=user)
            
            # Output results
            if alerts:
                print(f"\n🚨 UNUSUAL SIGN-IN ALERTS: {len(alerts)} found")
                for alert in alerts:
                    print(f"{alert['timestamp']} | {alert['message']}")
            else:
                print("✅ No unusual sign-in locations detected.")
                
            return alerts
            
        except Exception as e:
            print(f"Error in unusual sign-in detection: {e}")
            return []
    
    def _analyze_user_locations(self, user, events):
        """Analyze user's sign-in locations for anomalies"""
        unusual_signins = []
        
        # Sort events by timestamp
        events.sort(key=lambda x: x.get('timestamp', ''))
        
        for i, event in enumerate(events):
            ip = event.get('ip', '')
            timestamp = event.get('timestamp', '')
            
            if not ip or not timestamp:
                continue
            
            # Get location for this IP
            location = self._get_ip_location(ip)
            if not location:
                continue
            
            # Check against user's previous locations
            if user in self.user_locations:
                for prev_location in self.user_locations[user]:
                    distance = self._calculate_distance(location, prev_location['location'])
                    time_diff = self._calculate_time_difference(timestamp, prev_location['timestamp'])
                    
                    # Check if this is an unusual location
                    if distance > self.location_threshold_km:
                        # Check if travel time is realistic (impossible travel)
                        max_speed_kmh = 1000  # Maximum realistic travel speed (including flights)
                        required_time_hours = distance / max_speed_kmh
                        
                        if time_diff < required_time_hours:
                            unusual_signins.append({
                                'ip': ip,
                                'location': location,
                                'distance_km': round(distance, 2),
                                'previous_location': prev_location['location'],
                                'time_diff_hours': round(time_diff, 2),
                                'impossible_travel': True
                            })
                        elif distance > self.location_threshold_km * 2:  # Very far from usual location
                            unusual_signins.append({
                                'ip': ip,
                                'location': location,
                                'distance_km': round(distance, 2),
                                'previous_location': prev_location['location'],
                                'time_diff_hours': round(time_diff, 2),
                                'impossible_travel': False
                            })
            
            # Add this location to user's history
            self.user_locations[user].append({
                'location': location,
                'timestamp': timestamp,
                'ip': ip
            })
            
            # Keep only recent locations (last 30 days)
            self._cleanup_old_locations(user)
        
        return unusual_signins
    
    def _get_ip_location(self, ip):
        """Get geographic location for IP address"""
        try:
            # Use a free IP geolocation service
            response = requests.get(f"http://ip-api.com/json/{ip}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    return {
                        'city': data.get('city', 'Unknown'),
                        'country': data.get('country', 'Unknown'),
                        'lat': data.get('lat', 0),
                        'lon': data.get('lon', 0)
                    }
        except:
            pass
        
        # Fallback to mock data for testing
        mock_locations = {
            '***********': {'city': 'Seattle', 'country': 'USA', 'lat': 47.6062, 'lon': -122.3321},
            '********': {'city': 'New York', 'country': 'USA', 'lat': 40.7128, 'lon': -74.0060},
            '*********': {'city': 'London', 'country': 'UK', 'lat': 51.5074, 'lon': -0.1278},
            '*********': {'city': 'Moscow', 'country': 'Russia', 'lat': 55.7558, 'lon': 37.6176}
        }
        
        return mock_locations.get(ip, {'city': 'Unknown', 'country': 'Unknown', 'lat': 0, 'lon': 0})
    
    def _calculate_distance(self, loc1, loc2):
        """Calculate distance between two locations in kilometers"""
        import math
        
        lat1, lon1 = math.radians(loc1['lat']), math.radians(loc1['lon'])
        lat2, lon2 = math.radians(loc2['lat']), math.radians(loc2['lon'])
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Earth's radius in kilometers
        r = 6371
        
        return r * c
    
    def _calculate_time_difference(self, timestamp1, timestamp2):
        """Calculate time difference in hours"""
        try:
            dt1 = datetime.fromisoformat(timestamp1.replace('Z', '+00:00'))
            dt2 = datetime.fromisoformat(timestamp2.replace('Z', '+00:00'))
            return abs((dt1 - dt2).total_seconds()) / 3600
        except:
            return 0
    
    def _calculate_severity(self, signin_data):
        """Calculate severity based on sign-in characteristics"""
        if signin_data.get('impossible_travel'):
            return 'CRITICAL'
        elif signin_data['distance_km'] > 5000:
            return 'HIGH'
        elif signin_data['distance_km'] > 1000:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _cleanup_old_locations(self, user):
        """Remove old location data"""
        cutoff_date = datetime.utcnow() - timedelta(days=30)
        
        self.user_locations[user] = [
            loc for loc in self.user_locations[user]
            if datetime.fromisoformat(loc['timestamp'].replace('Z', '+00:00')) > cutoff_date
        ]
    
    def _log_alert(self, alert):
        """Log alert to file"""
        try:
            with open(self.alerts_log, 'a') as f:
                f.write(json.dumps(alert) + '\n')
        except Exception as e:
            print(f"Error logging alert: {e}")

def main():
    detector = UnusualSignInDetector()
    detector.detect_unusual_signins()

if __name__ == "__main__":
    main()
