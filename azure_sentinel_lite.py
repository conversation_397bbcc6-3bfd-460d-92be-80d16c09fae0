#!/usr/bin/env python3
"""
Azure Sentinel Lite - Real-time Security Detection System
A comprehensive security monitoring system for Azure environments
"""

import sys
import time
import json
from datetime import datetime
from pathlib import Path
import argparse

# Add local modules to path
sys.path.append(str(Path(__file__).parent))

from utils.config import Config
from utils.email_notifier import EmailNotifier
from detectors.detect_port_scan import PortScanDetector
from detectors.detect_exposed_vm import ExposedVMDetector
from detectors.detect_unusual_signin import UnusualSignInDetector
from detectors.detect_elevated_activity import ElevatedActivityDetector
from simulator.detections.detect_role_escalation import PrivilegeEscalationDetector

class AzureSentinelLite:
    """Main orchestrator for Azure security detection system"""
    
    def __init__(self):
        print("🔒 Initializing Azure Sentinel Lite...")
        
        try:
            # Validate configuration
            Config.validate()
            print("✅ Configuration validated")
        except ValueError as e:
            print(f"❌ Configuration error: {e}")
            print("Please check your config.env file and ensure all required fields are set.")
            sys.exit(1)
        
        # Initialize detectors
        self.detectors = {
            'port_scan': PortScanDetector(),
            'exposed_vm': ExposedVMDetector(),
            'unusual_signin': UnusualSignInDetector(),
            'elevated_activity': ElevatedActivityDetector(),
            'privilege_escalation': PrivilegeEscalationDetector()
        }
        
        self.email_notifier = EmailNotifier()
        
        # Ensure log directories exist
        Config.LOG_DIR.mkdir(parents=True, exist_ok=True)
        
        print("✅ Azure Sentinel Lite initialized successfully")
    
    def run_all_detectors(self):
        """Run all security detectors"""
        print("\n🔍 Running security detection scan...")
        
        all_alerts = []
        detection_summary = {}
        
        for detector_name, detector in self.detectors.items():
            print(f"\n📊 Running {detector_name.replace('_', ' ').title()} Detector...")
            
            try:
                if detector_name == 'port_scan':
                    alerts = detector.detect_port_scans()
                elif detector_name == 'exposed_vm':
                    alerts = detector.detect_exposed_vms()
                elif detector_name == 'unusual_signin':
                    alerts = detector.detect_unusual_signins()
                elif detector_name == 'elevated_activity':
                    alerts = detector.detect_elevated_activity()
                elif detector_name == 'privilege_escalation':
                    alerts = detector.detect_privilege_escalation()
                else:
                    alerts = []
                
                detection_summary[detector_name] = len(alerts)
                all_alerts.extend(alerts)
                
            except Exception as e:
                print(f"❌ Error in {detector_name} detector: {e}")
                detection_summary[detector_name] = 0
        
        # Generate summary report
        self._generate_summary_report(detection_summary, all_alerts)
        
        return all_alerts
    
    def run_single_detector(self, detector_name):
        """Run a specific detector"""
        if detector_name not in self.detectors:
            print(f"❌ Unknown detector: {detector_name}")
            print(f"Available detectors: {', '.join(self.detectors.keys())}")
            return []
        
        print(f"\n🔍 Running {detector_name.replace('_', ' ').title()} Detector...")
        
        detector = self.detectors[detector_name]
        
        try:
            if detector_name == 'port_scan':
                return detector.detect_port_scans()
            elif detector_name == 'exposed_vm':
                return detector.detect_exposed_vms()
            elif detector_name == 'unusual_signin':
                return detector.detect_unusual_signins()
            elif detector_name == 'elevated_activity':
                return detector.detect_elevated_activity()
            elif detector_name == 'privilege_escalation':
                return detector.detect_privilege_escalation()
        except Exception as e:
            print(f"❌ Error in {detector_name} detector: {e}")
            return []
    
    def monitor_continuous(self, interval_minutes=5):
        """Run continuous monitoring"""
        print(f"\n🔄 Starting continuous monitoring (interval: {interval_minutes} minutes)")
        print("Press Ctrl+C to stop monitoring")
        
        try:
            while True:
                print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Running detection cycle...")
                
                alerts = self.run_all_detectors()
                
                if alerts:
                    print(f"🚨 {len(alerts)} total alerts generated this cycle")
                else:
                    print("✅ No alerts generated this cycle")
                
                print(f"💤 Sleeping for {interval_minutes} minutes...")
                time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
    
    def _generate_summary_report(self, detection_summary, all_alerts):
        """Generate and display summary report"""
        print("\n" + "="*60)
        print("📋 AZURE SENTINEL LITE - DETECTION SUMMARY")
        print("="*60)
        
        total_alerts = len(all_alerts)
        print(f"🚨 Total Alerts: {total_alerts}")
        
        if total_alerts > 0:
            # Count by severity
            severity_counts = {}
            for alert in all_alerts:
                severity = alert.get('severity', 'UNKNOWN')
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            print("\n📊 Alerts by Severity:")
            for severity, count in sorted(severity_counts.items()):
                print(f"   {severity}: {count}")
            
            # Count by detector
            print("\n🔍 Alerts by Detector:")
            for detector, count in detection_summary.items():
                if count > 0:
                    print(f"   {detector.replace('_', ' ').title()}: {count}")
            
            # Show recent critical alerts
            critical_alerts = [a for a in all_alerts if a.get('severity') == 'CRITICAL']
            if critical_alerts:
                print(f"\n🔥 CRITICAL ALERTS ({len(critical_alerts)}):")
                for alert in critical_alerts[-5:]:  # Show last 5
                    print(f"   • {alert.get('message', 'No message')}")
        
        print("\n📁 Log Files:")
        print(f"   Events: {Config.EVENTS_LOG}")
        print(f"   Alerts: {Config.ALERTS_LOG}")
        
        print("="*60)
    
    def show_recent_alerts(self, count=10):
        """Show recent alerts from log file"""
        if not Config.ALERTS_LOG.exists():
            print("No alerts log file found.")
            return
        
        print(f"\n📋 Last {count} Alerts:")
        print("-" * 50)
        
        try:
            with open(Config.ALERTS_LOG, 'r') as f:
                lines = f.readlines()
                
            recent_lines = lines[-count:] if len(lines) >= count else lines
            
            for line in recent_lines:
                try:
                    alert = json.loads(line)
                    timestamp = alert.get('timestamp', 'Unknown')
                    alert_type = alert.get('type', 'Unknown')
                    severity = alert.get('severity', 'Unknown')
                    message = alert.get('message', 'No message')
                    
                    print(f"{timestamp} | {severity} | {alert_type} | {message}")
                except json.JSONDecodeError:
                    continue
                    
        except Exception as e:
            print(f"Error reading alerts: {e}")

def main():
    parser = argparse.ArgumentParser(description='Azure Sentinel Lite - Security Detection System')
    parser.add_argument('--detector', '-d', 
                       choices=['port_scan', 'exposed_vm', 'unusual_signin', 'elevated_activity', 'privilege_escalation'],
                       help='Run specific detector only')
    parser.add_argument('--monitor', '-m', action='store_true',
                       help='Run continuous monitoring')
    parser.add_argument('--interval', '-i', type=int, default=5,
                       help='Monitoring interval in minutes (default: 5)')
    parser.add_argument('--recent', '-r', type=int, metavar='N',
                       help='Show N recent alerts')
    parser.add_argument('--simulate', '-s', action='store_true',
                       help='Generate sample threat data first')
    
    args = parser.parse_args()
    
    # Initialize the system
    sentinel = AzureSentinelLite()
    
    # Generate sample data if requested
    if args.simulate:
        print("🎭 Generating sample threat data...")
        from simulator.simulate_threats import simulate_events, simulate_attack_scenario
        simulate_events(20)
        simulate_attack_scenario()
        print("✅ Sample data generated")
    
    # Show recent alerts if requested
    if args.recent:
        sentinel.show_recent_alerts(args.recent)
        return
    
    # Run specific detector if requested
    if args.detector:
        sentinel.run_single_detector(args.detector)
        return
    
    # Run continuous monitoring if requested
    if args.monitor:
        sentinel.monitor_continuous(args.interval)
        return
    
    # Default: run all detectors once
    sentinel.run_all_detectors()

if __name__ == "__main__":
    main()
