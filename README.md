# Azure Sentinel Lite 🔒

A comprehensive real-time security detection system for Azure environments that monitors for various threats and sends email alerts.

## Features

### 🔍 Detection Capabilities
- **Port Scan Detection** - Identifies network reconnaissance activities
- **Exposed VM Detection** - Finds VMs with risky public IP configurations
- **Unusual Sign-in Location** - Detects logins from suspicious geographic locations
- **Elevated User Activity** - Monitors privileged user actions for anomalies
- **Privilege Escalation** - Tracks unauthorized role changes and permission increases

### 📧 Alert System
- **Email Notifications** - Automatic alerts sent to admin and affected users
- **Severity Classification** - CRITICAL, HIGH, MEDIUM, LOW risk levels
- **Detailed Reports** - Comprehensive alert information with recommendations
- **Log Files** - Structured logging for audit and analysis

### 🎯 Azure Integration
- **Real Azure Data** - Connects to Azure APIs for live monitoring
- **RBAC Monitoring** - Tracks role-based access control changes
- **Network Security** - Monitors NSG rules and network configurations
- **Resource Access** - Tracks access to sensitive Azure resources

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd azure-sentinel-lite

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Copy and edit the configuration file:

```bash
cp config.env.example config.env
```

Edit `config.env` with your settings:

```env
# Azure Configuration
AZURE_SUBSCRIPTION_ID=your_subscription_id_here
AZURE_TENANT_ID=your_tenant_id_here
AZURE_CLIENT_ID=your_client_id_here
AZURE_CLIENT_SECRET=your_client_secret_here

# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
ADMIN_EMAIL=<EMAIL>
```

### 3. Azure Service Principal Setup

Create a service principal with appropriate permissions:

```bash
# Create service principal
az ad sp create-for-rbac --name "azure-sentinel-lite" --role "Security Reader" --scopes "/subscriptions/YOUR_SUBSCRIPTION_ID"

# Additional permissions needed:
# - Reader (for resource enumeration)
# - Security Reader (for security-related data)
# - Monitor Reader (for activity logs)
```

### 4. Run Detection

```bash
# Run all detectors once
python azure_sentinel_lite.py

# Run specific detector
python azure_sentinel_lite.py --detector port_scan

# Continuous monitoring (every 5 minutes)
python azure_sentinel_lite.py --monitor

# Generate sample data and run detection
python azure_sentinel_lite.py --simulate
```

## Usage Examples

### Basic Detection Scan
```bash
python azure_sentinel_lite.py
```

### Continuous Monitoring
```bash
# Monitor every 5 minutes (default)
python azure_sentinel_lite.py --monitor

# Monitor every 10 minutes
python azure_sentinel_lite.py --monitor --interval 10
```

### Specific Detector
```bash
# Check for port scans only
python azure_sentinel_lite.py --detector port_scan

# Check for exposed VMs only
python azure_sentinel_lite.py --detector exposed_vm
```

### View Recent Alerts
```bash
# Show last 10 alerts
python azure_sentinel_lite.py --recent 10
```

## Detection Details

### 🌐 Port Scan Detection
- Monitors network traffic for scanning patterns
- Detects multiple port probes from single source
- Identifies reconnaissance activities
- **Triggers**: 5+ different ports or 10+ scan attempts

### 🖥️ Exposed VM Detection
- Scans for VMs with public IP addresses
- Checks Network Security Group (NSG) rules
- Identifies risky port exposures (SSH, RDP, databases)
- **Triggers**: Public IP + risky NSG rules

### 🌍 Unusual Sign-in Location
- Tracks user login locations using IP geolocation
- Detects impossible travel scenarios
- Identifies logins from high-risk countries
- **Triggers**: >500km from usual location or impossible travel time

### 👑 Elevated Activity Monitoring
- Monitors privileged user actions
- Tracks after-hours administrative activity
- Detects high-volume privilege operations
- **Triggers**: Rapid role changes, after-hours activity, high volume

### ⬆️ Privilege Escalation Detection
- Monitors Azure RBAC role assignments
- Tracks permission increases
- Risk-scores role changes
- **Triggers**: Escalation to Owner, Global Admin, or high-risk roles

## Email Alerts

When threats are detected, the system automatically sends email alerts containing:

- **Alert Type** and severity level
- **Detailed Information** about the threat
- **Affected Resources** and users
- **Recommended Actions** for remediation
- **Timestamp** and context

### Email Recipients
- **Admin Email**: Always receives all alerts
- **User Email**: Receives alerts related to their account (when available)

## Log Files

The system maintains structured logs:

- `logs/events_log.jsonl` - Raw security events
- `logs/alerts.jsonl` - Generated security alerts

## Configuration Options

### Detection Thresholds
```env
ALERT_THRESHOLD_MINUTES=60          # Alert aggregation window
MAX_FAILED_LOGINS=5                 # Failed login threshold
UNUSUAL_LOCATION_THRESHOLD_KM=500   # Distance threshold for unusual locations
```

### Email Settings
```env
SMTP_SERVER=smtp.gmail.com          # SMTP server
SMTP_PORT=587                       # SMTP port
EMAIL_USERNAME=<EMAIL> # Sender email
EMAIL_PASSWORD=your_app_password    # App password (not regular password)
ADMIN_EMAIL=<EMAIL>       # Admin notification email
```

## Testing

### Generate Sample Data
```bash
# Generate sample threat events
python simulator/simulate_threats.py

# Run detection on sample data
python azure_sentinel_lite.py --simulate
```

### Test Individual Detectors
```bash
# Test each detector individually
python detectors/detect_port_scan.py
python detectors/detect_exposed_vm.py
python detectors/detect_unusual_signin.py
python detectors/detect_elevated_activity.py
python simulator/detections/detect_role_escalation.py
```

## Troubleshooting

### Common Issues

1. **Azure Authentication Errors**
   - Verify service principal credentials
   - Check subscription ID and tenant ID
   - Ensure proper RBAC permissions

2. **Email Delivery Issues**
   - Use app passwords for Gmail (not regular password)
   - Check SMTP server and port settings
   - Verify firewall allows SMTP traffic

3. **No Alerts Generated**
   - Run with `--simulate` to generate test data
   - Check log files for events
   - Verify detection thresholds in config

### Debug Mode
```bash
# Enable verbose logging
export LOG_LEVEL=DEBUG
python azure_sentinel_lite.py
```

## Security Considerations

- Store credentials securely (use Azure Key Vault in production)
- Limit service principal permissions to minimum required
- Regularly rotate authentication credentials
- Monitor the monitoring system itself for tampering
- Use encrypted connections for all communications

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new detectors
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**⚠️ Important**: This is a security monitoring tool. Ensure you have proper authorization before deploying in any environment.
