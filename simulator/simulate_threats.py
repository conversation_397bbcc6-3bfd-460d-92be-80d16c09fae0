import json
import random
from datetime import datetime, timedelta
from pathlib import Path

# Output path
log_file = Path("../logs/events_log.jsonl")
log_file.parent.mkdir(parents=True, exist_ok=True)

# Enhanced simulation data for Azure environment
fake_ips = [
    "***********", "********", "*********", "*********",
    "**************", "************", "**************",  # Suspicious IPs
    "***********", "********"  # Internal IPs
]

fake_users = [
    "<EMAIL>", "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>",
    "<EMAIL>", "<EMAIL>"
]

azure_roles = [
    "Reader", "Storage Blob Data Reader", "Storage Blob Data Contributor",
    "Contributor", "User Access Administrator", "Owner",
    "Security Administrator", "Global Administrator",
    "Privileged Role Administrator", "Application Administrator"
]

vm_names = [
    "web-server-01", "db-server-prod", "app-gateway-01",
    "jump-box", "domain-controller", "file-server-01"
]

# Enhanced event generation with Azure-specific scenarios
def generate_event():
    event_types = [
        "failed_login", "successful_login", "port_scan", "role_escalation",
        "vm_exposed", "unusual_signin", "elevated_activity",
        "resource_access", "network_change"
    ]

    # Weight certain events to be more common
    weights = [15, 10, 8, 5, 3, 4, 6, 12, 7]
    event_type = random.choices(event_types, weights=weights)[0]

    # Generate timestamp with some variation (last 24 hours)
    base_time = datetime.utcnow()
    time_offset = timedelta(hours=random.uniform(-24, 0))
    timestamp = (base_time + time_offset).isoformat()

    if event_type == "failed_login":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "user": random.choice(fake_users),
            "ip": random.choice(fake_ips),
            "user_agent": random.choice([
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
                "Python-requests/2.28.1",  # Suspicious
                "curl/7.68.0"  # Suspicious
            ]),
            "failure_reason": random.choice([
                "Invalid password", "Account locked", "MFA required",
                "Account disabled", "Invalid username"
            ]),
            "message": "Login failed for user"
        }

    elif event_type == "successful_login":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "user": random.choice(fake_users),
            "ip": random.choice(fake_ips),
            "location": random.choice([
                "Seattle, WA, US", "New York, NY, US", "London, UK",
                "Moscow, Russia", "Beijing, China", "Unknown"
            ]),
            "device": random.choice([
                "Windows 10", "macOS", "iOS", "Android", "Linux"
            ]),
            "message": "Successful login"
        }

    elif event_type == "port_scan":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "ip": random.choice(fake_ips),
            "target_ip": random.choice(["*********", "*********", "*********"]),
            "port": random.choice([22, 3389, 1433, 3306, 5432, 8080, 443, 80, 21, 23]),
            "protocol": random.choice(["TCP", "UDP"]),
            "scan_type": random.choice(["SYN", "Connect", "UDP", "FIN"]),
            "message": "Port probe detected"
        }

    elif event_type == "role_escalation":
        prev_role = random.choice(azure_roles[:6])  # Lower privilege roles
        new_role = random.choice(azure_roles[3:])   # Higher privilege roles

        return {
            "timestamp": timestamp,
            "type": event_type,
            "user": random.choice(fake_users),
            "previous_role": prev_role,
            "new_role": new_role,
            "scope": random.choice([
                "/subscriptions/12345678-1234-1234-1234-123456789012",
                "/subscriptions/12345678-1234-1234-1234-123456789012/resourceGroups/production",
                "/subscriptions/12345678-1234-1234-1234-123456789012/resourceGroups/development"
            ]),
            "assigned_by": random.choice(fake_users),
            "message": f"Role change detected: {prev_role} → {new_role}"
        }

    elif event_type == "vm_exposed":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "vm_name": random.choice(vm_names),
            "public_ip": f"{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}",
            "exposed_ports": random.sample([22, 3389, 1433, 3306, 8080, 443], random.randint(1, 3)),
            "nsg_rule": f"allow-{random.choice(['ssh', 'rdp', 'web', 'db'])}-{random.randint(100, 999)}",
            "resource_group": random.choice(["production", "development", "staging"]),
            "message": "VM with public IP and open ports detected"
        }

    elif event_type == "unusual_signin":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "user": random.choice(fake_users),
            "ip": random.choice(fake_ips),
            "location": random.choice([
                "Moscow, Russia", "Beijing, China", "Lagos, Nigeria",
                "Unknown Location", "Tor Exit Node"
            ]),
            "previous_location": random.choice([
                "Seattle, WA, US", "New York, NY, US", "London, UK"
            ]),
            "distance_km": random.randint(500, 15000),
            "risk_score": random.randint(60, 100),
            "message": "Sign-in from unusual location detected"
        }

    elif event_type == "elevated_activity":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "user": random.choice([u for u in fake_users if 'admin' in u or 'security' in u]),
            "activity": random.choice([
                "Created new user account", "Modified security policy",
                "Accessed key vault", "Changed network rules",
                "Deleted resource group", "Modified RBAC assignments"
            ]),
            "resource": random.choice([
                "Key Vault", "Storage Account", "Virtual Network",
                "Resource Group", "Azure AD", "Security Center"
            ]),
            "risk_level": random.choice(["Medium", "High", "Critical"]),
            "message": "Elevated privilege activity detected"
        }

    elif event_type == "resource_access":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "user": random.choice(fake_users),
            "resource_type": random.choice([
                "Storage Account", "Key Vault", "SQL Database",
                "Virtual Machine", "App Service", "Function App"
            ]),
            "operation": random.choice([
                "Read", "Write", "Delete", "List", "Create", "Update"
            ]),
            "result": random.choice(["Success", "Failed", "Denied"]),
            "ip": random.choice(fake_ips),
            "message": "Resource access attempt"
        }

    elif event_type == "network_change":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "user": random.choice(fake_users),
            "change_type": random.choice([
                "NSG rule added", "NSG rule modified", "NSG rule deleted",
                "Route table modified", "VNet peering created",
                "Public IP associated", "Load balancer rule changed"
            ]),
            "resource": random.choice([
                "NetworkSecurityGroup", "RouteTable", "VirtualNetwork",
                "PublicIPAddress", "LoadBalancer"
            ]),
            "resource_group": random.choice(["production", "development", "staging"]),
            "message": "Network configuration change detected"
        }

# Generate a number of fake events
def simulate_events(num=10):
    with open(log_file, "a") as f:
        for _ in range(num):
            event = generate_event()
            f.write(json.dumps(event) + "\n")
    print(f"[+] Simulated {num} Azure security events → {log_file.resolve()}")

def simulate_attack_scenario():
    """Simulate a realistic attack scenario"""
    print("[+] Simulating realistic attack scenario...")

    attacker_ip = "**************"
    target_user = "<EMAIL>"

    # Phase 1: Reconnaissance (port scanning)
    for _ in range(5):
        event = {
            "timestamp": datetime.utcnow().isoformat(),
            "type": "port_scan",
            "ip": attacker_ip,
            "target_ip": "*********",
            "port": random.choice([22, 3389, 1433, 8080]),
            "protocol": "TCP",
            "scan_type": "SYN",
            "message": "Port probe detected"
        }
        with open(log_file, "a") as f:
            f.write(json.dumps(event) + "\n")

    # Phase 2: Failed login attempts
    for _ in range(8):
        event = {
            "timestamp": datetime.utcnow().isoformat(),
            "type": "failed_login",
            "user": target_user,
            "ip": attacker_ip,
            "user_agent": "Python-requests/2.28.1",
            "failure_reason": "Invalid password",
            "message": "Login failed for user"
        }
        with open(log_file, "a") as f:
            f.write(json.dumps(event) + "\n")

    # Phase 3: Successful compromise
    event = {
        "timestamp": datetime.utcnow().isoformat(),
        "type": "successful_login",
        "user": target_user,
        "ip": attacker_ip,
        "location": "Moscow, Russia",
        "device": "Linux",
        "message": "Successful login"
    }
    with open(log_file, "a") as f:
        f.write(json.dumps(event) + "\n")

    # Phase 4: Privilege escalation
    event = {
        "timestamp": datetime.utcnow().isoformat(),
        "type": "role_escalation",
        "user": target_user,
        "previous_role": "Reader",
        "new_role": "Contributor",
        "scope": "/subscriptions/12345678-1234-1234-1234-123456789012",
        "assigned_by": target_user,
        "message": "Role change detected: Reader → Contributor"
    }
    with open(log_file, "a") as f:
        f.write(json.dumps(event) + "\n")

    print("[+] Attack scenario simulation complete!")

if __name__ == "__main__":
    # Clear previous logs for fresh simulation
    if log_file.exists():
        log_file.unlink()

    # Simulate normal events
    simulate_events(30)

    # Simulate attack scenario
    simulate_attack_scenario()
