import json
import random
from datetime import datetime
from pathlib import Path

# Output path
log_file = Path("../logs/events_log.jsonl")
log_file.parent.mkdir(parents=True, exist_ok=True)

# Fake IP and usernames for simulation
fake_ips = ["***********", "********", "*********", "*********"]
fake_users = ["admin", "<EMAIL>", "<EMAIL>"]

# Types of simulated events
def generate_event():
    event_types = [
        "failed_login",
        "port_scan",
        "role_escalation"
    ]
    event_type = random.choice(event_types)
    timestamp = datetime.utcnow().isoformat()

    if event_type == "failed_login":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "user": random.choice(fake_users),
            "ip": random.choice(fake_ips),
            "message": "Login failed for user"
        }

    elif event_type == "port_scan":
        return {
            "timestamp": timestamp,
            "type": event_type,
            "ip": random.choice(fake_ips),
            "port": random.choice([22, 3389, 8080]),
            "message": "Port probe detected"
        }

    elif event_type == "role_escalation":
     previous_roles = ["Reader", "Storage Blob Data Reader", "None"]
     new_roles = ["Contributor", "User Access Administrator", "Owner"]

    prev_role = random.choice(previous_roles)
    new_role = random.choice(new_roles)
    
    return {
        "timestamp": timestamp,
        "type": event_type,
        "user": random.choice(fake_users),
        "previous_role": prev_role,
        "new_role": new_role,
        "message": f"Role change detected: {prev_role} → {new_role}"
    }


# Generate a number of fake events
def simulate_events(num=10):
    with open(log_file, "a") as f:
        for _ in range(num):
            event = generate_event()
            f.write(json.dumps(event) + "\n")
    print(f"[+] Simulated {num} events → {log_file.resolve()}")

if __name__ == "__main__":
    simulate_events(20)
