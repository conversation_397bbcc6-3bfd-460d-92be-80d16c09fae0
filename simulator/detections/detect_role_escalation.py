import json
from pathlib import Path

# Path to the log file
log_file = Path("../logs/events_log.jsonl")

# High-risk roles to monitor
privileged_roles = ["Owner", "Contributor", "User Access Administrator"]

def detect_privilege_escalation():
    alerts = []

    if not log_file.exists():
        print("Log file not found.")
        return

    with open(log_file, "r") as f:
        for line in f:
            try:
                event = json.loads(line)
            except json.JSONDecodeError:
                continue

            if event.get("type") == "role_escalation":
                new_role = event.get("new_role", "")
                prev_role = event.get("previous_role", "")
                user = event.get("user", "unknown")

                if new_role in privileged_roles:
                    alerts.append({
                        "user": user,
                        "previous_role": prev_role,
                        "new_role": new_role,
                        "timestamp": event["timestamp"],
                        "message": f"[ALERT] {user} escalated from {prev_role} to {new_role}"
                    })

    # Output results
    if alerts:
        print("\n🚨 PRIVILEGE ESCALATION ALERTS:")
        for alert in alerts:
            print(f"{alert['timestamp']} | {alert['message']}")
    else:
        print("✅ No privilege escalation detected.")

if __name__ == "__main__":
    detect_privilege_escalation()
