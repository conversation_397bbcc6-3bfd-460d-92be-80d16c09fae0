from azure.identity import ClientSecretCredential
from azure.mgmt.compute import ComputeManagementClient
from azure.mgmt.network import NetworkManagementClient
from azure.mgmt.monitor import MonitorManagementClient
from azure.mgmt.authorization import AuthorizationManagementClient
from azure.mgmt.resource import ResourceManagementClient
from utils.config import Config

class AzureClient:
    """Azure client wrapper for accessing Azure services"""
    
    def __init__(self):
        # Validate configuration
        Config.validate()
        
        # Create credential
        self.credential = ClientSecretCredential(
            tenant_id=Config.AZURE_TENANT_ID,
            client_id=Config.AZURE_CLIENT_ID,
            client_secret=Config.AZURE_CLIENT_SECRET
        )
        
        # Initialize clients
        self.subscription_id = Config.AZURE_SUBSCRIPTION_ID
        self.compute_client = ComputeManagementClient(self.credential, self.subscription_id)
        self.network_client = NetworkManagementClient(self.credential, self.subscription_id)
        self.monitor_client = MonitorManagementClient(self.credential, self.subscription_id)
        self.auth_client = AuthorizationManagementClient(self.credential, self.subscription_id)
        self.resource_client = ResourceManagementClient(self.credential, self.subscription_id)
    
    def get_virtual_machines(self):
        """Get all virtual machines in the subscription"""
        try:
            vms = []
            for vm in self.compute_client.virtual_machines.list_all():
                vms.append({
                    'name': vm.name,
                    'location': vm.location,
                    'resource_group': vm.id.split('/')[4],
                    'vm_size': vm.hardware_profile.vm_size if vm.hardware_profile else None,
                    'os_type': vm.storage_profile.os_disk.os_type if vm.storage_profile and vm.storage_profile.os_disk else None
                })
            return vms
        except Exception as e:
            print(f"Error getting VMs: {e}")
            return []
    
    def get_public_ips(self):
        """Get all public IP addresses"""
        try:
            public_ips = []
            for rg in self.resource_client.resource_groups.list():
                for pip in self.network_client.public_ip_addresses.list(rg.name):
                    public_ips.append({
                        'name': pip.name,
                        'resource_group': rg.name,
                        'ip_address': pip.ip_address,
                        'allocation_method': pip.public_ip_allocation_method,
                        'associated_resource': pip.ip_configuration.id if pip.ip_configuration else None
                    })
            return public_ips
        except Exception as e:
            print(f"Error getting public IPs: {e}")
            return []
    
    def get_network_security_groups(self):
        """Get all network security groups and their rules"""
        try:
            nsgs = []
            for rg in self.resource_client.resource_groups.list():
                for nsg in self.network_client.network_security_groups.list(rg.name):
                    nsg_data = {
                        'name': nsg.name,
                        'resource_group': rg.name,
                        'location': nsg.location,
                        'security_rules': []
                    }
                    
                    # Get security rules
                    if nsg.security_rules:
                        for rule in nsg.security_rules:
                            nsg_data['security_rules'].append({
                                'name': rule.name,
                                'priority': rule.priority,
                                'direction': rule.direction,
                                'access': rule.access,
                                'protocol': rule.protocol,
                                'source_port_range': rule.source_port_range,
                                'destination_port_range': rule.destination_port_range,
                                'source_address_prefix': rule.source_address_prefix,
                                'destination_address_prefix': rule.destination_address_prefix
                            })
                    
                    nsgs.append(nsg_data)
            return nsgs
        except Exception as e:
            print(f"Error getting NSGs: {e}")
            return []
    
    def get_role_assignments(self):
        """Get role assignments for the subscription"""
        try:
            assignments = []
            for assignment in self.auth_client.role_assignments.list():
                assignments.append({
                    'principal_id': assignment.principal_id,
                    'role_definition_id': assignment.role_definition_id,
                    'scope': assignment.scope,
                    'principal_type': assignment.principal_type
                })
            return assignments
        except Exception as e:
            print(f"Error getting role assignments: {e}")
            return []
