# Azure Configuration
AZURE_SUBSCRIPTION_ID=your_subscription_id_here
AZURE_TENANT_ID=your_tenant_id_here
AZURE_CLIENT_ID=your_client_id_here
AZURE_CLIENT_SECRET=your_client_secret_here

# Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
ADMIN_EMAIL=<EMAIL>

# Detection Configuration
LOG_LEVEL=INFO
ALERT_THRESHOLD_MINUTES=60
MAX_FAILED_LOGINS=5
UNUSUAL_LOCATION_THRESHOLD_KM=500

# GeoIP Database (optional - for location detection)
GEOIP_DB_PATH=./data/GeoLite2-City.mmdb
